repos:
- repo: https://github.com/ambv/black
  rev: 24.4.2
  hooks:
    - id: black
      name: Format Python Code
      language: python
      entry: black
      args:
        - --safe
        - --line-length=88
        - --target-version=py39
        - .

- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v4.6.0
  hooks:
    - id: requirements-txt-fixer
      name: Requirements file fixer
      language: python
      args:
        - requirements.txt
        - requirements-dev.txt

- repo: https://github.com/PyCQA/flake8
  rev: 7.0.0 
  hooks:
    - id: flake8
      name: Flake8 Check
      language: python
      entry: flake8
      args:
        - paperswithcode

- repo: https://github.com/pycqa/pydocstyle
  rev: 6.3.0
  hooks:
    - id: pydocstyle
      name: Python Documentation Style Check
      language: python
      entry: pydocstyle
      args:
        - paperswithcode

[{"task": "Taxonomy Learning", "description": "Taxonomy learning is the task of hierarchically classifying concepts in an automatic manner from text corpora. The process of building taxonomies is usually divided into two main steps: (1) extracting hypernyms for concepts, which may constitute a field of research in itself (see Hypernym Discovery below) and (2) refining the structure into a taxonomy.", "subtasks": [{"task": "Hypernym Discovery", "description": "Given a corpus and a target term (hyponym), the task of hypernym discovery consists of extracting a set of its most appropriate hypernyms from the corpus. For example, for the input word “dog”, some valid hypernyms would be “canine”, “mammal” or “animal”.", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "SemEval 2018", "description": "The SemEval-2018 hypernym discovery evaluation benchmark ([<PERSON><PERSON><PERSON><PERSON> et al. 2018](http://aclweb.org/anthology/S18-1115)), which can be freely downloaded [here](https://competitions.codalab.org/competitions/17119), contains three domains (general, medical and music) and is also available in Italian and Spanish (not in this repository). For each domain a target corpus and vocabulary (i.e. hypernym search space) are provided. The dataset contains both concepts (e.g. dog) and entities (e.g. Manchester United) up to trigrams. The following table lists the number of hyponym-hypernym pairs for each dataset: \n\n| Partition           | General | Medical | Music |\n| ------------- | :-----:|:-----:|:-----:|\n|Trial | 200 | 101 | 355 |\n|Training |  11779 | 3256 | 5455 |\n|Test | 7048 | 4116 | 5233 |\n\nThe results for each model and dataset (general, medical and music) are presented below (MFH stands for “Most Frequent Hypernyms” and is used as a baseline).\n\n**General:**\n\n\n\n**Medical domain:**\n\n\n\n**Music domain:**", "dataset_links": [{"title": "Camach<PERSON><PERSON> et al. 2018](http://aclweb.org/anthology/S18-1115)), which can be freely downloaded [here", "url": "http://aclweb.org/anthology/S18-1115)), which can be freely downloaded [here](https://competitions.codalab.org/competitions/17119), contains three domains (general, medical and music) and is also available in Italian and Spanish (not in this repository). For each domain a target corpus and vocabulary (i.e. hypernym search space) are provided. The dataset contains both concepts (e.g. dog) and entities (e.g. Manchester United"}], "subdatasets": [{"subdataset": "General", "sota": {"metrics": ["MAP", "MRR", "P@5"], "rows": [{"model_name": "CRIM", "metrics": {"MAP": "19.78", "MRR": "36.10", "P@5": "19.03"}, "paper_title": "A Hybrid Approach to Hypernym Discovery", "paper_url": "http://aclweb.org/anthology/S18-1116"}, {"model_name": "vTE", "metrics": {"MAP": "10.60", "MRR": "23.83", "P@5": "9.91"}, "paper_title": "Supervised Distributional Hypernym Discovery via Domain Adaptation", "paper_url": "https://aclweb.org/anthology/D16-1041"}, {"model_name": "NLP_HZ", "metrics": {"MAP": "9.37", "MRR": "17.29", "P@5": "9.19"}, "paper_title": "A Nearest Neighbor Approach", "paper_url": "http://aclweb.org/anthology/S18-1148"}, {"model_name": "300-sparsans", "metrics": {"MAP": "8.95", "MRR": "19.44", "P@5": "8.63"}, "paper_title": "Hypernymy as interaction of sparse attributes", "paper_url": "http://aclweb.org/anthology/S18-1152"}, {"model_name": "MFH", "metrics": {"MAP": "8.77", "MRR": "21.39", "P@5": "7.81"}}, {"model_name": "SJTU BCMI", "metrics": {"MAP": "5.77", "MRR": "10.56", "P@5": "5.96"}, "paper_title": "Neural Hypernym Discovery with Term Embeddings", "paper_url": "http://aclweb.org/anthology/S18-1147"}, {"model_name": "Apollo", "metrics": {"MAP": "2.68", "MRR": "6.01", "P@5": "2.69"}, "paper_title": "Detecting Hypernymy Relations Using Syntactic Dependencies", "paper_url": "http://aclweb.org/anthology/S18-1146"}, {"model_name": "balAPInc", "metrics": {"MAP": "1.36", "MRR": "3.18", "P@5": "1.30"}, "paper_title": "Hypernyms under Siege: Linguistically-motivated Artillery for Hypernymy Detection", "paper_url": "http://www.aclweb.org/anthology/E17-1007"}]}}, {"subdataset": "Medical domain", "sota": {"metrics": ["MAP", "MRR", "P@5"], "rows": [{"model_name": "CRIM", "metrics": {"MAP": "34.05", "MRR": "54.64", "P@5": "36.77"}, "paper_title": "A Hybrid Approach to Hypernym Discovery", "paper_url": "http://aclweb.org/anthology/S18-1116"}, {"model_name": "MFH", "metrics": {"MAP": "28.93", "MRR": "35.80", "P@5": "34.20"}}, {"model_name": "300-sparsans", "metrics": {"MAP": "20.75", "MRR": "40.60", "P@5": "21.43"}, "paper_title": "Hypernymy as interaction of sparse attributes", "paper_url": "http://aclweb.org/anthology/S18-1152"}, {"model_name": "vTE", "metrics": {"MAP": "18.84", "MRR": "41.07", "P@5": "20.71"}, "paper_title": "Supervised Distributional Hypernym Discovery via Domain Adaptation", "paper_url": "https://aclweb.org/anthology/D16-1041"}, {"model_name": "EXPR", "metrics": {"MAP": "13.77", "MRR": "40.76", "P@5": "12.76"}, "paper_title": "A Combined Approach for Hypernym Discovery", "paper_url": "http://aclweb.org/anthology/S18-1150"}, {"model_name": "SJTU BCMI", "metrics": {"MAP": "11.69", "MRR": "25.95", "P@5": "11.69"}, "paper_title": "Neural Hypernym Discovery with Term Embeddings", "paper_url": "http://aclweb.org/anthology/S18-1147"}, {"model_name": "ADAPT", "metrics": {"MAP": "8.13", "MRR": "20.56", "P@5": "8.32"}, "paper_title": "Skip-Gram Word Embeddings for Unsupervised Hypernym Discovery in Specialised Corpora", "paper_url": "http://aclweb.org/anthology/S18-1151"}, {"model_name": "balAPInc", "metrics": {"MAP": "0.91", "MRR": "2.10", "P@5": "1.08"}, "paper_title": "Hypernyms under Siege: Linguistically-motivated Artillery for Hypernymy Detection", "paper_url": "http://www.aclweb.org/anthology/E17-1007"}]}}, {"subdataset": "Music domain", "sota": {"metrics": ["MAP", "MRR", "P@5"], "rows": [{"model_name": "CRIM", "metrics": {"MAP": "40.97", "MRR": "60.93", "P@5": "41.31"}, "paper_title": "A Hybrid Approach to Hypernym Discovery", "paper_url": "http://aclweb.org/anthology/S18-1116"}, {"model_name": "MFH", "metrics": {"MAP": "33.32", "MRR": "51.48", "P@5": "35.76"}}, {"model_name": "300-sparsans", "metrics": {"MAP": "29.54", "MRR": "46.43", "P@5": "28.86"}, "paper_title": "Hypernymy as interaction of sparse attributes", "paper_url": "http://aclweb.org/anthology/S18-1152"}, {"model_name": "vTE", "metrics": {"MAP": "12.99", "MRR": "39.36", "P@5": "12.41"}, "paper_title": "Supervised Distributional Hypernym Discovery via Domain Adaptation", "paper_url": "https://aclweb.org/anthology/D16-1041"}, {"model_name": "SJTU BCMI", "metrics": {"MAP": "4.71", "MRR": "9.15", "P@5": "4.91"}, "paper_title": "Neural Hypernym Discovery with Term Embeddings", "paper_url": "http://aclweb.org/anthology/S18-1147"}, {"model_name": "ADAPT", "metrics": {"MAP": "2.63", "MRR": "7.46", "P@5": "2.64"}, "paper_title": "Skip-Gram Word Embeddings for Unsupervised Hypernym Discovery in Specialised Corpora", "paper_url": "http://aclweb.org/anthology/S18-1151"}, {"model_name": "balAPInc", "metrics": {"MAP": "1.95", "MRR": "5.01", "P@5": "2.15"}, "paper_title": "Hypernyms under Siege: Linguistically-motivated Artillery for Hypernymy Detection", "paper_url": "http://www.aclweb.org/anthology/E17-1007"}]}}]}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Natural language inference", "description": "Natural language inference is the task of determining whether a \"hypothesis\" is \ntrue (entailment), false (contradiction), or undetermined (neutral) given a \"premise\".\n\nExample:\n\n| Premise | Label | Hypothesis |\n| --- | ---| --- |\n| A man inspects the uniform of a figure in some East Asian country. | contradiction | The man is sleeping. |\n| An older and younger man smiling. | neutral  | Two men are smiling and laughing at the cats playing on the floor. |\n| A soccer game with multiple males playing. | entailment | Some men are playing a sport. |", "datasets": [{"dataset": "SNLI", "description": "The [Stanford Natural Language Inference (SNLI) Corpus](https://arxiv.org/abs/1508.05326)\ncontains around 550k hypothesis/premise pairs. Models are evaluated based on accuracy.\n\nState-of-the-art results can be seen on the [SNLI website](https://nlp.stanford.edu/projects/snli/).", "dataset_links": [{"title": "Stanford Natural Language Inference (SNLI) Corpus", "url": "SNLI) Corpus](https://arxiv.org/abs/1508.05326"}, {"title": "SNLI website", "url": "https://nlp.stanford.edu/projects/snli/"}]}, {"dataset": "MultiNLI", "description": "The [Multi-Genre Natural Language Inference (MultiNLI) corpus](https://arxiv.org/abs/1704.05426)\ncontains around 433k hypothesis/premise pairs. It is similar to the SNLI corpus, but\ncovers a range of genres of spoken and written text and supports cross-genre evaluation. The data\ncan be downloaded from the [MultiNLI](https://www.nyu.edu/projects/bowman/multinli/) website.\n\nPublic leaderboards for [in-genre (matched)](https://www.kaggle.com/c/multinli-matched-open-evaluation/leaderboard) \nand [cross-genre (mismatched)](https://www.kaggle.com/c/multinli-mismatched-open-evaluation/leaderboard)\nevaluation are available, but entries do not correspond to published models.", "dataset_links": [{"title": "Multi-Genre Natural Language Inference (MultiNLI) corpus", "url": "MultiNLI) corpus](https://arxiv.org/abs/1704.05426"}, {"title": "MultiNLI", "url": "https://www.nyu.edu/projects/bowman/multinli/"}, {"title": "in-genre (matched)", "url": "matched)](https://www.kaggle.com/c/multinli-matched-open-evaluation/leaderboard"}, {"title": "cross-genre (mismatched)", "url": "mismatched)](https://www.kaggle.com/c/multinli-mismatched-open-evaluation/leaderboard"}], "sota": {"metrics": ["Matched", "Mismatched"], "rows": [{"model_name": "Finetuned Transformer LM", "metrics": {"Matched": "82.1", "Mismatched": "81.4"}, "paper_title": "Improving Language Understanding by Generative Pre-Training", "paper_url": "https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/language-unsupervised/language_understanding_paper.pdf", "code_links": []}, {"model_name": "Multi-task BiLSTM + Attn", "metrics": {"Matched": "72.2", "Mismatched": "72.1"}, "paper_title": "GLUE: A Multi-Task Benchmark and Analysis Platform for Natural Language Understanding", "paper_url": "https://arxiv.org/abs/1804.07461", "code_links": []}, {"model_name": "GenSen", "metrics": {"Matched": "71.4", "Mismatched": "71.3"}, "paper_title": "Learning General Purpose Distributed Sentence Representations via Large Scale Multi-task Learning", "paper_url": "https://arxiv.org/abs/1804.00079", "code_links": []}]}}, {"dataset": "SciTail", "description": "The [SciTail](http://ai2-website.s3.amazonaws.com/publications/scitail-aaai-2018_cameraready.pdf)\nentailment dataset consists of 27k. In contrast to the SNLI and MultiNLI, it was not crowd-sourced\nbut created from sentences that already exist \"in the wild\". Hypotheses were created from\nscience questions and the corresponding answer candidates, while relevant web sentences from a large\ncorpus were used as premises. Models are evaluated based on accuracy.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "SciTail", "url": "http://ai2-website.s3.amazonaws.com/publications/scitail-aaai-2018_cameraready.pdf"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "Finetuned Transformer LM", "metrics": {"Accuracy": "88.3"}, "paper_title": "Improving Language Understanding by Generative Pre-Training", "paper_url": "https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/language-unsupervised/language_understanding_paper.pdf"}, {"model_name": "Hierarchical BiLSTM Max Pooling", "metrics": {"Accuracy": "86.0"}, "paper_title": "Natural Language Inference with Hierarchical BiLSTM Max Pooling", "paper_url": "https://arxiv.org/abs/1808.08762"}, {"model_name": "CAFE", "metrics": {"Accuracy": "83.3"}, "paper_title": "A Compare-Propagate Architecture with Alignment Factorization for Natural Language Inference", "paper_url": "https://arxiv.org/abs/1801.00102"}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Semantic parsing", "description": "", "subtasks": [{"task": "AMR parsing", "description": "Each AMR is a single rooted, directed graph. AMRs include PropBank semantic roles, within-sentence coreference, named entities and types, modality, negation, questions, quantities, and so on. [See](https://amr.isi.edu/index.html).", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "LDC2014T12:", "description": "13,051 sentences\n\nModels are evaluated on the newswire section and the full dataset based on [smatch](https://amr.isi.edu/smatch-13.pdf).\nSystems marked with \\* are pipeline systems that require other systems (i.e. a dependency parse or a SRL parse) as input.\n\nMT-Based (<PERSON><PERSON> et al., 2015)* | -- | 0.66 | [Parsing English into Abstract Meaning Representation Using Syntax-Based Machine Translation ](http://www.aclweb.org/anthology/D15-1136)\n| Transition-based parser-Stack-LSTM (Ballesteros and Al-Onaizan, 2017)* | 0.69 | 0.64  | [AMR Parsing using Stack-LSTMs](http://www.aclweb.org/anthology/D17-1130) |\n| Transition-based parser-Stack-LSTM (Ballesteros and Al-Onaizan, 2017) | 0.68 | 0.63  | [AMR Parsing using Stack-LSTMs](http://www.aclweb.org/anthology/D17-1130) |", "dataset_links": [{"title": "smatch", "url": "https://amr.isi.edu/smatch-13.pdf"}, {"title": "Parsing English into Abstract Meaning Representation Using Syntax-Based Machine Translation", "url": "http://www.aclweb.org/anthology/D15-1136"}, {"title": "AMR Parsing using Stack-LSTMs", "url": "http://www.aclweb.org/anthology/D17-1130"}, {"title": "AMR Parsing using Stack-LSTMs", "url": "http://www.aclweb.org/anthology/D17-1130"}], "sota": {"metrics": ["F1 Newswire", "F1 Full"], "rows": [{"model_name": "Incremental joint model", "metrics": {"F1 Newswire": "0.71", "F1 Full": "0.66"}, "paper_title": "AMR Parsing with an Incremental Joint Model", "paper_url": "https://aclweb.org/anthology/D16-1065"}, {"model_name": "Transition-based transducer", "metrics": {"F1 Newswire": "0.70", "F1 Full": "0.66"}, "paper_title": "Boosting Transition-based AMR Parsing with Refined Actions and Auxiliary Analyzers", "paper_url": "http://www.aclweb.org/anthology/P15-2141"}, {"model_name": "Imitation learning ", "metrics": {"F1 Newswire": "0.70", "F1 Full": "--"}, "paper_title": "Noise reduction and targeted exploration in imitation learning for Abstract Meaning Representation parsing", "paper_url": "http://www.aclweb.org/anthology/P16-1001"}]}}, {"dataset": "LDC2015E86:", "description": "19,572 sentences\n\nModels are evaluated based on [smatch](https://amr.isi.edu/smatch-13.pdf).\n\nJoint model (<PERSON><PERSON> and <PERSON><PERSON>, 2018) | 73.7 | [AMR Parsing as Graph Prediction with Latent Alignment](https://arxiv.org/abs/1805.05286) |\nMul-BiLSTM (Foland and Martin, 2017) | 70.7 | [Abstract Meaning Representation Parsing using LSTM Recurrent Neural Networks](http://aclweb.org/anthology/P17-1043) |\nJAMR (<PERSON><PERSON><PERSON> et al., 2016) | 67.0 | [CMU at SemEval-2016 Task 8: Graph-based AMR Parsing with Infinite Ramp Loss](http://www.aclweb.org/anthology/S16-1186) |\nCAMR (<PERSON> et al., 2016) | 66.5 | [CAMR at SemEval-2016 Task 8: An Extended Transition-based AMR Parser](http://www.aclweb.org/anthology/S16-1181) |\nAMREager (<PERSON><PERSON> et al., 2017) | 64.0 | [An Incremental Parser for Abstract Meaning Representation](http://www.aclweb.org/anthology/E17-1051) |\nSEQ2SEQ + 20M (<PERSON><PERSON> et al., 2017) | 62.1 | [Neural AMR: Sequence-to-Sequence Models for Parsing and Generation](https://arxiv.org/abs/1704.08381) |", "dataset_links": [{"title": "smatch", "url": "https://amr.isi.edu/smatch-13.pdf"}, {"title": "AMR Parsing as Graph Prediction with Latent Alignment", "url": "https://arxiv.org/abs/1805.05286"}, {"title": "Abstract Meaning Representation Parsing using LSTM Recurrent Neural Networks", "url": "http://aclweb.org/anthology/P17-1043"}, {"title": "CMU at SemEval-2016 Task 8: Graph-based AMR Parsing with Infinite Ramp Loss", "url": "http://www.aclweb.org/anthology/S16-1186"}, {"title": "CAMR at SemEval-2016 Task 8: An Extended Transition-based AMR Parser", "url": "http://www.aclweb.org/anthology/S16-1181"}, {"title": "An Incremental Parser for Abstract Meaning Representation", "url": "http://www.aclweb.org/anthology/E17-1051"}, {"title": "Neural AMR: Sequence-to-Sequence Models for Parsing and Generation", "url": "https://arxiv.org/abs/1704.08381"}], "sota": {"metrics": ["Smatch"], "rows": []}}, {"dataset": "LDC2016E25", "description": "39,260 sentences\n\nResults are computed over 8 runs. Models are evaluated based on [smatch](https://amr.isi.edu/smatch-13.pdf).\n\nJoint model (<PERSON><PERSON> and <PERSON><PERSON>, 2018) | 74.4 | [AMR Parsing as Graph Prediction with Latent Alignment](https://arxiv.org/abs/1805.05286) |\nChSeq + 100K (van <PERSON> and <PERSON>, 2017) | 71.0 | [Neural Semantic Parsing by Character-based Translation: Experiments with Abstract Meaning Representations](https://arxiv.org/abs/1705.09980) |\nNeural-Pointer (Buys and Blunsom, 2017) | 61.9 | [Oxford at SemEval-2017 Task 9: Neural AMR Parsing with Pointer-Augmented Attention](http://aclweb.org/anthology/S17-2157) |", "dataset_links": [{"title": "smatch", "url": "https://amr.isi.edu/smatch-13.pdf"}, {"title": "AMR Parsing as Graph Prediction with Latent Alignment", "url": "https://arxiv.org/abs/1805.05286"}, {"title": "Neural Semantic Parsing by Character-based Translation: Experiments with Abstract Meaning Representations", "url": "https://arxiv.org/abs/1705.09980"}, {"title": "Oxford at SemEval-2017 Task 9: Neural AMR Parsing with <PERSON><PERSON>-Augmented Attention", "url": "http://aclweb.org/anthology/S17-2157"}], "sota": {"metrics": ["Smatch"], "rows": []}}]}, {"task": "SQL parsing", "description": "", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "ATIS", "description": "5,280 user questions for a flight-booking task:\n\n- Collected and manually annotated with SQL [<PERSON><PERSON> et al., (1994)](http://dl.acm.org/citation.cfm?id=1075823)\n- Modified by [<PERSON><PERSON> et al., (2017)](http://www.aclweb.org/anthology/P17-1089) to reduce nesting\n- Bugfixes and changes to a canonical style by [<PERSON><PERSON> et al., (2018)](http://arxiv.org/abs/1806.09029)\n\n\nExample:\n\n| Question | SQL query | \n| ------------- |  --- |\n| what flights from any city land at MKE | `SELECT DISTINCT FLIGHTalias0.FLIGHT_ID FROM AIRPORT AS AIRPORTalias0 , AIRPORT_SERVICE AS AIRPORT_SERVICEalias0 , CITY AS CITYalias0 , FLIGHT AS FLIGHTalias0 WHERE AIRPORTalias0.AIRPORT_CODE = \"MKE\" AND CITYalias0.CITY_CODE = AIRPORT_SERVICEalias0.CITY_CODE AND FLIGHTalias0.FROM_AIRPORT = AIRPORT_SERVICEalias0.AIRPORT_CODE AND FLIGHTalias0.TO_AIRPORT = AIRPORTalias0.AIRPORT_CODE ;` |", "dataset_links": [{"title": "<PERSON><PERSON> et al., (1994)", "url": "1994)](http://dl.acm.org/citation.cfm?id=1075823"}, {"title": "<PERSON><PERSON> et al., (2017)", "url": "2017)](http://www.aclweb.org/anthology/P17-1089"}, {"title": "<PERSON><PERSON><PERSON> et al., (2018)", "url": "2018)](http://arxiv.org/abs/1806.09029"}], "sota": {"metrics": ["Question Split", "Query Split"], "rows": [{"model_name": "Seq2Seq with copying", "metrics": {"Question Split": "51", "Query Split": "32"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}, {"model_name": "<PERSON><PERSON> et al.,", "metrics": {"Question Split": "45", "Query Split": "17"}, "paper_title": "Learning a neural semantic parser from user feedback", "paper_url": "http://www.aclweb.org/anthology/P17-1089", "code_links": [{"title": "System", "url": "https://github.com/sriniiyer/nl2sql"}]}, {"model_name": "Template Baseline", "metrics": {"Question Split": "45", "Query Split": "0"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}]}}, {"dataset": "Advising", "description": "4,570 user questions about university course advising, with manually annotated SQL [<PERSON><PERSON> et al., (2018)](http://arxiv.org/abs/1806.09029).\n\nExample:\n\n| Question | SQL query | \n| ------------- |  --- |\n| Can undergrads take 550 ? | `SELECT DISTINCT COURSEalias0.ADVISORY_REQUIREMENT , COURSEalias0.ENFORCED_REQUIREMENT , COURSEalias0.NAME FROM COURSE AS COURSEalias0 WHERE COURSEalias0.DEPARTMENT = \\\"department0\\\" AND COURSEalias0.NUMBER = 550 ;` |", "dataset_links": [{"title": "<PERSON><PERSON><PERSON> et al., (2018)", "url": "2018)](http://arxiv.org/abs/1806.09029"}], "sota": {"metrics": ["Question Split", "Query Split"], "rows": [{"model_name": "Template Baseline", "metrics": {"Question Split": "80", "Query Split": "0"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}, {"model_name": "Seq2Seq with copying", "metrics": {"Question Split": "70", "Query Split": "0"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}, {"model_name": "<PERSON><PERSON> et al.,", "metrics": {"Question Split": "41", "Query Split": "1"}, "paper_title": "Learning a neural semantic parser from user feedback", "paper_url": "http://www.aclweb.org/anthology/P17-1089", "code_links": [{"title": "System", "url": "https://github.com/sriniiyer/nl2sql"}]}]}}, {"dataset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "877 user questions about US geography:\n\n- Collected and manually annotated with Prolog [<PERSON><PERSON> and <PERSON><PERSON> (1996)](http://dl.acm.org/citation.cfm?id=1864519.1864543)\n- Most questions were converted to SQL by [<PERSON><PERSON><PERSON> et al., (2003)](http://doi.acm.org/10.1145/604045.604070)\n- Remaining question converted to SQL by [<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2012)](https://doi.org/10.1007/978-3-642-45260-4_5), and independently by [<PERSON><PERSON> et al., (2017)](http://www.aclweb.org/anthology/P17-1089)\n- Bugfixes and changes to a canonical style by [<PERSON><PERSON> et al., (2018)](http://arxiv.org/abs/1806.09029)\n\nExample:\n\n| Question | SQL query | \n| ------------- |  --- |\n| what is the biggest city in arizona | `SELECT CITYalias0.CITY_NAME FROM CITY AS CITYalias0 WHERE CITYalias0.POPULATION = ( SELECT MAX( CITYalias1.POPULATION ) FROM CITY AS CITYalias1 WHERE CITYalias1.STATE_NAME = \"arizona\" ) AND CITYalias0.STATE_NAME = \"arizona\"` |", "dataset_links": [{"title": "<PERSON><PERSON> and <PERSON><PERSON> (1996)", "url": "1996)](http://dl.acm.org/citation.cfm?id=1864519.1864543"}, {"title": "<PERSON><PERSON><PERSON> et al., (2003)", "url": "2003)](http://doi.acm.org/10.1145/604045.604070"}, {"title": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (2012)](https://doi.org/10.1007/978-3-642-45260-4_5), and independently by [<PERSON><PERSON> et al., (2017)", "url": "2012)](https://doi.org/10.1007/978-3-642-45260-4_5), and independently by [<PERSON><PERSON> et al., (2017)](http://www.aclweb.org/anthology/P17-1089"}, {"title": "<PERSON><PERSON><PERSON> et al., (2018)", "url": "2018)](http://arxiv.org/abs/1806.09029"}], "sota": {"metrics": ["Question Split", "Query Split"], "rows": [{"model_name": "Seq2Seq with copying", "metrics": {"Question Split": "71", "Query Split": "20"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}, {"model_name": "<PERSON><PERSON> et al.,", "metrics": {"Question Split": "66", "Query Split": "40"}, "paper_title": "Learning a neural semantic parser from user feedback", "paper_url": "http://www.aclweb.org/anthology/P17-1089", "code_links": [{"title": "System", "url": "https://github.com/sriniiyer/nl2sql"}]}, {"model_name": "Template Baseline", "metrics": {"Question Split": "66", "Query Split": "0"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}]}}, {"dataset": "Scholar", "description": "817 user questions about academic publications, with automatically generated SQL that was checked by asking the user if the output was correct.\n\n- Collected by [<PERSON><PERSON> et al., (2017)](http://www.aclweb.org/anthology/P17-1089)\n- Bugfixes and changes to a canonical style by [<PERSON><PERSON> et al., (2018)](http://arxiv.org/abs/1806.09029)\n\nExample:\n\n| Question | SQL query | \n| ------------- |  --- |\n| What papers has sharon goldwater written ? | `SELECT DISTINCT WRITESalias0.PAPERID FROM AUTHOR AS AUTHORalias0 , WRITES AS WRITESalias0 WHERE AUTHORalias0.AUTHORNAME = \"sharon goldwater\" AND WRITESalias0.AUTHORID = AUTHORalias0.AUTHORID ;` |", "dataset_links": [{"title": "<PERSON><PERSON> et al., (2017)", "url": "2017)](http://www.aclweb.org/anthology/P17-1089"}, {"title": "<PERSON><PERSON><PERSON> et al., (2018)", "url": "2018)](http://arxiv.org/abs/1806.09029"}], "sota": {"metrics": ["Question Split", "Query Split"], "rows": [{"model_name": "Seq2Seq with copying", "metrics": {"Question Split": "59", "Query Split": "5"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}, {"model_name": "Template Baseline", "metrics": {"Question Split": "52", "Query Split": "0"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}, {"model_name": "<PERSON><PERSON> et al.,", "metrics": {"Question Split": "44", "Query Split": "3"}, "paper_title": "Learning a neural semantic parser from user feedback", "paper_url": "http://www.aclweb.org/anthology/P17-1089", "code_links": [{"title": "System", "url": "https://github.com/sriniiyer/nl2sql"}]}]}}, {"dataset": "Spider", "description": "Spider is a large-scale complex and cross-domain semantic parsing and text-to-SQL \ndataset. It consists of 10,181 questions and 5,693 unique complex SQL queries on \n200 databases with multiple tables covering 138 different domains. In Spider 1.0, \ndifferent complex SQL queries and databases appear in train and test sets. \n\nThe Spider dataset can be accessed and leaderboard can be accessed [here](https://yale-lily.github.io/spider).", "dataset_links": [{"title": "here", "url": "https://yale-lily.github.io/spider"}]}, {"dataset": "WikiSQL", "description": "The [WikiSQL dataset](https://arxiv.org/abs/1709.00103) consists of 87,673 \nexamples of questions, SQL queries, and database tables built from 26,521 tables.\nTrain/dev/test splits are provided so that each table is only in one split.\nModels are evaluated based on accuracy on execute result matches.\n\nExample:\n\n| Question | SQL query | \n| ------------- |  --- |\n| How many engine types did <PERSON> use? | `SELECT COUNT Engine WHERE Driver = <PERSON>` |", "dataset_links": [{"title": "WikiSQL dataset", "url": "https://arxiv.org/abs/1709.00103"}], "sota": {"metrics": ["Acc ex"], "rows": [{"model_name": "TypeSQL+TC", "metrics": {"Acc ex": "82.6"}, "paper_title": "TypeSQL: Knowledge-based Type-Aware Neural Text-to-SQL Generation", "paper_url": "https://arxiv.org/abs/1804.09769"}, {"model_name": "SQLNet", "metrics": {"Acc ex": "68.0"}, "paper_title": "Sqlnet: Generating structured queries from natural language without reinforcement learning", "paper_url": "https://arxiv.org/abs/1711.04436"}, {"model_name": "Seq2SQL", "metrics": {"Acc ex": "59.4"}, "paper_title": "Seq2sql: Generating structured queries from natural language using reinforcement learning", "paper_url": "https://arxiv.org/abs/1709.00103"}]}}, {"dataset": "Smaller Datasets", "description": "Restaurants - 378 questions about restaurants, their cuisine and locations, collected by [<PERSON> and <PERSON><PERSON> (2000)](http://www.aclweb.org/anthology/W/W00/W00-1317.pdf), converted to SQL by [<PERSON><PERSON><PERSON> et al., (2003)]((http://doi.acm.org/10.1145/604045.604070) and [<PERSON><PERSON><PERSON><PERSON> and <PERSON> (2012)](https://doi.org/10.1007/978-3-642-45260-4_5), improved and converted to canonical style by [<PERSON><PERSON><PERSON> et al., (2018)](http://arxiv.org/abs/1806.09029)\n\nExample:\n\n| Question | SQL query | \n| ------------- |  --- |\n| where is a restaurant in alameda ? | `SELECT LOCATIONalias0.HOUSE_NUMBER , RESTAURANTalias0.NAME FROM LOCATION AS LOCATIONalias0 , RESTAURANT AS RESTAURANTalias0 WHERE LOCATIONalias0.CITY_NAME = \"alameda\" AND RESTAURANTalias0.ID = LOCATIONalias0.RESTAURANT_ID ;` |\n\n\nAcademic - 196 questions about publications generated by enumerating all of the different queries possible with the Microsoft Academic Search interface, then writing questions for each query [Li and Jagadish (2014)](http://dx.doi.org/10.14778/2735461.2735468). Improved and converted to a cononical style by [Finegan-Dollak et al., (2018)](http://arxiv.org/abs/1806.09029).\n\nExample:\n\n| Question | SQL query | \n| ------------- |  --- |\n| return me the homepage of PVLDB | `SELECT JOURNALalias0.HOMEPAGE FROM JOURNAL AS JOURNALalias0 WHERE JOURNALalias0.NAME = \"PVLDB\" ;` |\n\n\nYelp - 128 user questions about the Yelp website [Yaghmazadeh et al., 2017](http://doi.org/10.1145/3133887). Improved and converted to a cononical style by [Finegan-Dollak et al., (2018)](http://arxiv.org/abs/1806.09029).\n\nExample:\n\n| Question | SQL query | \n| ------------- |  --- |\n| List all businesses with rating 3.5 | `SELECT BUSINESSalias0.NAME FROM BUSINESS AS BUSINESSalias0 WHERE BUSINESSalias0.RATING = 3.5 ;` |\n\n\nIMDB - 131 user questions about the Internet Movie Database [Yaghmazadeh et al., 2017](http://doi.org/10.1145/3133887). Improved and converted to a cononical style by [Finegan-Dollak et al., (2018)](http://arxiv.org/abs/1806.09029).\n\nExample:\n\n| Question | SQL query | \n| ------------- |  --- |\n| What year was the movie \" The Imitation Game \" produced | `SELECT MOVIEalias0.RELEASE_YEAR FROM MOVIE AS MOVIEalias0 WHERE MOVIEalias0.TITLE = \"The Imitation Game\" ;` |\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "<PERSON> and <PERSON><PERSON> (2000)](http://www.aclweb.org/anthology/W/W00/W00-1317.pdf), converted to SQL by [<PERSON><PERSON><PERSON> et al., (2003)]((http://doi.acm.org/10.1145/604045.604070) and [<PERSON><PERSON><PERSON><PERSON> and <PERSON> (2012)](https://doi.org/10.1007/978-3-642-45260-4_5), improved and converted to canonical style by [<PERSON><PERSON> et al., (2018)", "url": "2000)](http://www.aclweb.org/anthology/W/W00/W00-1317.pdf), converted to SQL by [<PERSON><PERSON><PERSON> et al., (2003)]((http://doi.acm.org/10.1145/604045.604070) and [<PERSON><PERSON><PERSON><PERSON> and <PERSON> (2012)](https://doi.org/10.1007/978-3-642-45260-4_5), improved and converted to canonical style by [<PERSON><PERSON> et al., (2018)](http://arxiv.org/abs/1806.09029"}, {"title": "<PERSON> and <PERSON><PERSON> (2014)](http://dx.doi.org/10.14778/2735461.2735468). Improved and converted to a cononical style by [<PERSON><PERSON> et al., (2018)", "url": "2014)](http://dx.doi.org/10.14778/2735461.2735468). Improved and converted to a cononical style by [<PERSON><PERSON> et al., (2018)](http://arxiv.org/abs/1806.09029"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2017](http://doi.org/10.1145/3133887). Improved and converted to a cononical style by [<PERSON><PERSON> et al., (2018)", "url": "http://doi.org/10.1145/3133887). Improved and converted to a cononical style by [<PERSON><PERSON><PERSON> et al., (2018)](http://arxiv.org/abs/1806.09029"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2017](http://doi.org/10.1145/3133887). Improved and converted to a cononical style by [<PERSON><PERSON> et al., (2018)", "url": "http://doi.org/10.1145/3133887). Improved and converted to a cononical style by [<PERSON><PERSON><PERSON> et al., (2018)](http://arxiv.org/abs/1806.09029"}, {"title": "Go back to the README", "url": "../README.md"}], "subdatasets": [{"subdataset": "Example", "sota": {"metrics": ["Question Split", "Query Split"], "rows": [{"model_name": "<PERSON><PERSON> et al.,", "metrics": {"Question Split": "100", "Query Split": "8"}, "paper_title": "Learning a neural semantic parser from user feedback", "paper_url": "http://www.aclweb.org/anthology/P17-1089", "code_links": [{"title": "System", "url": "https://github.com/sriniiyer/nl2sql"}]}, {"model_name": "Seq2Seq with copying", "metrics": {"Question Split": "100", "Query Split": "4"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}, {"model_name": "Template Baseline", "metrics": {"Question Split": "95", "Query Split": "0"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}]}}, {"subdataset": "Example", "sota": {"metrics": ["Question Split", "Query Split"], "rows": [{"model_name": "Seq2Seq with copying", "metrics": {"Question Split": "81", "Query Split": "74"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}, {"model_name": "<PERSON><PERSON> et al.,", "metrics": {"Question Split": "76", "Query Split": "70"}, "paper_title": "Learning a neural semantic parser from user feedback", "paper_url": "http://www.aclweb.org/anthology/P17-1089", "code_links": [{"title": "System", "url": "https://github.com/sriniiyer/nl2sql"}]}, {"model_name": "Template Baseline", "metrics": {"Question Split": "0", "Query Split": "0"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}]}}, {"subdataset": "Example", "sota": {"metrics": ["Question Split", "Query Split"], "rows": [{"model_name": "Seq2Seq with copying", "metrics": {"Question Split": "12", "Query Split": "4"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}, {"model_name": "<PERSON><PERSON> et al.,", "metrics": {"Question Split": "6", "Query Split": "6"}, "paper_title": "Learning a neural semantic parser from user feedback", "paper_url": "http://www.aclweb.org/anthology/P17-1089", "code_links": [{"title": "System", "url": "https://github.com/sriniiyer/nl2sql"}]}, {"model_name": "Template Baseline", "metrics": {"Question Split": "1", "Query Split": "0"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}]}}, {"subdataset": "Example", "sota": {"metrics": ["Question Split", "Query Split"], "rows": [{"model_name": "Seq2Seq with copying", "metrics": {"Question Split": "26", "Query Split": "9"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}, {"model_name": "<PERSON><PERSON> et al.,", "metrics": {"Question Split": "10", "Query Split": "4"}, "paper_title": "Learning a neural semantic parser from user feedback", "paper_url": "http://www.aclweb.org/anthology/P17-1089", "code_links": [{"title": "System", "url": "https://github.com/sriniiyer/nl2sql"}]}, {"model_name": "Template Baseline", "metrics": {"Question Split": "0", "Query Split": "0"}, "paper_title": "Improving Text-to-SQL Evaluation Methodology", "paper_url": "http://arxiv.org/abs/1806.09029", "code_links": [{"title": "Data and System", "url": "https://github.com/jkkummerfeld/text2sql-data"}]}]}}]}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Information Extraction", "description": "", "subtasks": [{"task": "Open Knowledge Graph Canonicalization", "description": "Open Information Extraction approaches leads to creation of large Knowledge bases (KB) from the web. The problem with such methods is that their entities and relations are not canonicalized, which leads to storage of redundant and ambiguous facts. For example, an Open KB storing *\\<<PERSON>, was born in, Honolulu\\>* and *\\<<PERSON>, took birth in, Honolulu\\>* doesn't know that *<PERSON>* and *<PERSON>* mean the same entity. Similarly, *took birth in* and *was born in* also refer to the same relation. Problem of Open KB canonicalization involves identifying groups of equivalent entities and relations in the KB.", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Datasets", "description": "| Datasets                                 | # Gold Entities | #NPs  | #Relations | #Triples |\n| ---------------------------------------- | :-------------: | ----- | ---------- | -------- |\n| [Base](https://suchanek.name/work/publications/cikm2014.pdf) |       150       | 290   | 3K         | 9K       |\n| [Ambiguous](https://suchanek.name/work/publications/cikm2014.pdf) |       446       | 717   | 11K        | 37K      |\n| [ReVerb45K](https://github.com/malllabiisc/cesi) |      7.5K       | 15.5K | 22K        | 45K      |", "dataset_links": [{"title": "Base", "url": "https://suchanek.name/work/publications/cikm2014.pdf"}, {"title": "Ambiguous", "url": "https://suchanek.name/work/publications/cikm2014.pdf"}, {"title": "ReVerb45K", "url": "https://github.com/malllabiisc/cesi"}]}, {"dataset": "Noun Phrase Canonicalization", "description": "[Go back to the README](../README.md)", "dataset_links": [{"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Base Dataset", "Ambiguous dataset", "ReVerb45k"], "rows": [{"model_name": "", "metrics": {"Base Dataset": "**Precision**", "Ambiguous dataset": "**Recall**", "ReVerb45k": "**F1**"}}, {"model_name": "CESI", "metrics": {"Base Dataset": "98.2", "Ambiguous dataset": "99.8", "ReVerb45k": "99.9"}}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2014", "metrics": {"Base Dataset": "94.8", "Ambiguous dataset": "97.9", "ReVerb45k": "98.3"}}]}}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "CCG supertagging", "description": "Combinatory Categorical Grammar (CCG; [<PERSON><PERSON><PERSON>, 2000](http://www.citeulike.org/group/14833/article/8971002)) is a\nhighly lexicalized formalism. The standard parsing model of [<PERSON> and <PERSON> (2007)](https://www.mitpressjournals.org/doi/abs/10.1162/coli.2007.33.4.493)\nuses over 400 lexical categories (or _supertags_), compared to about 50 part-of-speech tags for typical parsers.\n\nExample:\n\n| Vinken | , | 61 | years | old |\n| --- | ---| --- | --- | --- |\n| N| , | N/N | N | (S[adj]\\ NP)\\ NP |", "datasets": [{"dataset": "CCGBank", "description": "The CCGBank is a corpus of CCG derivations and dependency structures extracted from the Penn Treebank by\n[<PERSON><PERSON><PERSON>ma<PERSON> and Steedman (2007)](http://www.aclweb.org/anthology/J07-3004). Sections 2-21 are used for training,\nsection 00 for development, and section 23 as in-domain test set.\nPerformance is only calculated on the 425 most frequent labels. Models are evaluated based on accuracy.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> and Steedman (2007)", "url": "2007)](http://www.aclweb.org/anthology/J07-3004"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "<PERSON> et al.", "metrics": {"Accuracy": "96.1"}, "paper_title": "Semi-Supervised Sequence Modeling with Cross-View Training", "paper_url": "https://arxiv.org/abs/1809.08370"}, {"model_name": "<PERSON> et al.", "metrics": {"Accuracy": "94.7"}, "paper_title": "LSTM CCG Parsing", "paper_url": "https://aclweb.org/anthology/N/N16/N16-1026.pdf"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON> et al.", "metrics": {"Accuracy": "94.24"}, "paper_title": "Supertagging with LSTMs", "paper_url": "https://aclweb.org/anthology/N/N16/N16-1027.pdf"}, {"model_name": "Low supervision", "metrics": {"Accuracy": "93.26"}, "paper_title": "Deep multi-task learning with low level tasks supervised at lower layers", "paper_url": "http://anthology.aclweb.org/P16-2038"}, {"model_name": "<PERSON> et al.", "metrics": {"Accuracy": "93.00"}, "paper_title": "CCG Supertagging with a Recurrent Neural Network", "paper_url": "http://www.aclweb.org/anthology/P15-2041"}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Lexical Normalization", "description": "Lexical normalization is the task of translating/transforming a non standard text to a standard register.\n\nExample:\n\n```\nnew pix comming tomoroe\nnew pictures coming tomorrow\n```\n\nDatasets usually consists of tweets, since these naturally contain a fair amount of \nthese phenomena.\n\nFor lexical normalization, only replacements on the word-level are annotated.\nSome corpora include annotation for 1-N and N-1 replacements. However, word\ninsertion/deletion and reordering is not part of the task.", "datasets": [{"dataset": "LexNorm", "description": "The [LexNorm](http://people.eng.unimelb.edu.au/tbaldwin/etc/lexnorm_v1.2.tgz) corpus was originally introduced by [<PERSON> and <PERSON> (2011)](http://aclweb.org/anthology/P/P11/P11-1038.pdf).\nSeveral mistakes in annotation were resolved by [<PERSON> and <PERSON>](http://www.aclweb.org/anthology/D13-1007);\non this page, we only report results on the new dataset. For this dataset, the 2,577\ntweets from [<PERSON> and <PERSON>(2014)](http://www.aclweb.org/anthology/P14-3012) is often\nused as training data, because of its similar annotation style.\n\nThis dataset is commonly evaluated with accuracy on the non-standard words. This\nmeans that the system knows in advance which words are in need of normalization.\n\n\n\\* used a slightly different version of the data", "dataset_links": [{"title": "LexNorm](http://people.eng.unimelb.edu.au/tbaldwin/etc/lexnorm_v1.2.tgz) corpus was originally introduced by [<PERSON> and <PERSON> (2011)", "url": "http://people.eng.unimelb.edu.au/tbaldwin/etc/lexnorm_v1.2.tgz) corpus was originally introduced by [<PERSON> and <PERSON> (2011)](http://aclweb.org/anthology/P/P11/P11-1038.pdf"}, {"title": "<PERSON> and <PERSON><PERSON><PERSON>", "url": "http://www.aclweb.org/anthology/D13-1007"}, {"title": "<PERSON> and <PERSON>(2014)", "url": "2014)](http://www.aclweb.org/anthology/P14-3012"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "<PERSON><PERSON><PERSON>", "metrics": {"Accuracy": "87.63"}, "paper_title": "MoNoise: Modeling Noise Using a Modular Normalization System", "paper_url": "http://www.let.rug.nl/rob/doc/clin27.paper.pdf", "code_links": [{"title": "Official", "url": "https://bitbucket.org/robvanderg/monoise/"}]}, {"model_name": "Joint POS + Norm in a Viterbi decoding", "metrics": {"Accuracy": "87.58*"}, "paper_title": "Joint POS Tagging and Text Normalization for Informal Text", "paper_url": "http://www.aaai.org/ocs/index.php/IJCAI/IJCAI15/paper/download/10839/10838", "code_links": []}, {"model_name": "Syllable based", "metrics": {"Accuracy": "86.08"}, "paper_title": "Tweet Normalization with Syllables", "paper_url": "http://www.aclweb.org/anthology/P15-1089", "code_links": []}, {"model_name": "unLOL", "metrics": {"Accuracy": "82.06"}, "paper_title": "A Log-Linear Model for Unsupervised Text Normalization", "paper_url": "http://www.aclweb.org/anthology/D13-1007", "code_links": []}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Dialogue", "description": "Dialogue is notoriously hard to evaluate. Past approaches have used human evaluation.", "subtasks": [{"task": "Dialogue act classification", "description": "Dialogue act classification is the task of classifying an utterance with respect to the fuction it serves in a dialogue, i.e. the act the speaker is performing. Dialogue acts are a type of speech acts (for Speech Act Theory, see [Austin (1975)](http://www.hup.harvard.edu/catalog.php?isbn=9780674411524) and [<PERSON><PERSON> (1969)](https://www.cambridge.org/core/books/speech-acts/D2D7B03E472C8A390ED60B86E08640E7)).", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Switchboard corpus", "description": "The [Switchboard-1 corpus](https://catalog.ldc.upenn.edu/ldc97s62) is a telephone speech corpus, consisting of about 2,400 two-sided telephone conversation among 543 speakers with about 70 provided conversation topics. The dataset includes the audio files and the transcription files, as well as information about the speakers and the calls.\n\nThe Switchboard Dialogue Act Corpus (SwDA) [[download](https://web.stanford.edu/~jurafsky/swb1_dialogact_annot.tar.gz)] extends the Switchboard-1 corpus with tags from the [SWBD-DAMSL tagset](https://web.stanford.edu/~jurafsky/ws97/manual.august1.html), which is an augmentation to the Discourse Annotation and Markup System of Labeling (DAMSL) tagset. The 220 tags were reduced to 42 tags by clustering in order to improve the language model on the Switchboard corpus. A subset of the Switchboard-1 corpus consisting of 1155 conversations was used. The resulting tags include dialogue acts like statement-non-opinion, acknowledge, statement-opinion, agree/accept, etc.  \nAnnotated example:  \n*Speaker:* A, *Dialogue Act:* Yes-No-Question, *Utterance:* So do you go to college right now?", "dataset_links": [{"title": "Switchboard-1 corpus", "url": "https://catalog.ldc.upenn.edu/ldc97s62"}, {"title": "[download](https://web.stanford.edu/~j<PERSON><PERSON><PERSON>/swb1_dialogact_annot.tar.gz)] extends the Switchboard-1 corpus with tags from the [SWBD-DAMSL tagset", "url": "https://web.stanford.edu/~jurafsky/swb1_dialogact_annot.tar.gz)] extends the Switchboard-1 corpus with tags from the [SWBD-DAMSL tagset](https://web.stanford.edu/~jurafsky/ws97/manual.august1.html), which is an augmentation to the Discourse Annotation and Markup System of Labeling (DAMSL"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "CRF-ASN", "metrics": {"Accuracy": "81.3"}, "paper_title": "Dialogue Act Recognition via CRF-Attentive Structured Network", "paper_url": "https://arxiv.org/abs/1711.05568", "code_links": []}, {"model_name": "Bi-LSTM-CRF", "metrics": {"Accuracy": "79.2"}, "paper_title": "Dialogue Act Sequence Labeling using Hierarchical encoder with CRF", "paper_url": "https://arxiv.org/abs/1709.04250", "code_links": [{"title": "Link", "url": "https://github.com/YanWenqiang/HBLSTM-CRF"}]}, {"model_name": "RNN with 3 utterances in context", "metrics": {"Accuracy": "77.34"}, "paper_title": "A Context-based Approach for Dialogue Act Recognition using Simple Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1805.06280", "code_links": []}]}}, {"dataset": "ICSI Meeting Recorder Dialog Act (MRDA) corpus", "description": "The [MRDA corpus](http://www1.icsi.berkeley.edu/Speech/mr/) [[download](http://www.icsi.berkeley.edu/~ees/dadb/icsi_mrda+hs_corpus_050512.tar.gz)] consists of about 75 hours of speech from 75 naturally-occurring meetings among 53 speakers. The tagset used for labeling is a modified version of the SWBD-DAMSL tagset. It is annotated with three types of information: marking of the dialogue act segment boundaries, marking of the dialogue acts and marking of correspondences between dialogue acts.   \nAnnotated example:  \n*Time:* 2804-2810, *Speaker:* c6, *Dialogue Act:* s^bd, *Transcript:* i mean these are just discriminative.  \nMultiple dialogue acts are separated by \"^\".", "dataset_links": [{"title": "MRDA corpus](http://www1.icsi.berkeley.edu/Speech/mr/) [[download", "url": "http://www1.icsi.berkeley.edu/Speech/mr/) [[download](http://www.icsi.berkeley.edu/~ees/dadb/icsi_mrda+hs_corpus_050512.tar.gz"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "CRF-ASN", "metrics": {"Accuracy": "91.7"}, "paper_title": "Dialogue Act Recognition via CRF-Attentive Structured Network", "paper_url": "https://arxiv.org/abs/1711.05568", "code_links": []}, {"model_name": "Bi-LSTM-CRF", "metrics": {"Accuracy": "90.9"}, "paper_title": "Dialogue Act Sequence Labeling using Hierarchical encoder with CRF", "paper_url": "https://arxiv.org/abs/1709.04250", "code_links": [{"title": "Link", "url": "https://github.com/YanWenqiang/HBLSTM-CRF"}]}]}}]}, {"task": "Dialogue state tracking", "description": "Dialogue state tacking consists of determining at each turn of a dialogue the\nfull representation of what the user wants at that point in the dialogue,\nwhich contains a goal constraint, a set of requested slots, and the user's dialogue act.", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Second dialogue state tracking challenge", "description": "For goal-oriented dialogue, the dataset of the [second Dialogue Systems Technology Challenges](http://www.aclweb.org/anthology/W14-4337)\n(DSTC2) is a common evaluation dataset. The DSTC2 focuses on the restaurant search domain. Models are\nevaluated based on accuracy on both individual and joint slot tracking.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "second Dialogue Systems Technology Challenges", "url": "http://www.aclweb.org/anthology/W14-4337"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Request", "Area", "Food", "Price", "Joint"], "rows": [{"model_name": "<PERSON><PERSON> et al.", "metrics": {"Request": "97.5", "Area": "-", "Food": "-", "Price": "-", "Joint": "74.5"}, "paper_title": "Global-locally Self-attentive Dialogue State Tracker", "paper_url": "https://arxiv.org/abs/1805.09655"}, {"model_name": "<PERSON> et al.", "metrics": {"Request": "-", "Area": "90", "Food": "84", "Price": "92", "Joint": "72"}, "paper_title": "Dialogue Learning with Human Teaching and Feedback in End-to-End Trainable Task-Oriented Dialogue Systems", "paper_url": "https://arxiv.org/abs/1804.06512"}, {"model_name": "Neural belief tracker", "metrics": {"Request": "96.5", "Area": "90", "Food": "84", "Price": "94", "Joint": "73.4"}, "paper_title": "Neural Belief Tracker: Data-Driven Dialogue State Tracking", "paper_url": "https://arxiv.org/abs/1606.03777"}, {"model_name": "RNN", "metrics": {"Request": "95.7", "Area": "92", "Food": "86", "Price": "86", "Joint": "69"}, "paper_title": "Robust dialog state tracking using delexicalised recurrent neural networks and unsupervised gate", "paper_url": "http://svr-ftp.eng.cam.ac.uk/~sjy/papers/htyo14.pdf"}]}}, {"dataset": "Wizard-of-Oz", "description": "The [WoZ 2.0 dataset](https://arxiv.org/pdf/1606.03777.pdf) is a newer dialogue state tracking dataset whose evaluation is detached from the noisy output of speech recognition systems. Similar to DSTC2, it covers the restaurant search domain and has identical evaluation.", "dataset_links": [{"title": "WoZ 2.0 dataset", "url": "https://arxiv.org/pdf/1606.03777.pdf"}], "sota": {"metrics": ["Request", "Joint"], "rows": [{"model_name": "<PERSON><PERSON> et al.", "metrics": {"Request": "97.1", "Joint": "88.1"}, "paper_title": "Global-locally Self-attentive Dialogue State Tracker", "paper_url": "https://arxiv.org/abs/1805.09655"}, {"model_name": "Neural belief tracker", "metrics": {"Request": "96.5", "Joint": "84.4"}, "paper_title": "Neural Belief Tracker: Data-Driven Dialogue State Tracking", "paper_url": "https://arxiv.org/abs/1606.03777"}, {"model_name": "RNN", "metrics": {"Request": "87.1", "Joint": "70.8"}, "paper_title": "Robust dialog state tracking using delexicalised recurrent neural networks and unsupervised gate", "paper_url": "http://svr-ftp.eng.cam.ac.uk/~sjy/papers/htyo14.pdf"}]}}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Relationship Extraction", "description": "Relationship extraction is the task of extracting semantic relationships from a text. Extracted relationships usually\noccur between two or more entities of a certain type (e.g. Person, Organisation, Location) and fall into a number of\nsemantic categories (e.g. married to, employed by, lives in).", "datasets": [{"dataset": "New York Times Corpus", "description": "The standard corpus for distantly supervised relationship extraction is the New York Times (NYT) corpus, published in\n[<PERSON><PERSON><PERSON> et al, 2010](http://www.riedelcastro.org//publications/papers/riedel10modeling.pdf).\n\nThis contains text from the [New York Times Annotated Corpus](https://catalog.ldc.upenn.edu/ldc2008t19) with named\nentities extracted from the text using the Stanford NER system and automatically linked to entities in the Freebase\nknowledge base. Pairs of named entities are labelled with relationship types by aligning them against facts in the\nFreebase knowledge base. (The process of using a separate database to provide label is known as 'distant supervision')\n\nExample:\n > **Elevation Partners**, the $1.9 billion private equity group that was founded by **<PERSON>**\n\n `(founded_by, Elevation_Partners, Roger_M<PERSON>)`\n\nDifferent papers have reported various metrics since the release of the dataset, making it difficult to compare systems\ndirectly. The main metrics used are either precision at N results or plots of the precision-recall. The range of recall\nhas increased over the years as systems improve, with earlier systems having very low precision at 30% recall.\n\n\n\n\n(+) Obtained from results in the paper \"Neural Relation Extraction with Selective Attention over Instances\"", "dataset_links": [{"title": "<PERSON><PERSON><PERSON> et al, 2010", "url": "http://www.riedelcastro.org//publications/papers/riedel10modeling.pdf"}, {"title": "New York Times Annotated Corpus", "url": "https://catalog.ldc.upenn.edu/ldc2008t19"}], "sota": {"metrics": ["P@10%", "P@30%"], "rows": [{"model_name": "RESIDE", "metrics": {"P@10%": "73.6", "P@30%": "59.5"}, "paper_title": "RESIDE: Improving Distantly-Supervised Neural Relation Extraction using Side Information", "paper_url": "http://malllabiisc.github.io/publications/papers/reside_emnlp18.pdf", "code_links": [{"title": "RESIDE", "url": "https://github.com/malllabiisc/RESIDE"}]}, {"model_name": "PCNN+ATT", "metrics": {"P@10%": "69.4", "P@30%": "51.8"}, "paper_title": "Neural Relation Extraction with Selective Attention over Instances", "paper_url": "http://www.aclweb.org/anthology/P16-1200", "code_links": [{"title": "OpenNRE", "url": "https://github.com/thunlp/OpenNRE/"}]}, {"model_name": "MIML-RE", "metrics": {"P@10%": "60.7+", "P@30%": "-"}, "paper_title": "Multi-instance Multi-label Learning for Relation Extraction", "paper_url": "http://www.aclweb.org/anthology/D12-1042", "code_links": [{"title": "Mimlre", "url": "https://nlp.stanford.edu/software/mimlre.shtml"}]}, {"model_name": "MultiR", "metrics": {"P@10%": "60.9+", "P@30%": "-"}, "paper_title": "Knowledge-Based Weak Supervision for Information Extraction of Overlapping Relations", "paper_url": "http://www.aclweb.org/anthology/P11-1055", "code_links": [{"title": "MultiR", "url": "http://aiweb.cs.washington.edu/ai/raphaelh/mr/"}]}, {"model_name": "", "metrics": {"P@10%": "39.9+", "P@30%": "-"}, "paper_title": "Distant supervision for relation extraction without labeled data", "paper_url": "http://www.aclweb.org/anthology/P09-1113", "code_links": []}]}}, {"dataset": "SemEval-2010 Task 8", "description": "[SemEval-2010](http://www.aclweb.org/anthology/S10-1006) introduced 'Task 8 - Multi-Way Classification of Semantic\nRelations Between Pairs of Nominals'. The task is, given a sentence and two tagged nominals, to predict the relation\nbetween those nominals *and* the direction of the relation. The dataset contains nine general semantic relations\ntogether with a tenth 'OTHER' relation.\n\nExample:\n > There were apples, **pears** and oranges in the **bowl**.\n\n `(content-container, pears, bowl)`\n\nThe main evaluation metric used is macro-averaged F1, averaged across the nine proper relationships (i.e. excluding the\nOTHER relation), taking directionality of the relation into account.\n\nSeveral papers have used additional data (e.g. pre-trained word embeddings, WordNet) to improve performance. The figures\nreported here are the highest achieved by the model using any external resources.", "dataset_links": [{"title": "SemEval-2010", "url": "http://www.aclweb.org/anthology/S10-1006"}]}]}, {"task": "FewRel", "description": "The Few-Shot Relation Classification Dataset (FewRel) is a different setting from the previous datasets. This dataset consists of 70K sentences expressing 100 relations annotated by crowdworkers on Wikipedia corpus. The few-shot learning task follows the N-way K-shot meta learning setting. It is both the largest supervised relation classification dataset as well as the largest few-shot learning dataset till now. \n\nThe public leaderboard is available on the [FewRel website](http://zhuhao.me/fewrel).\n\n[Go back to the README](../README.md)", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Word Sense Disambiguation", "description": "The task of Word Sense Disambiguation (WSD) consists of associating words in context with their most suitable entry in a pre-defined sense inventory. The de-facto sense inventory for English in WSD is [WordNet](https://wordnet.princeton.edu).\nFor example, given the word “mouse” and the following sentence:\n\n“A mouse consists of an object held in one's hand, with one or more buttons.” \n\nwe would assign “mouse”  with its electronic device sense ([the 4th sense in the WordNet sense inventory](http://wordnetweb.princeton.edu/perl/webwn?c=8&sub=Change&o2=&o0=1&o8=1&o1=1&o7=&o5=&o9=&o6=&o3=&o4=&i=-1&h=000000&s=mouse)).", "datasets": [{"dataset": "Fine-grained WSD:", "description": "The [Evaluation framework](http://lcl.uniroma1.it/wsdeval/) of [<PERSON><PERSON><PERSON> et al. 2017](http://aclweb.org/anthology/E/E17/E17-1010.pdf) [1] includes two training sets (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 1993- and OMSTI-<PERSON> and <PERSON>, 2015-) and five test sets from the Senseval/SemEval series (Edmonds and Cotton, 2001; <PERSON> and <PERSON>, 2004; <PERSON><PERSON><PERSON> et al., 2007; <PERSON><PERSON><PERSON> et al., 2013; <PERSON><PERSON> and <PERSON>, 2015), standardized to the same format and sense inventory (i.e. WordNet 3.0).\n\nTypically, there are two kinds of approach for WSD: supervised (which make use of sense-annotated training data) and knowledge-based (which make use of the properties of lexical resources).\n\nSupervised: The most widely used training corpus used is SemCor, with 226,036 sense annotations from 352 documents manually annotated. All supervised systems in the evaluation table are trained on SemCor. Some supervised methods, particularly neural architectures, usually employ the SemEval 2007 dataset as development set (marked by *). The most usual baseline is the Most Frequent Sense (MFS) heuristic, which selects for each target word the most frequent sense in the training data.\n\nKnowledge-based:  Knowledge-based systems usually exploit WordNet or [BabelNet](https://babelnet.org/) as semantic network. The first sense given by the underlying sense inventory (i.e. WordNet 3.0) is included as a baseline.\n\nThe main evaluation measure is F1-score.", "dataset_links": [{"title": "Evaluation framework](http://lcl.uniroma1.it/wsdeval/) of [Ra<PERSON>ato et al. 2017](http://aclweb.org/anthology/E/E17/E17-1010.pdf) [1", "url": "http://lcl.uniroma1.it/wsdeval/) of [<PERSON><PERSON><PERSON> et al. 2017](http://aclweb.org/anthology/E/E17/E17-1010.pdf) [1] includes two training sets (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 1993- and OMSTI-<PERSON><PERSON><PERSON> and <PERSON>, 2015-) and five test sets from the Senseval/SemEval series (<PERSON><PERSON> and Cotton, 2001; <PERSON> and <PERSON>, 2004; <PERSON><PERSON><PERSON> et al., 2007; <PERSON><PERSON><PERSON> et al., 2013; <PERSON><PERSON> and <PERSON><PERSON>, 2015), standardized to the same format and sense inventory (i.e. WordNet 3.0"}, {"title": "BabelNet", "url": "https://babelnet.org/) as semantic network. The first sense given by the underlying sense inventory (i.e. WordNet 3.0"}]}, {"dataset": "Supervised:", "description": "", "sota": {"metrics": ["Senseval 2", "Senseval 3", "SemEval 2007", "SemEval 2013", "SemEval 2015"], "rows": [{"model_name": "MFS baseline", "metrics": {"Senseval 2": "65.6", "Senseval 3": "66.0", "SemEval 2007": "54.5", "SemEval 2013": "63.8", "SemEval 2015": "67.1"}, "paper_title": "[1]", "paper_url": "http://aclweb.org/anthology/E/E17/E17-1010.pdf"}, {"model_name": "Bi-LSTM<sub>att+LEX</sub>", "metrics": {"Senseval 2": "72.0", "Senseval 3": "69.4", "SemEval 2007": "63.7*", "SemEval 2013": "66.4", "SemEval 2015": "72.4"}, "paper_title": "[2]", "paper_url": "http://aclweb.org/anthology/D17-1120"}, {"model_name": "Bi-LSTM<sub>att+LEX+POS</sub>", "metrics": {"Senseval 2": "72.0", "Senseval 3": "69.1", "SemEval 2007": "64.8*", "SemEval 2013": "66.9", "SemEval 2015": "71.5"}, "paper_title": "[2]", "paper_url": "http://aclweb.org/anthology/D17-1120"}, {"model_name": "context2vec", "metrics": {"Senseval 2": "71.8", "Senseval 3": "69.1", "SemEval 2007": "61.3", "SemEval 2013": "65.6", "SemEval 2015": "71.9"}, "paper_title": "[3]", "paper_url": "http://www.aclweb.org/anthology/K16-1006"}, {"model_name": "ELMo", "metrics": {"Senseval 2": "71.6", "Senseval 3": "69.6", "SemEval 2007": "62.2", "SemEval 2013": "66.2", "SemEval 2015": "71.3"}, "paper_title": "[4]", "paper_url": "http://aclweb.org/anthology/N18-1202"}, {"model_name": "GAS", "metrics": {"Senseval 2": "72.0", "Senseval 3": "70.0", "SemEval 2007": "--*", "SemEval 2013": "66.7", "SemEval 2015": "71.6"}, "paper_title": "[5]", "paper_url": "http://aclweb.org/anthology/P18-1230"}, {"model_name": "GAS", "metrics": {"Senseval 2": "72.1", "Senseval 3": "70.2", "SemEval 2007": "--*", "SemEval 2013": "67", "SemEval 2015": "71.8"}, "paper_title": "[5]", "paper_url": "http://aclweb.org/anthology/P18-1230"}, {"model_name": "GAS<sub>ext</sub>", "metrics": {"Senseval 2": "72.4", "Senseval 3": "70.1", "SemEval 2007": "--*", "SemEval 2013": "67.1", "SemEval 2015": "72.1"}, "paper_title": "[5]", "paper_url": "http://aclweb.org/anthology/P18-1230"}, {"model_name": "GAS<sub>ext</sub>", "metrics": {"Senseval 2": "72.2", "Senseval 3": "70.5", "SemEval 2007": "--*", "SemEval 2013": "67.2", "SemEval 2015": "72.6"}, "paper_title": "[5]", "paper_url": "http://aclweb.org/anthology/P18-1230"}, {"model_name": "supWSD", "metrics": {"Senseval 2": "71.3", "Senseval 3": "68.8", "SemEval 2007": "60.2", "SemEval 2013": "65.8", "SemEval 2015": "70.0"}, "paper_title": "[6]](https://aclanthology.info/pdf/P/P10/P10-4014.pdf) [[11]", "paper_url": "https://aclanthology.info/pdf/P/P10/P10-4014.pdf) [[11]](http://aclweb.org/anthology/D17-2018"}, {"model_name": "supWSD<sub>emb</sub>", "metrics": {"Senseval 2": "72.7", "Senseval 3": "70.6", "SemEval 2007": "63.1", "SemEval 2013": "66.8", "SemEval 2015": "71.8"}, "paper_title": "[7]](http://www.aclweb.org/anthology/P16-1085) [[11]", "paper_url": "http://www.aclweb.org/anthology/P16-1085) [[11]](http://aclweb.org/anthology/D17-2018"}]}}, {"dataset": "Knowledge-based:", "description": "Note: 'All' is the concatenation of all datasets, as described in [10] and [12]. The scores of [6,7] and [9] are not taken from the original papers but from the results of the implementations of [11] and [12], respectively.\n\n[1] [Word Sense Disambiguation: A Unified Evaluation Framework and Empirical Comparison](http://aclweb.org/anthology/E/E17/E17-1010.pdf)\n\n[2] [Neural Sequence Learning Models for Word Sense Disambiguation](http://aclweb.org/anthology/D17-1120)\n\n[3] [context2vec: Learning generic context embedding with bidirectional lstm](http://www.aclweb.org/anthology/K16-1006)\n\n[4] [Deep contextualized word representations](http://aclweb.org/anthology/N18-1202)\n\n[5] [Incorporating Glosses into Neural Word Sense Disambiguation](http://aclweb.org/anthology/P18-1230)\n\n[6] [It makes sense: A wide-coverage word sense disambiguation system for free text](https://aclanthology.info/pdf/P/P10/P10-4014.pdf)\n\n[7] [Embeddings for Word Sense Disambiguation: An Evaluation Study](http://www.aclweb.org/anthology/P16-1085)\n\n[8] [Entity Linking meets Word Sense Disambiguation: A Unified Approach](http://aclweb.org/anthology/Q14-1019)\n\n[9] [Random walks for knowledge-based word sense disambiguation](https://www.mitpressjournals.org/doi/full/10.1162/COLI_a_00164)\n\n[10] [Knowledge-based Word Sense Disambiguation using Topic Models](https://arxiv.org/pdf/1801.01900.pdf)\n\n[11] [SupWSD: A Flexible Toolkit for Supervised Word Sense Disambiguation](http://aclweb.org/anthology/D17-2018)\n\n[12] [The risk of sub-optimal use of Open Source NLP Software: UKB is inadvertently state-of-the-art in knowledge-based WSD](http://aclweb.org/anthology/W18-2505)", "dataset_links": [{"title": "1] [Word Sense Disambiguation: A Unified Evaluation Framework and Empirical Comparison", "url": "http://aclweb.org/anthology/E/E17/E17-1010.pdf"}, {"title": "2] [Neural Sequence Learning Models for Word Sense Disambiguation", "url": "http://aclweb.org/anthology/D17-1120"}, {"title": "3] [context2vec: Learning generic context embedding with bidirectional lstm", "url": "http://www.aclweb.org/anthology/K16-1006"}, {"title": "4] [Deep contextualized word representations", "url": "http://aclweb.org/anthology/N18-1202"}, {"title": "5] [Incorporating Glosses into Neural Word Sense Disambiguation", "url": "http://aclweb.org/anthology/P18-1230"}, {"title": "6] [It makes sense: A wide-coverage word sense disambiguation system for free text", "url": "https://aclanthology.info/pdf/P/P10/P10-4014.pdf"}, {"title": "7] [Embeddings for Word Sense Disambiguation: An Evaluation Study", "url": "http://www.aclweb.org/anthology/P16-1085"}, {"title": "8] [Enti<PERSON> Linking meets Word Sense Disambiguation: A Unified Approach", "url": "http://aclweb.org/anthology/Q14-1019"}, {"title": "9] [Random walks for knowledge-based word sense disambiguation", "url": "https://www.mitpressjournals.org/doi/full/10.1162/COLI_a_00164"}, {"title": "10] [Knowledge-based Word Sense Disambiguation using Topic Models", "url": "https://arxiv.org/pdf/1801.01900.pdf"}, {"title": "11] [SupWSD: A Flexible Toolkit for Supervised Word Sense Disambiguation", "url": "http://aclweb.org/anthology/D17-2018"}, {"title": "12] [The risk of sub-optimal use of Open Source NLP Software: UKB is inadvertently state-of-the-art in knowledge-based WSD", "url": "http://aclweb.org/anthology/W18-2505"}], "sota": {"metrics": ["All", "Senseval 2", "Senseval 3", "SemEval 2007", "SemEval 2013", "SemEval 2015"], "rows": [{"model_name": "WN 1st sense baseline", "metrics": {"All": "65.2", "Senseval 2": "66.8", "Senseval 3": "66.2", "SemEval 2007": "55.2", "SemEval 2013": "63.0", "SemEval 2015": "67.8"}, "paper_title": "[1]", "paper_url": "http://aclweb.org/anthology/E/E17/E17-1010.pdf"}, {"model_name": "<PERSON><PERSON><PERSON>", "metrics": {"All": "65.5", "Senseval 2": "67.0", "Senseval 3": "63.5", "SemEval 2007": "51.6", "SemEval 2013": "66.4", "SemEval 2015": "**70.3**"}, "paper_title": "[8]", "paper_url": "http://aclweb.org/anthology/Q14-1019"}, {"model_name": "UKB<sub>ppr_w2w-nf</sub>", "metrics": {"All": "57.5", "Senseval 2": "64.2", "Senseval 3": "54.8", "SemEval 2007": "40.0", "SemEval 2013": "64.5", "SemEval 2015": "64.5"}, "paper_title": "[9]](https://www.mitpressjournals.org/doi/full/10.1162/COLI_a_00164) [[12]", "paper_url": "https://www.mitpressjournals.org/doi/full/10.1162/COLI_a_00164) [[12]](http://aclweb.org/anthology/W18-2505"}, {"model_name": "UKB<sub>ppr_w2w</sub>", "metrics": {"All": "**67.3**", "Senseval 2": "68.8", "Senseval 3": "66.1", "SemEval 2007": "53.0", "SemEval 2013": "**68.8**", "SemEval 2015": "**70.3**"}, "paper_title": "[9]](https://www.mitpressjournals.org/doi/full/10.1162/COLI_a_00164) [[12]", "paper_url": "https://www.mitpressjournals.org/doi/full/10.1162/COLI_a_00164) [[12]](http://aclweb.org/anthology/W18-2505"}, {"model_name": "WSD-TM", "metrics": {"All": "66.9", "Senseval 2": "**69.0**", "Senseval 3": "**66.9**", "SemEval 2007": "**55.6**", "SemEval 2013": "65.3", "SemEval 2015": "69.6"}, "paper_title": "[10]", "paper_url": "https://arxiv.org/pdf/1801.01900.pdf"}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Multimodal", "description": "", "subtasks": [{"task": "Multimodal Emotion Recognition", "description": "", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "IEMOCAP", "description": "The  IEMOCAP ([<PERSON><PERSON>  et  al., 2008](https://link.springer.com/article/10.1007/s10579-008-9076-6)) contains the acts of 10 speakers in a two-way conversation segmented into utterances. The medium of the conversations in all the videos is English. The database contains the following categorical labels: anger, happiness, sadness, neutral, excitement, frustration, fear, surprise,  and other.\n\n**Monologue:**\n\n\n**Conversational:**\nConversational setting enables the models to capture emotions expressed by the speakers in a conversation. Inter speaker dependencies are considered in this setting.", "dataset_links": [{"title": "<PERSON><PERSON>  et  al., 2008", "url": "https://link.springer.com/article/10.1007/s10579-008-9076-6)"}], "subdatasets": [{"subdataset": "Monologue", "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "CHFusion", "metrics": {"Accuracy": "76.5%"}, "paper_title": "Multimodal Sentiment Analysis using Hierarchical Fusion with Context Modeling", "paper_url": "https://arxiv.org/pdf/1806.06228.pdf"}, {"model_name": "bc-LSTM", "metrics": {"Accuracy": "74.10%"}, "paper_title": "Context-Dependent Sentiment Analysis in User-Generated Videos", "paper_url": "http://sentic.net/context-dependent-sentiment-analysis-in-user-generated-videos.pdf"}]}}, {"subdataset": "Conversational setting enables the models to capture emotions expressed by the speakers in a conversation. Inter speaker dependencies are considered in this setting.", "sota": {"metrics": ["Weighted Accuracy (WAA)"], "rows": [{"model_name": "CMN", "metrics": {"Weighted Accuracy (WAA)": "77.62%"}, "paper_title": "Conversational Memory Network for Emotion Recognition in Dyadic Dialogue Videos", "paper_url": "http://aclweb.org/anthology/N18-1193"}, {"model_name": "Memn2n", "metrics": {"Weighted Accuracy (WAA)": "75.08"}, "paper_title": "Conversational Memory Network for Emotion Recognition in Dyadic Dialogue Videos", "paper_url": "http://aclweb.org/anthology/N18-1193"}]}}]}]}, {"task": "Multimodal Metaphor Recognition", "description": "[<PERSON> et. al, 2016](http://www.aclweb.org/anthology/S16-2003) created a dataset of verb-noun pairs from WordNet that had multiple senses. They annoted these pairs for metaphoricity (metaphor or not a metaphor). Dataset is in English.\n\n| Model                                                        |                            F1 Score                             | Paper / Source                                               | Code        |\n| ------------------------------------------------------------ | :----------------------------------------------------------: | ------------------------------------------------------------ | ----------- |\n| 5-layer convolutional network (<PERSON><PERSON><PERSON><PERSON> et al., 2012), Word2Vec | 0.75 | [Shutova et. al, 2016](http://www.aclweb.org/anthology/N16-1020) | Unavailable |\n\n[<PERSON><PERSON><PERSON><PERSON>  et. al, 2014](http://www.aclweb.org/anthology/P14-1024) created a dataset of adjective-noun pairs that they then annotated for metaphoricity. Dataset is in English.\n\n| Model                                                        |                            F1 Score                             | Paper / Source                                               | Code        |\n| ------------------------------------------------------------ | :----------------------------------------------------------: | ------------------------------------------------------------ | ----------- |\n| 5-layer convolutional network (Krizhevsky et al., 2012), Word2Vec | 0.79 | [Shutova et. al, 2016](http://www.aclweb.org/anthology/N16-1020) | Unavailable |", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Multimodal Sentiment Analysis", "description": "", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "MOSI", "description": "The MOSI dataset ([<PERSON><PERSON><PERSON> et al., 2016](https://arxiv.org/pdf/1606.06259.pdf)) is a dataset rich in sentimental expressions where 93 people review topics in English. The videos are segmented with each segments sentiment label scored between +3 (strong positive) to -3 (strong negative)  by  5  annotators.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "<PERSON><PERSON><PERSON> et al., 2016", "url": "https://arxiv.org/pdf/1606.06259.pdf)) is a dataset rich in sentimental expressions where 93 people review topics in English. The videos are segmented with each segments sentiment label scored between +3 (strong positive) to -3 (strong negative"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "bc-LSTM", "metrics": {"Accuracy": "80.3%"}, "paper_title": "Context-Dependent Sentiment Analysis in User-Generated Videos", "paper_url": "http://sentic.net/context-dependent-sentiment-analysis-in-user-generated-videos.pdf"}, {"model_name": "MARN", "metrics": {"Accuracy": "77.1%"}, "paper_title": "Multi-attention Recurrent Network for Human Communication Comprehension", "paper_url": "https://arxiv.org/pdf/1802.00923.pdf"}]}}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Summarization", "description": "Summarization is the task of producing a shorter version of one or several documents that preserves most of the\ninput's meaning.", "datasets": [{"dataset": "Warning: Evaluation Metrics", "description": "For summarization, automatic metrics such as ROUGE and METEOR have serious limitations:\n1. They only assess content selection and do not account for other quality aspects, such as fluency, grammaticality, coherence, etc. \n2. To assess content selection, they rely mostly on lexical overlap, although an abstractive summary could express they same content as a reference without any lexical overlap.\n3. Given the subjectiveness of summarization and the correspondingly low agreement between annotators, the metrics were designed to be used with multiple reference summaries per input. However, recent datasets such as CNN/DailyMail and Gigaword provide only a single reference.\n\nTherefore, tracking progress and claiming state-of-the-art based only on these metrics is questionable. Most papers carry out additional manual comparisons of alternative summaries. Unfortunately, such experiments are difficult to compare across papers. If you have an idea on how to do that, feel free to contribute."}, {"dataset": "CNN / Daily Mail", "description": "The [CNN / Daily Mail dataset](https://arxiv.org/abs/1506.03340) as processed by \n[<PERSON><PERSON><PERSON><PERSON> et al. (2016)](http://www.aclweb.org/anthology/K16-1028) has been used\nfor evaluating summarization. The dataset contains online news articles (781 tokens \non average) paired with multi-sentence summaries (3.75 sentences or 56 tokens on average).\nThe processed version contains 287,226 training pairs, 13,368 validation pairs and 11,490 test pairs.\nModels are evaluated with full-length F1-scores of ROUGE-1, ROUGE-2, ROUGE-L, and METEOR (optional).", "dataset_links": [{"title": "CNN / Daily Mail dataset", "url": "https://arxiv.org/abs/1506.03340"}, {"title": "<PERSON><PERSON><PERSON><PERSON> et al. (2016)", "url": "2016)](http://www.aclweb.org/anthology/K16-1028"}]}, {"dataset": "Gigaword", "description": "The Gigaword summarization dataset has been first used by [<PERSON> et al., 2015](https://www.aclweb.org/anthology/D/D15/D15-1044.pdf) and represents a sentence summarization / headline generation task with very short input documents (31.4 tokens) and summaries (8.3 tokens). It contains 3.8M training, 189k development and 1951 test instances. Models are evaluated with ROUGE-1, ROUGE-2 and ROUGE-L using full-length F1-scores.\n\n\n(*) [<PERSON> et al., 2015](https://www.aclweb.org/anthology/D/D15/D15-1044.pdf)  report ROUGE recall, the table here contains ROUGE F1-scores for <PERSON>'s model reported by [<PERSON><PERSON> et al., 2016](http://www.aclweb.org/anthology/N16-1012)", "dataset_links": [{"title": "<PERSON> et al., 2015", "url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf) and represents a sentence summarization / headline generation task with very short input documents (31.4 tokens) and summaries (8.3 tokens"}, {"title": "<PERSON> et al., 2015](https://www.aclweb.org/anthology/D/D15/D15-1044.pdf)  report ROUGE recall, the table here contains ROUGE F1-scores for <PERSON>'s model reported by [<PERSON><PERSON> et al., 2016", "url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf)  report ROUGE recall, the table here contains ROUGE F1-scores for <PERSON>'s model reported by [<PERSON><PERSON> et al., 2016](http://www.aclweb.org/anthology/N16-1012"}], "sota": {"metrics": ["ROUGE-1", "ROUGE-2", "ROUGE-L"], "rows": [{"model_name": "Re^3 Sum", "metrics": {"ROUGE-1": "37.04", "ROUGE-2": "19.03", "ROUGE-L": "34.46"}, "paper_title": "Retrieve, <PERSON><PERSON> and Rewrite: Soft Template Based Neural Summarization", "paper_url": "http://aclweb.org/anthology/P18-1015", "code_links": []}, {"model_name": "CGU", "metrics": {"ROUGE-1": "36.3", "ROUGE-2": "18.0", "ROUGE-L": "33.8"}, "paper_title": "Global Encoding for Abstractive Summarization", "paper_url": "http://aclweb.org/anthology/P18-2027", "code_links": [{"title": "Official", "url": "https://www.github.com/lancopku/Global-Encoding"}]}, {"model_name": "Pointer + Coverage + EntailmentGen + QuestionGen", "metrics": {"ROUGE-1": "35.98", "ROUGE-2": "17.76", "ROUGE-L": "33.63"}, "paper_title": "Soft Layer-Specific Multi-Task Summarization with Entailment and Question Generation", "paper_url": "http://aclweb.org/anthology/P18-1064", "code_links": []}, {"model_name": "words-lvt5k-1sent", "metrics": {"ROUGE-1": "36.4", "ROUGE-2": "17.7", "ROUGE-L": "33.71"}, "paper_title": "Abstractive Text Summarization using Sequence-to-sequence RNNs and Beyond", "paper_url": "http://www.aclweb.org/anthology/K16-1028", "code_links": []}, {"model_name": "Struct+2Way+Word", "metrics": {"ROUGE-1": "35.47", "ROUGE-2": "17.66", "ROUGE-L": "33.52"}, "paper_title": "Structure-Infused Copy Mechanisms for Abstractive Summarization", "paper_url": "http://aclweb.org/anthology/C18-1146", "code_links": []}, {"model_name": "FTSum_g", "metrics": {"ROUGE-1": "37.27", "ROUGE-2": "17.65", "ROUGE-L": "34.24"}, "paper_title": "Faithful to the Original: Fact Aware Neural Abstractive Summarization", "paper_url": "https://arxiv.org/pdf/1711.04434.pdf", "code_links": []}, {"model_name": "DRGD", "metrics": {"ROUGE-1": "36.27", "ROUGE-2": "17.57", "ROUGE-L": "33.62"}, "paper_title": "Deep Recurrent Generative Decoder for Abstractive Text Summarization", "paper_url": "http://aclweb.org/anthology/D17-1222", "code_links": []}, {"model_name": "SEASS", "metrics": {"ROUGE-1": "36.15", "ROUGE-2": "17.54", "ROUGE-L": "33.63"}, "paper_title": "Selective Encoding for Abstractive Sentence Summarization", "paper_url": "http://aclweb.org/anthology/P17-1101", "code_links": []}, {"model_name": "EndDec+WFE", "metrics": {"ROUGE-1": "36.30", "ROUGE-2": "17.31", "ROUGE-L": "33.88"}, "paper_title": "Cutting-off Redundant Repeating Generations for Neural Abstractive Summarization", "paper_url": "http://aclweb.org/anthology/E17-2047", "code_links": []}, {"model_name": "Seq2seq + selective + MTL + ERAM", "metrics": {"ROUGE-1": "35.33", "ROUGE-2": "17.27", "ROUGE-L": "33.19"}, "paper_title": "Ensure the Correctness of the Summary: Incorporate Entailment Knowledge into Abstractive Sentence Summarization", "paper_url": "http://aclweb.org/anthology/C18-1121", "code_links": []}, {"model_name": "Seq2seq + E2T_cnn", "metrics": {"ROUGE-1": "37.04", "ROUGE-2": "16.66", "ROUGE-L": "34.93"}, "paper_title": "Entity Commonsense Representation for Neural Abstractive Summarization", "paper_url": "http://aclweb.org/anthology/N18-1064", "code_links": []}, {"model_name": "RAS-<PERSON><PERSON>", "metrics": {"ROUGE-1": "33.78", "ROUGE-2": "15.97", "ROUGE-L": "31.15"}, "paper_title": "Abstractive Sentence Summarization with Attentive Recurrent Neural Networks", "paper_url": "http://www.aclweb.org/anthology/N16-1012", "code_links": []}, {"model_name": "ABS+", "metrics": {"ROUGE-1": "29.76", "ROUGE-2": "11.88", "ROUGE-L": "26.96"}, "paper_title": "A Neural Attention Model for Sentence Summarization", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf", "code_links": []}, {"model_name": "ABS", "metrics": {"ROUGE-1": "29.55", "ROUGE-2": "11.32", "ROUGE-L": "26.42"}, "paper_title": "A Neural Attention Model for Sentence Summarization", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf", "code_links": []}]}}, {"dataset": "DUC 2004 Task 1", "description": "Similar to Gigaword, task 1 of [DUC 2004](https://duc.nist.gov/duc2004/) is a sentence summarization task. The dataset contains 500 documents with on average 35.6 tokens and summaries with 10.4 tokens. Due to its size, neural models are typically trained on other datasets and only tested on DUC 2004. Evaluation metrics are ROUGE-1, ROUGE-2 and ROUGE-L recall @ 75 bytes.", "dataset_links": [{"title": "DUC 2004", "url": "https://duc.nist.gov/duc2004/"}], "sota": {"metrics": ["ROUGE-1", "ROUGE-2", "ROUGE-L"], "rows": [{"model_name": "DRGD", "metrics": {"ROUGE-1": "31.79", "ROUGE-2": "10.75", "ROUGE-L": "27.48"}, "paper_title": "Deep Recurrent Generative Decoder for Abstractive Text Summarization", "paper_url": "http://aclweb.org/anthology/D17-1222", "code_links": []}, {"model_name": "EndDec+WFE", "metrics": {"ROUGE-1": "32.28", "ROUGE-2": "10.54", "ROUGE-L": "27.8"}, "paper_title": "Cutting-off Redundant Repeating Generations for Neural Abstractive Summarization", "paper_url": "http://aclweb.org/anthology/E17-2047", "code_links": []}, {"model_name": "Seq2seq + selective + MTL + ERAM", "metrics": {"ROUGE-1": "29.33", "ROUGE-2": "10.24", "ROUGE-L": "25.24"}, "paper_title": "Ensure the Correctness of the Summary: Incorporate Entailment Knowledge into Abstractive Sentence Summarization", "paper_url": "http://aclweb.org/anthology/C18-1121", "code_links": []}, {"model_name": "SEASS", "metrics": {"ROUGE-1": "29.21", "ROUGE-2": "9.56", "ROUGE-L": "25.51"}, "paper_title": "Selective Encoding for Abstractive Sentence Summarization", "paper_url": "http://aclweb.org/anthology/P17-1101", "code_links": []}, {"model_name": "words-lvt5k-1sent", "metrics": {"ROUGE-1": "28.61", "ROUGE-2": "9.42", "ROUGE-L": "25.24"}, "paper_title": "Abstractive Text Summarization using Sequence-to-sequence RNNs and Beyond", "paper_url": "http://www.aclweb.org/anthology/K16-1028", "code_links": []}, {"model_name": "ABS+", "metrics": {"ROUGE-1": "28.18", "ROUGE-2": "8.49", "ROUGE-L": "23.81"}, "paper_title": "A Neural Attention Model for Sentence Summarization", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf", "code_links": []}, {"model_name": "RAS-<PERSON><PERSON>", "metrics": {"ROUGE-1": "28.97", "ROUGE-2": "8.26", "ROUGE-L": "24.06"}, "paper_title": "Abstractive Sentence Summarization with Attentive Recurrent Neural Networks", "paper_url": "http://www.aclweb.org/anthology/N16-1012", "code_links": []}, {"model_name": "ABS", "metrics": {"ROUGE-1": "26.55", "ROUGE-2": "7.06", "ROUGE-L": "22.05"}, "paper_title": "A Neural Attention Model for Sentence Summarization", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf", "code_links": []}]}}], "subtasks": [{"task": "Webis-TLDR-17 Corpus", "description": "This [dataset](https://zenodo.org/record/1168855) contains 3 Million pairs of content and self-written summaries mined from Reddit. It is one of the first large-scale summarization dataset from the social media domain. For more details, refer to [TL;DR: Mining Reddit to Learn Automatic Summarization](https://aclweb.org/anthology/W17-4508)", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Sentence Compression", "description": "Sentence compression produces a shorter sentence by removing redundant information,\npreserving the grammatically and the important content of the original sentence.", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Google Dataset", "description": "The [Google Dataset](https://github.com/google-research-datasets/sentence-compression) was built by <PERSON><PERSON><PERSON><PERSON> et al., 2013([Overcoming the Lack of Parallel Data in Sentence Compression](https://www.aclweb.org/anthology/D/D13/D13-1155.pdf)). The first dataset released contained only 10,000 sentence-compression pairs, but last year was released an additional 200,000 pairs. \n\nExample of a sentence-compression pair:\n> Sentence: <PERSON> is open to fighting <PERSON> in the future, despite snubbing the Bolton-born boxer in favour of a May bout with Argentine <PERSON>, according to promoters <PERSON> Boy\n\n> Compression: <PERSON> is open to fighting <PERSON> in the future. \n\nIn short, this is a deletion-based task where the compression is a subsequence from the original sentence. From the 10,000 pairs of the eval portion([repository](https://github.com/google-research-datasets/sentence-compression/tree/master/data)) it is used the very first 1,000 sentence for automatic evaluation and the 200,000 pairs for training.\n\nModels are evaluated using the following metrics:\n* F1 - compute the recall and precision in terms of tokens kept in the golden and the generated compressions.\n* Compression rate (CR) - the length of the compression in characters divided over the sentence length. \n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "Google Dataset](https://github.com/google-research-datasets/sentence-compression) was built by <PERSON><PERSON><PERSON><PERSON> et al., 2013([Overcoming the Lack of Parallel Data in Sentence Compression", "url": "https://github.com/google-research-datasets/sentence-compression) was built by <PERSON><PERSON><PERSON><PERSON> et al., 2013([Overcoming the Lack of Parallel Data in Sentence Compression](https://www.aclweb.org/anthology/D/D13/D13-1155.pdf)"}, {"title": "repository", "url": "https://github.com/google-research-datasets/sentence-compression/tree/master/data)"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["F1", "CR"], "rows": [{"model_name": "BiRNN + LM Evaluator", "metrics": {"F1": "0.851", "CR": "0.39"}, "paper_title": "A Language Model based Evaluator for Sentence Compression", "paper_url": "https://aclweb.org/anthology/P18-2028", "code_links": []}, {"model_name": "LSTM", "metrics": {"F1": "0.82", "CR": "0.38"}, "paper_title": "Sentence Compression by Deletion with LSTMs", "paper_url": "https://research.google.com/pubs/archive/43852.pdf", "code_links": []}, {"model_name": "BiLSTM", "metrics": {"F1": "0.8", "CR": "0.43"}, "paper_title": "Can Syntax Help? Improving an LSTM-based Sentence Compression Model for New Domains", "paper_url": "http://www.aclweb.org/anthology/P17-1127", "code_links": []}]}}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Machine translation", "description": "Machine translation is the task of translating a sentence in a source language to a different target language. \n\nResults with a * indicate that the mean test score over the the best window based on average dev-set BLEU score over \n21 consecutive evaluations is reported as in [<PERSON> et al. (2018)](https://arxiv.org/abs/1804.09849).", "datasets": [{"dataset": "WMT 2014 EN-DE", "description": "Models are evaluated on the English-German dataset of the Ninth Workshop on Statistical Machine Translation (WMT 2014) based\non BLEU.", "sota": {"metrics": ["BLEU"], "rows": [{"model_name": "Transformer Big + BT", "metrics": {"BLEU": "35.0"}, "paper_title": "Understanding Back-Translation at Scale", "paper_url": "https://arxiv.org/pdf/1808.09381.pdf"}, {"model_name": "DeepL", "metrics": {"BLEU": "33.3"}, "paper_title": "DeepL Press release", "paper_url": "https://www.deepl.com/press.html"}, {"model_name": "Transformer Big", "metrics": {"BLEU": "29.3"}, "paper_title": "Scaling Neural Machine Translation", "paper_url": "https://arxiv.org/abs/1806.00187"}, {"model_name": "RNMT+", "metrics": {"BLEU": "28.5*"}, "paper_title": "The Best of Both Worlds: Combining Recent Advances in Neural Machine Translation", "paper_url": "https://arxiv.org/abs/1804.09849"}, {"model_name": "Transformer Big", "metrics": {"BLEU": "28.4"}, "paper_title": "Attention Is All You Need", "paper_url": "https://arxiv.org/abs/1706.03762"}, {"model_name": "Transformer Base", "metrics": {"BLEU": "27.3"}, "paper_title": "Attention Is All You Need", "paper_url": "https://arxiv.org/abs/1706.03762"}, {"model_name": "MoE", "metrics": {"BLEU": "26.03"}, "paper_title": "Outrageously Large Neural Networks: The Sparsely-Gated Mixture-of-Experts Layer", "paper_url": "https://arxiv.org/abs/1701.06538"}, {"model_name": "ConvS2S", "metrics": {"BLEU": "25.16"}, "paper_title": "Convolutional Sequence to Sequence Learning", "paper_url": "https://arxiv.org/abs/1705.03122"}]}}, {"dataset": "WMT 2014 EN-FR", "description": "Similarly, models are evaluated on the English-French dataset of the Ninth Workshop on Statistical Machine Translation (WMT 2014) based\non BLEU.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["BLEU"], "rows": [{"model_name": "DeepL", "metrics": {"BLEU": "45.9"}, "paper_title": "DeepL Press release", "paper_url": "https://www.deepl.com/press.html"}, {"model_name": "Transformer Big + BT", "metrics": {"BLEU": "45.6"}, "paper_title": "Understanding Back-Translation at Scale", "paper_url": "https://arxiv.org/pdf/1808.09381.pdf"}, {"model_name": "Transformer Big", "metrics": {"BLEU": "43.2"}, "paper_title": "Scaling Neural Machine Translation", "paper_url": "https://arxiv.org/abs/1806.00187"}, {"model_name": "RNMT+", "metrics": {"BLEU": "41.0*"}, "paper_title": "The Best of Both Worlds: Combining Recent Advances in Neural Machine Translation", "paper_url": "https://arxiv.org/abs/1804.09849"}, {"model_name": "Transformer Big", "metrics": {"BLEU": "41.0"}, "paper_title": "Attention Is All You Need", "paper_url": "https://arxiv.org/abs/1706.03762"}, {"model_name": "MoE", "metrics": {"BLEU": "40.56"}, "paper_title": "Outrageously Large Neural Networks: The Sparsely-Gated Mixture-of-Experts Layer", "paper_url": "https://arxiv.org/abs/1701.06538"}, {"model_name": "ConvS2S", "metrics": {"BLEU": "40.46"}, "paper_title": "Convolutional Sequence to Sequence Learning", "paper_url": "https://arxiv.org/abs/1705.03122"}, {"model_name": "Transformer Base", "metrics": {"BLEU": "38.1"}, "paper_title": "Attention Is All You Need", "paper_url": "https://arxiv.org/abs/1706.03762"}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Automatic speech recognition (ASR)", "description": "Automatic speech recognition is the task of automatically recognizing speech. You \ncan find a repository tracking the state-of-the-art [here](https://github.com/syhw/wer_are_we).", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Text classification", "description": "Text classification is the task of assigning a sentence or document an appropriate category.\nThe categories depend on the chosen dataset and can range from topics.", "datasets": [{"dataset": "AG News", "description": "The [AG News corpus](https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf)\nconsists of news articles from the [AG's corpus of news articles on the web](http://www.di.unipi.it/~gulli/AG_corpus_of_news_articles.html)\npertaining to the 4 largest classes. The dataset contains 30,000 training examples for each class\n1,900 examples for each class for testing. Models are evaluated based on error rate (lower is better).\n\n\n\\* Results reported in Johnson and Zhang, 2017", "dataset_links": [{"title": "AG News corpus", "url": "https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf"}, {"title": "AG's corpus of news articles on the web", "url": "http://www.di.unipi.it/~gulli/AG_corpus_of_news_articles.html"}], "sota": {"metrics": ["Error"], "rows": [{"model_name": "ULMFiT", "metrics": {"Error": "5.01"}, "paper_title": "Universal Language Model Fine-tuning for Text Classification", "paper_url": "https://arxiv.org/abs/1801.06146", "code_links": [{"title": "Official", "url": "http://nlp.fast.ai/ulmfit"}]}, {"model_name": "CNN", "metrics": {"Error": "6.57"}, "paper_title": "Supervised and Semi-Supervised Text Categorization using LSTM for Region Embeddings", "paper_url": "https://arxiv.org/abs/1602.02373", "code_links": [{"title": "Official", "url": "https://github.com/rie<PERSON><PERSON>son/ConText"}]}, {"model_name": "DPCNN", "metrics": {"Error": "6.87"}, "paper_title": "Deep Pyramid Convolutional Neural Networks for Text Categorization", "paper_url": "http://aclweb.org/anthology/P17-1052", "code_links": [{"title": "Official", "url": "https://github.com/rie<PERSON><PERSON>son/ConText"}]}, {"model_name": "VDCN", "metrics": {"Error": "8.67"}, "paper_title": "Very Deep Convolutional Networks for Text Classification", "paper_url": "https://arxiv.org/abs/1606.01781", "code_links": [{"title": "Non Official", "url": "https://github.com/ArdalanM/nlp-benchmarks/blob/master/src/VDCNN.py"}]}, {"model_name": "Char-level CNN", "metrics": {"Error": "9.51"}, "paper_title": "Character-level Convolutional Networks for Text Classification", "paper_url": "https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf", "code_links": [{"title": "Non Official", "url": "https://github.com/ArdalanM/nlp-benchmarks/blob/master/src/CNN.py"}]}]}}, {"dataset": "DBpedia", "description": "The [DBpedia ontology](https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf) \ndataset contains 560,000 training samples and 70,000 testing samples for each of 14 nonoverlapping classes from DBpedia.\nModels are evaluated based on error rate (lower is better).", "dataset_links": [{"title": "DBpedia ontology", "url": "https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf"}], "sota": {"metrics": ["Error"], "rows": [{"model_name": "ULMFiT", "metrics": {"Error": "0.80"}, "paper_title": "Universal Language Model Fine-tuning for Text Classification", "paper_url": "https://arxiv.org/abs/1801.06146", "code_links": [{"title": "Official", "url": "http://nlp.fast.ai/ulmfit"}]}, {"model_name": "CNN", "metrics": {"Error": "0.84"}, "paper_title": "Supervised and Semi-Supervised Text Categorization using LSTM for Region Embeddings", "paper_url": "https://arxiv.org/abs/1602.02373", "code_links": [{"title": "Official", "url": "https://github.com/rie<PERSON><PERSON>son/ConText"}]}, {"model_name": "DPCNN", "metrics": {"Error": "0.88"}, "paper_title": "Deep Pyramid Convolutional Neural Networks for Text Categorization", "paper_url": "http://aclweb.org/anthology/P17-1052", "code_links": [{"title": "Official", "url": "https://github.com/rie<PERSON><PERSON>son/ConText"}]}, {"model_name": "VDCN", "metrics": {"Error": "1.29"}, "paper_title": "Very Deep Convolutional Networks for Text Classification", "paper_url": "https://arxiv.org/abs/1606.01781", "code_links": [{"title": "Non Official", "url": "https://github.com/ArdalanM/nlp-benchmarks/blob/master/src/VDCNN.py"}]}, {"model_name": "Char-level CNN", "metrics": {"Error": "1.55"}, "paper_title": "Character-level Convolutional Networks for Text Classification", "paper_url": "https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf", "code_links": [{"title": "Non Official", "url": "https://github.com/ArdalanM/nlp-benchmarks/blob/master/src/CNN.py"}]}]}}, {"dataset": "TREC", "description": "The [TREC dataset](http://citeseerx.ist.psu.edu/viewdoc/download?doi=*********.2766&rep=rep1&type=pdf) is dataset for\nquestion classification consisting of open-domain, fact-based questions divided into broad semantic categories. \nIt has both a six-class (TREC-6) and a fifty-class (TREC-50) version. Both have 5,452 training examples and 500 test examples, \nbut TREC-50 has finer-grained labels. Models are evaluated based on accuracy.\n\nTREC-6:\n\n\nTREC-50:\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "TREC dataset", "url": "http://citeseerx.ist.psu.edu/viewdoc/download?doi=*********.2766&rep=rep1&type=pdf"}, {"title": "Go back to the README", "url": "../README.md"}], "subdatasets": [{"subdataset": "TREC-6", "sota": {"metrics": ["Error"], "rows": [{"model_name": "USE_T+CNN", "metrics": {"Error": "1.93"}, "paper_title": "Universal Sentence Encoder", "paper_url": "https://arxiv.org/pdf/1803.11175.pdf", "code_links": [{"title": "Official", "url": "https://tfhub.dev/google/universal-sentence-encoder/1"}]}, {"model_name": "ULMFiT", "metrics": {"Error": "3.6"}, "paper_title": "Universal Language Model Fine-tuning for Text Classification", "paper_url": "https://arxiv.org/abs/1801.06146", "code_links": [{"title": "Official", "url": "http://nlp.fast.ai/ulmfit"}]}, {"model_name": "LSTM-CNN", "metrics": {"Error": "3.9"}, "paper_title": "Text Classification Improved by Integrating Bidirectional LSTM with Two-dimensional Max Pooling", "paper_url": "http://www.aclweb.org/anthology/C16-1329", "code_links": []}, {"model_name": "CNN+MCFA", "metrics": {"Error": "4"}, "paper_title": "Translations as Additional Contexts for Sentence Classification", "paper_url": "https://arxiv.org/pdf/1806.05516.pdf", "code_links": []}, {"model_name": "TBCNN", "metrics": {"Error": "4"}, "paper_title": "Discriminative Neural Sentence Modeling by Tree-Based Convolution", "paper_url": "http://aclweb.org/anthology/D15-1279", "code_links": []}, {"model_name": "CoVe", "metrics": {"Error": "4.2"}, "paper_title": "Learned in Translation: Contextualized Word Vectors", "paper_url": "https://arxiv.org/abs/1708.00107", "code_links": []}]}}, {"subdataset": "TREC-50", "sota": {"metrics": ["Error"], "rows": [{"model_name": "Rules", "metrics": {"Error": "2.8"}, "paper_title": "High Accuracy Rule-based Question Classification using Question Syntax and Semantics", "paper_url": "http://www.aclweb.org/anthology/C16-1116", "code_links": []}, {"model_name": "SVM", "metrics": {"Error": "8.4"}, "paper_title": "Improving Question Classification by Feature Extraction and Selection", "paper_url": "https://www.researchgate.net/publication/303553351_Improving_Question_Classification_by_Feature_Extraction_and_Selection", "code_links": []}]}}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Question answering", "description": "Question answering is the task of answering a question.", "datasets": [{"dataset": "ARC", "description": "The [AI2 Reasoning Challenge (ARC)](http://ai2-website.s3.amazonaws.com/publications/AI2ReasoningChallenge2018.pdf)\ndataset is a question answering, which contains 7,787 genuine grade-school level, multiple-choice science questions.\nThe dataset is partitioned into a Challenge Set and an Easy Set. The Challenge Set contains only questions\nanswered incorrectly by both a retrieval-based algorithm and a word co-occurrence algorithm. Models are evaluated\nbased on accuracy.\n\nA public leaderboard is available on the [ARC website](http://data.allenai.org/arc/).", "dataset_links": [{"title": "AI2 Reasoning Challenge (ARC)", "url": "ARC)](http://ai2-website.s3.amazonaws.com/publications/AI2ReasoningChallenge2018.pdf"}, {"title": "ARC website", "url": "http://data.allenai.org/arc/"}]}, {"dataset": "ShARC", "description": "[ShARC](https://arxiv.org/abs/1809.01494) is a challenging QA dataset that requires  logical reasoning, elements of entailment/NLI and natural language generation.\n\nMost work in machine reading focuses on question answering problems where the answer is directly expressed in the text to read. However, many real-world question answering problems require the reading of text not because it contains the literal answer, but because it contains a recipe to derive an answer together with the reader's background knowledge. We formalise this task and introduce the challenging ShARC dataset with 32k task instances. \n\nThe goal is to answer questions by possibly asking follow-up questions first. We assume that the question does not provide enough information to be answered directly. However, a model can use the supporting rule text to infer what needs to be asked in order to determine the final answer. Concretely, The model must decide whether to answer with \"Yes\", \"No\", \"Irrelevant\", or to generate a follow-up question given rule text, a user scenario and a conversation history. Performance is measured with Micro and Macro Accuracy for \"Yes\"/\"No\"/\"Irrelevant\"/\"More\" classifications, and the quality of follow-up questions are measured with BLEU.\n\nThe public data, further task details and public leaderboard are available on the [ShARC Website](https://sharc-data.github.io/).", "dataset_links": [{"title": "ShARC", "url": "https://arxiv.org/abs/1809.01494"}, {"title": "ShARC Website", "url": "https://sharc-data.github.io/"}]}], "subtasks": [{"task": "Reading comprehension", "description": "Most current question answering datasets frame the task as reading comprehension where the question is about a paragraph\nor document and the answer often is a span in the document. The Machine Reading group\nat UCL also provides an [overview of reading comprehension tasks](https://uclmr.github.io/ai4exams/data.html).", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "CliCR", "description": "The [CliCR dataset](http://aclweb.org/anthology/N18-1140) is a gap-filling reading comprehension dataset consisting of around 100,000 queries and their associated documents. The dataset was built from clinical case reports, requiring the reader to answer the query with a medical problem/test/treatment entity. The abilities to perform bridging inferences and track objects have been found to be the most frequently required skills for successful answering.\n\nThe instructions for accessing the dataset, the processing scripts, the baselines and the adaptations of some neural models can be found [here](https://github.com/clips/clicr).\n\nExample:\n\n| Document  | Question | Answer |\n| ------------- | -----:| -----: |\n| We report a case of a 72-year-old Caucasian woman with pl-7 positive antisynthetase syndrome. Clinical presentation included interstitial lung disease, myositis, mechanic’s hands and dysphagia. As lung injury was the main concern, treatment consisted of prednisolone and cyclophosphamide. Complete remission with reversal of pulmonary damage was achieved, as reported by CT scan, pulmonary function tests and functional status. [...] | Therefore, in severe cases an aggressive treatment, combining ________ and glucocorticoids as used in systemic vasculitis, is suggested.| cyclophoshamide |", "dataset_links": [{"title": "CliCR dataset", "url": "http://aclweb.org/anthology/N18-1140"}, {"title": "here", "url": "https://github.com/clips/clicr"}], "sota": {"metrics": ["F1"], "rows": [{"model_name": "Gated-Attention Reader", "metrics": {"F1": "33.9"}, "paper_title": "CliCR: A Dataset of Clinical Case Reports for Machine Reading Comprehension", "paper_url": "http://aclweb.org/anthology/N18-1140"}, {"model_name": "Stanford Attentive Reader", "metrics": {"F1": "27.2"}, "paper_title": "CliCR: A Dataset of Clinical Case Reports for Machine Reading Comprehension", "paper_url": "http://aclweb.org/anthology/N18-1140"}]}}, {"dataset": "CNN / Daily Mail", "description": "The [CNN / Daily Mail dataset](https://arxiv.org/abs/1506.03340) is a Cloze-style reading comprehension dataset\ncreated from CNN and Daily Mail news articles using heuristics. [Close-style](https://en.wikipedia.org/wiki/Cloze_test)\nmeans that a missing word has to be inferred. In this case, \"questions\" were created by replacing entities\nfrom bullet points summarizing one or several aspects of the article. Coreferent entities have been replaced with an\nentity marker @entityn where n is a distinct index.\nThe model is tasked to infer the missing entity\nin the bullet point based on the content of the corresponding article and models are evaluated based on\ntheir accuracy on the test set.\n\n|         | CNN | Daily Mail |\n| ------------- | -----:| -----: |\n| # Train | 380,298 | 879,450 |\n| # Dev | 3,924 | 64,835 |\n| # Test | 3,198 | 53,182 |\n\nExample:\n\n| Passage  | Question | Answer |\n| ------------- | -----:| -----: |\n| ﻿( @entity4 ) if you feel a ripple in the force today , it may be the news that the official @entity6 is getting its first gay character . according to the sci-fi website @entity9 , the upcoming novel \" @entity11 \" will feature a capable but flawed @entity13 official named @entity14 who \" also happens to be a lesbian . \" the character is the first gay figure in the official @entity6 -- the movies , television shows , comics and books approved by @entity6 franchise owner @entity22 -- according to @entity24 , editor of \" @entity6 \" books at @entity28 imprint @entity26 . | characters in \" @placeholder \" movies have gradually become more diverse | @entity6 |", "dataset_links": [{"title": "CNN / Daily Mail dataset", "url": "https://arxiv.org/abs/1506.03340"}, {"title": "Close-style", "url": "https://en.wikipedia.org/wiki/<PERSON><PERSON><PERSON>_test"}], "sota": {"metrics": ["CNN", "Daily Mail"], "rows": [{"model_name": "", "metrics": {"CNN": "77.9", "Daily Mail": "80.9"}, "paper_title": "Gated-Attention Readers for Text Comprehension", "paper_url": "http://aclweb.org/anthology/P17-1168"}, {"model_name": "", "metrics": {"CNN": "76.9", "Daily Mail": "79.6"}, "paper_title": "Bidirectional Attention Flow for Machine Comprehension", "paper_url": "https://arxiv.org/pdf/1611.01603.pdf"}, {"model_name": "", "metrics": {"CNN": "74.4", "Daily Mail": "-"}, "paper_title": "Attention-over-Attention Neural Networks for Reading Comprehension", "paper_url": "http://aclweb.org/anthology/P17-1055"}, {"model_name": "Neural net", "metrics": {"CNN": "72.4", "Daily Mail": "75.8"}, "paper_title": "A Thorough Examination of the CNN/Daily Mail Reading Comprehension Task", "paper_url": "https://www.aclweb.org/anthology/P16-1223"}, {"model_name": "Classifier", "metrics": {"CNN": "67.9", "Daily Mail": "68.3"}, "paper_title": "A Thorough Examination of the CNN/Daily Mail Reading Comprehension Task", "paper_url": "https://www.aclweb.org/anthology/P16-1223"}, {"model_name": "Impatient Reader", "metrics": {"CNN": "63.8", "Daily Mail": "68.0"}, "paper_title": "Teaching Machines to Read and Comprehend", "paper_url": "https://arxiv.org/abs/1506.03340"}]}}, {"dataset": "CoQA", "description": "[CoQA](https://arxiv.org/abs/1808.07042) is a large-scale dataset for building Conversational Question Answering systems. \nCoQA contains 127,000+ questions with answers collected from 8000+ conversations.\nEach conversation is collected by pairing two crowdworkers to chat about a passage in the form of questions and answers.\n\nThe data and public leaderboard are available [here](https://stanfordnlp.github.io/coqa/).", "dataset_links": [{"title": "CoQA", "url": "https://arxiv.org/abs/1808.07042"}, {"title": "here", "url": "https://stanfordnlp.github.io/coqa/"}]}, {"dataset": "HotpotQA", "description": "HotpotQA is a dataset with 113k Wikipedia-based question-answer pairs. Questions require \nfinding and reasoning over multiple supporting documents and are not constrained to any pre-existing knowledge bases.\nSentence-level supporting facts are available.\n\nThe data and public leaderboard are available from the [HotpotQA website](https://hotpotqa.github.io/).", "dataset_links": [{"title": "HotpotQA website", "url": "https://hotpotqa.github.io/"}]}, {"dataset": "MS MARCO", "description": "[MS MARCO](http://www.msmarco.org/dataset.aspx) aka Human Generated MAchine\nReading COmprehension Dataset, is designed and developed by Microsoft AI & Research. [Link to paper](https://arxiv.org/abs/1611.09268)\n- The questions are obtained from real anonymized user queries.\n- The answers are human generated. The context passages from which the answers are obtained are extracted from real documents using the latest Bing search engine.\n- The data set contains 100,000 queries and a subset of them contain multiple answers, and aim to release 1M queries in the future.  \n\nThe leaderboards for multiple tasks are available on the [MS MARCO leaderboard page](http://www.msmarco.org/leaders.aspx).", "dataset_links": [{"title": "MS MARCO", "url": "http://www.msmarco.org/dataset.aspx"}, {"title": "Link to paper", "url": "https://arxiv.org/abs/1611.09268"}, {"title": "MS MARCO leaderboard page", "url": "http://www.msmarco.org/leaders.aspx"}]}, {"dataset": "MultiRC", "description": "MultiRC (Multi-Sentence Reading Comprehension) is a dataset of short paragraphs and multi-sentence questions that can be answered from the content of the paragraph.\nWe have designed the dataset with three key challenges in mind:\n - The number of correct answer-options for each question is not pre-specified. This removes the over-reliance of current approaches on answer-options and forces them to decide on the correctness of each candidate answer independently of others. In other words, unlike previous work, the task here is not to simply identify the best answer-option, but to evaluate the correctness of each answer-option individually.\n - The correct answer(s) is not required to be a span in the text.\n - The paragraphs in our dataset have diverse provenance by being extracted from 7 different domains such as news, fiction, historical text etc., and hence are expected to be more diverse in their contents as compared to single-domain datasets.\n\nThe leaderboards for the dataset is available on the [MultiRC website](http://cogcomp.org/multirc/).", "dataset_links": [{"title": "MultiRC website", "url": "http://cogcomp.org/multirc/"}]}, {"dataset": "NewsQA", "description": "The [NewsQA dataset](https://arxiv.org/pdf/1611.09830.pdf) is a reading comprehension dataset of over 100,000\nhuman-generated question-answer pairs from over 10,000 news articles from CNN, with answers consisting of spans of text\nfrom the corresponding articles.\nSome challenging characteristics of this dataset are:\n- Answers are spans of arbitrary length;\n- Some questions have no answer in the corresponding article;\n- There are no candidate answers from which to choose.\nAlthough very similar to the SQuAD dataset, NewsQA offers a greater challenge to existing models at time of\nintroduction (eg. the paragraphs are longer than those in SQuAD). Models are evaluated based on F1 and Exact Match.\n\nExample:\n\n| Story  | Question | Answer |\n| ------------- | -----:| -----: |\n| MOSCOW, Russia (CNN) -- Russian space officials say the crew of the Soyuz space ship is resting after a rough ride back to Earth. A South Korean bioengineer was one of three people on board the Soyuz capsule. The craft carrying South Korea's first astronaut landed in northern Kazakhstan on Saturday, 260 miles (418 kilometers) off its mark, they said. Mission Control spokesman <PERSON><PERSON> said the condition of the crew -- South Korean bioengineer <PERSON>, American astronaut <PERSON> and Russian flight engineer <PERSON> -- was satisfactory, though the three had been subjected to severe G-forces during the re-entry. [...] | Where did the Soyuz capsule land? | northern Kazakhstan |\n\nThe dataset can be downloaded [here](https://github.com/Maluuba/newsqa).", "dataset_links": [{"title": "NewsQA dataset", "url": "https://arxiv.org/pdf/1611.09830.pdf"}, {"title": "here", "url": "https://github.com/Maluuba/newsqa"}], "sota": {"metrics": ["F1", "EM"], "rows": [{"model_name": "DecaProp", "metrics": {"F1": "66.3", "EM": "53.1"}, "paper_title": "Densely Connected Attention Propagation for Reading Comprehension", "paper_url": "https://arxiv.org/abs/1811.04210"}, {"model_name": "AMANDA", "metrics": {"F1": "63.7", "EM": "48.4"}, "paper_title": "A Question-Focused Multi-Factor Attention Network for Question Answering", "paper_url": "https://arxiv.org/abs/1801.08290"}, {"model_name": "MINIMAL(Dyn)", "metrics": {"F1": "63.2", "EM": "50.1"}, "paper_title": "Efficient and Robust Question Answering from Minimal Context over Documents", "paper_url": "https://arxiv.org/abs/1805.08092"}, {"model_name": "FastQAExt", "metrics": {"F1": "56.1", "EM": "43.7"}, "paper_title": "Making Neural QA as Simple as Possible but not Simpler", "paper_url": "https://arxiv.org/abs/1703.04816"}]}}, {"dataset": "QAngaroo", "description": "[QAngaroo](http://qangaroo.cs.ucl.ac.uk/index.html) is a set of two reading comprehension datasets,\nwhich require multiple steps of inference that combine facts from multiple documents. The first dataset, WikiHop\nis open-domain and focuses on Wikipedia articles. The second dataset, MedHop is based on paper abstracts from\nPubMed.\n\nThe leaderboards for both datasets are available on the [QAngaroo website](http://qangaroo.cs.ucl.ac.uk/leaderboard.html).", "dataset_links": [{"title": "QAngaroo", "url": "http://qangaroo.cs.ucl.ac.uk/index.html"}, {"title": "QAngaroo website", "url": "http://qangaroo.cs.ucl.ac.uk/leaderboard.html"}]}, {"dataset": "QuAC", "description": "Question Answering in Context (QuAC) is a dataset for modeling, understanding, and participating in information seeking dialog.\nData instances consist of an interactive dialog between two crowd workers:\n(1) a student who poses a sequence of freeform questions to learn as much as possible about a hidden Wikipedia text,\nand (2) a teacher who answers the questions by providing short excerpts (spans) from the text.\n\nThe leaderboard and data are available on the [QuAC website](http://quac.ai/).", "dataset_links": [{"title": "QuAC website", "url": "http://quac.ai/"}]}, {"dataset": "RACE", "description": "The [RACE dataset](https://arxiv.org/abs/1704.04683) is a reading comprehension dataset\ncollected from English examinations in China, which are designed for middle school and high school students.\nThe dataset contains more than 28,000 passages and nearly 100,000 questions and can be\ndownloaded [here](http://www.cs.cmu.edu/~glai1/data/race/). Models are evaluated based on accuracy\non middle school examinations (RACE-m), high school examinations (RACE-h), and on the total dataset (RACE).", "dataset_links": [{"title": "RACE dataset", "url": "https://arxiv.org/abs/1704.04683"}, {"title": "here", "url": "http://www.cs.cmu.edu/~glai1/data/race/"}], "sota": {"metrics": ["RACE-m", "RACE-h", "RACE"], "rows": [{"model_name": "Finetuned Transformer LM", "metrics": {"RACE-m": "62.9", "RACE-h": "57.4", "RACE": "59.0"}, "paper_title": "Improving Language Understanding by Generative Pre-Training", "paper_url": "https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/language-unsupervised/language_understanding_paper.pdf"}, {"model_name": "BiAttention MRU", "metrics": {"RACE-m": "60.2", "RACE-h": "50.3", "RACE": "53.3"}, "paper_title": "Multi-range Reasoning for Machine Comprehension", "paper_url": "https://arxiv.org/abs/1803.09074"}]}}, {"dataset": "SQuAD", "description": "The [Stanford Question Answering Dataset (SQuAD)](https://arxiv.org/abs/1606.05250)\nis a reading comprehension dataset, consisting of questions posed by crowdworkers\non a set of Wikipedia articles. The answer to every question is a segment of text (a span)\nfrom the corresponding reading passage. Recently, [SQuAD 2.0](https://arxiv.org/abs/1806.03822)\nhas been released, which includes unanswerable questions.\n\nThe public leaderboard is available on the [SQuAD website](https://rajpurkar.github.io/SQuAD-explorer/).", "dataset_links": [{"title": "Stanford Question Answering Dataset (SQuAD)", "url": "SQuAD)](https://arxiv.org/abs/1606.05250"}, {"title": "SQuAD 2.0", "url": "https://arxiv.org/abs/1806.03822"}, {"title": "SQuAD website", "url": "https://rajpurkar.github.io/SQuAD-explorer/"}]}, {"dataset": "<PERSON>", "description": "The [Story Cloze Test](http://aclweb.org/anthology/W17-0906.pdf) is a dataset for\nstory understanding that provides systems with four-sentence stories and two possible\nendings. The systems must then choose the correct ending to the story.", "dataset_links": [{"title": "<PERSON>", "url": "http://aclweb.org/anthology/W17-0906.pdf"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "Finetuned Transformer LM", "metrics": {"Accuracy": "86.5"}, "paper_title": "Improving Language Understanding by Generative Pre-Training", "paper_url": "https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/language-unsupervised/language_understanding_paper.pdf", "code_links": []}, {"model_name": "<PERSON> et al.", "metrics": {"Accuracy": "78.7"}, "paper_title": "Narrative Modeling with Memory Chains and Semantic Supervision", "paper_url": "http://aclweb.org/anthology/P18-2045", "code_links": [{"title": "Official", "url": "https://github.com/liufly/narrative-modeling"}]}, {"model_name": "Hidden Coherence Model", "metrics": {"Accuracy": "77.6"}, "paper_title": "Story Comprehension for Predicting What Happens Next", "paper_url": "http://aclweb.org/anthology/D17-1168", "code_links": []}, {"model_name": "val-LS-skip", "metrics": {"Accuracy": "76.5"}, "paper_title": "A Simple and Effective Approach to the Story Cloze Test", "paper_url": "http://aclweb.org/anthology/N18-2015", "code_links": []}]}}, {"dataset": "RecipeQA", "description": "[RecipeQA](https://arxiv.org/abs/1809.00812) is a dataset for multimodal comprehension of cooking recipes. It consists of over 36K question-answer pairs automatically generated from approximately 20K unique recipes with step-by-step instructions and images. Each question in RecipeQA involves multiple modalities such as titles, descriptions or images, and working towards an answer requires (i) joint understanding of images and text, (ii) capturing the temporal flow of events, and (iii) making sense of procedural knowledge.\n\nThe public leaderboard is available on the [RecipeQA website](https://hucvl.github.io/recipeqa/).", "dataset_links": [{"title": "RecipeQA", "url": "https://arxiv.org/abs/1809.00812) is a dataset for multimodal comprehension of cooking recipes. It consists of over 36K question-answer pairs automatically generated from approximately 20K unique recipes with step-by-step instructions and images. Each question in RecipeQA involves multiple modalities such as titles, descriptions or images, and working towards an answer requires (i) joint understanding of images and text, (ii) capturing the temporal flow of events, and (iii"}, {"title": "RecipeQA website", "url": "https://hucvl.github.io/recipeqa/"}]}, {"dataset": "NarrativeQA", "description": "[NarrativeQA](https://arxiv.org/abs/1712.07040) is a dataset built to encourage deeper comprehension of language. This dataset involves reasoning over reading entire books or movie scripts. This dataset contains approximately 45K question answer pairs in free form text. There are two modes of this dataset (1) reading comprehension over summaries and (2) reading comprehension over entire books/scripts. \n\n\n*Note that the above is for the Summary setting. There are no official published results for reading over entire books/stories except for the original paper.", "dataset_links": [{"title": "NarrativeQA", "url": "https://arxiv.org/abs/1712.07040) is a dataset built to encourage deeper comprehension of language. This dataset involves reasoning over reading entire books or movie scripts. This dataset contains approximately 45K question answer pairs in free form text. There are two modes of this dataset (1) reading comprehension over summaries and (2"}], "sota": {"metrics": ["BLEU-1", "BLEU-4", "METEOR", "Rouge-L"], "rows": [{"model_name": "DecaProp", "metrics": {"BLEU-1": "44.35", "BLEU-4": "27.61", "METEOR": "21.80", "Rouge-L": "44.69"}, "paper_title": "Densely Connected Attention Propagation for Reading Comprehension", "paper_url": "https://arxiv.org/abs/1811.04210", "code_links": [{"title": "official", "url": "https://github.com/vanzytay/NIPS2018_DECAPROP"}]}, {"model_name": "BiAttention + DCU-LSTM", "metrics": {"BLEU-1": "36.55", "BLEU-4": "19.79", "METEOR": "17.87", "Rouge-L": "41.44"}, "paper_title": "Multi-Granular Sequence Encoding via Dilated Compositional Units for Reading Comprehension", "paper_url": "http://aclweb.org/anthology/D18-1238", "code_links": []}, {"model_name": "BiDAF", "metrics": {"BLEU-1": "33.45", "BLEU-4": "15.69", "METEOR": "15.68", "Rouge-L": "36.74"}, "paper_title": "Bidirectional Attention Flow for Machine Comprehension", "paper_url": "https://arxiv.org/abs/1611.01603", "code_links": []}]}}]}, {"task": "Open-domain Question Answering", "description": "", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "<PERSON><PERSON><PERSON><PERSON>", "description": "[Du<PERSON>eader](https://ai.baidu.com/broad/subordinate?dataset=dureader) is a large-scale, open-domain Chinese machine reading comprehension (MRC) dataset, designed to address real-world MRC. [Link to paper](https://arxiv.org/pdf/1711.05073.pdf) \n\nDuReader has three advantages over other MRC datasets: \n- (1) data sources: questions and documents are based on Baidu Search and Baidu Zhidao; answers are manually generated. \n- (2) question types: it provides rich annotations for more question types, especially yes-no and opinion questions, that leaves more opportunity for the research community. \n- (3) scale: it contains 300K questions, 660K answers and 1.5M documents; it is the largest Chinese MRC dataset so far. \n\nTo help the community make these improvements, both the [dataset](https://ai.baidu.com/broad/download?dataset=dureader) of DuReader and [baseline systems](https://github.com/baidu/DuReader) have been posted online. \n\nThe [leaderboard](https://ai.baidu.com/broad/leaderboard?dataset=dureader) is avaiable on <PERSON><PERSON><PERSON><PERSON> page.", "dataset_links": [{"title": "Du<PERSON><PERSON><PERSON>](https://ai.baidu.com/broad/subordinate?dataset=dureader) is a large-scale, open-domain Chinese machine reading comprehension (MRC) dataset, designed to address real-world MRC. [Link to paper", "url": "https://ai.baidu.com/broad/subordinate?dataset=dureader) is a large-scale, open-domain Chinese machine reading comprehension (MRC) dataset, designed to address real-world MRC. [Link to paper](https://arxiv.org/pdf/1711.05073.pdf"}, {"title": "dataset](https://ai.baidu.com/broad/download?dataset=dureader) of DuReader and [baseline systems", "url": "https://ai.baidu.com/broad/download?dataset=dureader) of DuReader and [baseline systems](https://github.com/baidu/DuReader"}, {"title": "leaderboard", "url": "https://ai.baidu.com/broad/leaderboard?dataset=dureader"}]}, {"dataset": "Quasar", "description": "[Quasar](https://arxiv.org/abs/1707.03904) is a dataset for open-domain question answering. It includes two parts: (1) The Quasar-S dataset consists of 37,000 cloze-style queries constructed from definitions of software entity tags on the popular website Stack Overflow. (2) The Quasar-T dataset consists of 43,000 open-domain trivia questions and their answers obtained from various internet sources.", "dataset_links": [{"title": "Quasar", "url": "https://arxiv.org/abs/1707.03904) is a dataset for open-domain question answering. It includes two parts: (1) The Quasar-S dataset consists of 37,000 cloze-style queries constructed from definitions of software entity tags on the popular website Stack Overflow. (2"}], "sota": {"metrics": ["EM (Quasar-T)", "F1 (Quasar-T)"], "rows": [{"model_name": "Denoising QA", "metrics": {"EM (Quasar-T)": "42.2", "F1 (Quasar-T)": "49.3"}, "paper_title": "Denoising Distantly Supervised Open-Domain Question Answering", "paper_url": "http://aclweb.org/anthology/P18-1161", "code_links": [{"title": "official", "url": "https://github.com/thunlp/OpenQA"}]}, {"model_name": "DecaProp", "metrics": {"EM (Quasar-T)": "38.6", "F1 (Quasar-T)": "46.9"}, "paper_title": "Densely Connected Attention Propagation for Reading Comprehension", "paper_url": "https://arxiv.org/abs/1811.04210", "code_links": [{"title": "official", "url": "https://github.com/vanzytay/NIPS2018_DECAPROP"}]}, {"model_name": "R^3", "metrics": {"EM (Quasar-T)": "35.3", "F1 (Quasar-T)": "41.7"}, "paper_title": "R^3: <PERSON>inforce<PERSON> <PERSON>er-Reader for Open-Domain Question Answering", "paper_url": "https://aaai.org/ocs/index.php/AAAI/AAAI18/paper/view/16712/16165", "code_links": [{"title": "official", "url": "https://github.com/shuohangwang/mprc"}]}, {"model_name": "BiDAF", "metrics": {"EM (Quasar-T)": "25.9", "F1 (Quasar-T)": "28.5"}, "paper_title": "Bidirectional Attention Flow for Machine Comprehensio", "paper_url": "https://arxiv.org/abs/1611.01603", "code_links": [{"title": "official", "url": "https://github.com/allenai/bi-att-flow"}]}, {"model_name": "GA", "metrics": {"EM (Quasar-T)": "26.4", "F1 (Quasar-T)": "26.4"}, "paper_title": "Gated-Attention Readers for Text Comprehension", "paper_url": "https://arxiv.org/pdf/1606.01549", "code_links": []}]}}, {"dataset": "SearchQA", "description": "[SearchQA](https://arxiv.org/abs/1704.05179) was constructed to reflect a full pipeline of general question-answering. SearchQA consists of more than 140k question-answer pairs with each pair having 49.6 snippets on average. Each question-answer-context tuple of the SearchQA comes with additional meta-data such as the snippet's URL.\n\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "SearchQA", "url": "https://arxiv.org/abs/1704.05179"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Unigram Acc", "N-gram F1", "EM", "F1"], "rows": [{"model_name": "DecaProp", "metrics": {"Unigram Acc": "62.2", "N-gram F1": "70.8", "EM": "56.8", "F1": "63.6"}, "paper_title": "Densely Connected Attention Propagation for Reading Comprehension", "paper_url": "https://arxiv.org/abs/1811.04210", "code_links": [{"title": "official", "url": "https://github.com/vanzytay/NIPS2018_DECAPROP"}]}, {"model_name": "Denoising QA", "metrics": {"Unigram Acc": "-", "N-gram F1": "-", "EM": "58.8", "F1": "64.5"}, "paper_title": "Denoising Distantly Supervised Open-Domain Question Answering", "paper_url": "http://aclweb.org/anthology/P18-1161", "code_links": [{"title": "official", "url": "https://github.com/thunlp/OpenQA"}]}, {"model_name": "R^3", "metrics": {"Unigram Acc": "-", "N-gram F1": "-", "EM": "49.0", "F1": "55.3"}, "paper_title": "R^3: <PERSON>inforce<PERSON> <PERSON>er-Reader for Open-Domain Question Answering", "paper_url": "https://aaai.org/ocs/index.php/AAAI/AAAI18/paper/view/16712/16165", "code_links": [{"title": "official", "url": "https://github.com/shuohangwang/mprc"}]}, {"model_name": "Bi-Attention + DCU-LSTM", "metrics": {"Unigram Acc": "49.4", "N-gram F1": "59.5", "EM": "-", "F1": "-"}, "paper_title": "Multi-Granular Sequence Encoding via Dilated Compositional Units for Reading Comprehension", "paper_url": "http://aclweb.org/anthology/D18-1238", "code_links": []}, {"model_name": "AMANDA", "metrics": {"Unigram Acc": "46.8", "N-gram F1": "56.6", "EM": "-", "F1": "-"}, "paper_title": "A Question-Focused Multi-Factor Attention Network for Question Answering", "paper_url": "https://arxiv.org/abs/1801.08290", "code_links": [{"title": "official", "url": "https://github.com/nusnlp/amanda"}]}, {"model_name": "", "metrics": {"Unigram Acc": "46.8", "N-gram F1": "53.4", "EM": "-", "F1": "-"}, "paper_title": "Focused Hierarchical RNNs for Conditional Sequence Processing", "paper_url": "http://proceedings.mlr.press/v80/ke18a/ke18a.pdf", "code_links": []}, {"model_name": "ASR", "metrics": {"Unigram Acc": "41.3", "N-gram F1": "22.8", "EM": "-", "F1": "-"}, "paper_title": "Text Understanding with the Attention Sum Reader Network", "paper_url": "https://arxiv.org/abs/1603.01547", "code_links": []}]}}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Entity Linking", "description": "", "subtasks": [{"task": "Task", "description": "Entity Linking (EL) is the task of recognizing (cf. [Named Entity Recognition](named_entity_recognition.md)) and disambiguating (Named Entity Disambiguation) named entities to a knowledge base (e.g. Wikidata, DBpedia, or YAGO). It is sometimes also simply known as Named Entity Recognition and Disambiguation.\n\nEL can be split into two classes of approaches:\n* *End-to-End*: processing a piece of text to extract the entities (i.e. Named Entity Recognition) and then disambiguate these extracted entities to the correct entry in a given knowledge base (e.g. Wikidata, DBpedia, YAGO).\n* *Disambiguation-Only*: contrary to the first approach, this one directly takes gold standard named entities as input and only disambiguates them to the correct entry in a given knowledge base.\n\nExample:\n\n| <PERSON> | <PERSON> | was | born | in | Hawaï |\n| --- | ---| --- | --- | --- | --- |\n| https://en.wikipedia.org/wiki/<PERSON>_Obama | https://en.wikipedia.org/wiki/<PERSON>_<PERSON> | O | O | O | https://en.wikipedia.org/wiki/Hawaii |\n\nMore in details can be found in this [survey](http://dbgroup.cs.tsinghua.edu.cn/wangjy/papers/TKDE14-entitylinking.pdf).", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Current SOTA", "description": "[<PERSON><PERSON>][<PERSON><PERSON>] is the current SOTA in Cross-lingual Entity Linking. They construct a type system, and use it to constrain the outputs of a neural network to respect the symbolic structure. They achieve this by reformulating the design problem into a mixed integer problem: create a type system and subsequently train a neural network with it. They propose a 2-step algorithm: 1) heuristic search or stochastic optimization over discrete variables that define a type system\ninformed by an Oracle and a Learnability heuristic, 2) gradient descent to fit classifier parameters. They apply DeepType to the problem of Entity Linking on three standard datasets (i.e. WikiDisamb30, CoNLL (YAGO), TAC KBP 2010) and find that it outperforms all existing solutions by a wide margin, including approaches that rely on a human-designed type system or recent deep learning-based entity embeddings, while explicitly using symbolic information lets it integrate new entities without retraining.", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Evaluation", "description": "", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Metrics", "description": ""}, {"dataset": "Datasets", "description": ""}, {"dataset": "Platforms", "description": "Evaluating Entity Linking systems in a manner that allows for direct comparison of performance can be difficult. The precise definition of a \"correct\" annotation can be somewhat subjective and it is easy to make mistakes. To provide a simple example, given the input surface form **\"Tom Waits\"**, an evaluation dataset might record the dbpedia resource `http://dbpedia.org/resource/<PERSON>_<PERSON>` as the correct referent. Yet an annotation system which returns a reference to `http://dbpedia.org/resource/PEHDTSCKJBMA` has technically provided an appropriate annotation as this resource is a redirect to `http://dbpedia.org/resource/<PERSON>_<PERSON>s`. Alternatively if evaluating an End-to-End EL system, then accuracy with respect to word boundaries must be considered e.g. if a system only annotates **\"<PERSON>\"** with the URI `http://dbpedia.org/resource/<PERSON>_<PERSON>` in the surface form **\"Barack Obama\"**, then is the system correct or incorrect in its annotation?\n\nFurthermore, the performance of an EL system can be strongly affected by the nature of the content on which the evaluation is performed e.g. news content versus Tweets. Hence comparing the relative performance of two EL systems which have been tested on two different corpora can be fallicious. Rather than allowing these little subjective points to creep into the evaluation of EL systems, it is better to make use of a standard evaluation platform where these assumptions are known and made explicit in the configuration of the experiment.\n\n[GERBIL][GERBIL], developed by [AKSW][AKSW] is an evaluation platform that is based on the [BAT framework][Cornolti]. It defines a number of standard experiments which may be run for any given EL service. These experiment types determine how strict the evaluation is with respect to measures such as word boundary alignment and also dictates how much responsibility is assigned to the EL service with respect to Entity Recognition, etc. GERBIL hosts 38 evaluation datasets obtained from a variety of different EL challenges. At present it also has hooks for 17 different EL services which may be included in an experiment.\n\nGERBIL may be used to test your own EL system either by downloading the source code and deploying GERBAL locally, or by making your service available on the web and giving GERBIL a link to your API endpoint. The only condition is that your API must accept input and respond with output in [NIF][NIF] format. It is also possible to upload your own evaluation dataset if you would like to test these services on your own content. Note the dataset must also be in NIF format. The [DBpedia Spotlight evaluation dataset][SpotlightEvaluation] is a good example of how to structure your content.\n\nGERBIL does have a number of shortcomings, the most notable of which are:\n1. There is no way to view the annotations returned by each system you test. These are handled internally by GERBIL and then discarded. This can make it difficult to determine the source of error with an EL system.\n2. There is no way to observe the candidate list considered for each surface form. This is, of course, a standard problem with any third party EL API, but if one is conducting a detailed investigation into the performance of an EL system, it is important to know if the source of error was the EL algorithm itself, or the candidate retrieval process which failed to identify the correct referent as a candidate. This was listed as an important consideration by [Hachey et al][Hachey].\n\nNevertheless, GERBIL is an excellent resource for standardising how EL systems are tested and compared. It is also a good starting point for anyone new to Entity Linking as it contains links to a wide variety of EL resources. For more information, see the research paper by [[Usbeck]](http://svn.aksw.org/papers/2015/WWW_GERBIL/public.pdf).", "dataset_links": [{"title": "[<PERSON><PERSON>]", "url": "http://svn.aksw.org/papers/2015/WWW_GERBIL/public.pdf"}]}]}, {"task": "References", "description": "[<PERSON><PERSON><PERSON>] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Robust Disambiguation of Named Entities in Text. EMNLP 2011. http://www.aclweb.org/anthology/D11-1072\n\n[CoNLL] <PERSON> and <PERSON><PERSON>. Introduction to the CoNLL-2003 Shared Task: Language-Independent Named Entity Recognition. CoNLL 2003. http://www.aclweb.org/anthology/W03-0419.pdf\n\n[<PERSON><PERSON>] <PERSON><PERSON> et al. GERBIL - General Entity Annotator Benchmarking Framework. WWW 2015. http://svn.aksw.org/papers/2015/WWW_GERBIL/public.pdf\n\n[Go back to the README](../README.md)\n\n[Sil]: https://www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/view/16501/16101 \"Neural Cross-Lingual Entity Linking\"\n[Shen]: http://dbgroup.cs.tsinghua.edu.cn/wangjy/papers/TKDE14-entitylinking.pdf \"Entity Linking with a Knowledge Base: Issues, Techniques, and Solutions\"\n[AIDACoNLLYAGO]: https://www.mpi-inf.mpg.de/departments/databases-and-information-systems/research/yago-naga/aida/downloads/ \"AIDA CoNLL-YAGO Dataset\"\n[YAGO2]: http://yago-knowledge.org/ \"YAGO2\"\n[Wikipedia]: https://en.wikipedia.org/ \"Wikipedia\"\n[Freebase]: http://wiki.freebase.com/wiki/Machine_ID \"Freebase\"\n[Radhakrishnan]: http://aclweb.org/anthology/N18-1167 \"ELDEN: Improved Entity Linking using Densified Knowledge Graphs\"\n[Le]: https://arxiv.org/abs/1804.10637\n[NIF]: http://persistence.uni-leipzig.org/nlp2rdf/ \"NLP Interchange Formt\"\n[SpotlightEvaluation]: http://apps.yovisto.com/labs/ner-benchmarks/data/dbpedia-spotlight-nif.ttl \"GERBIL DBpedia Spotlight Dataset\"\n[Cornolti]: https://static.googleusercontent.com/media/research.google.com/en//pubs/archive/40749.pdf \"A Framework for Benchmarking Entity-Annotation Systems\"\n[GERBIL]: http://aksw.org/Projects/GERBIL.html \"General Entity Annotator Benchmarking framework\"\n[AKSW]: http://aksw.org/About.html \"Agile Knowledge Engineering and Semantic Web\"\n[Hachey]: http://benhachey.info/pubs/hachey-aij12-evaluating.pdf \"Evaluating Entity Linking with Wikipedia\"\n[Raiman]: https://arxiv.org/pdf/1802.01021.pdf \"DeepType: Multilingual Entity Linking by Neural Type System Evolution\"", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Part-of-speech tagging", "description": "Part-of-speech tagging (POS tagging) is the task of tagging a word in a text with its part of speech.\nA part of speech is a category of words with similar grammatical properties. Common English\nparts of speech are noun, verb, adjective, adverb, pronoun, preposition, conjunction, etc.\n\nExample: \n\n| Vinken | , | 61 | years | old |\n| --- | ---| --- | --- | --- |\n| NNP | , | CD | NNS | JJ |", "datasets": [{"dataset": "Penn Treebank", "description": "A standard dataset for POS tagging is the Wall Street Journal (WSJ) portion of the Penn Treebank, containing 45 \ndifferent POS tags. Sections 0-18 are used for training, sections 19-21 for development, and sections \n22-24 for testing. Models are evaluated based on accuracy.", "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "Meta BiLSTM", "metrics": {"Accuracy": "97.96"}, "paper_title": "Morphosyntactic Tagging with a Meta-BiLSTM Model over Context Sensitive Token Encodings", "paper_url": "https://arxiv.org/abs/1805.08237", "code_links": []}, {"model_name": "Flair embeddings", "metrics": {"Accuracy": "97.85"}, "paper_title": "Contextual String Embeddings for Sequence Labeling", "paper_url": "http://aclweb.org/anthology/C18-1139", "code_links": [{"title": "Flair framework", "url": "https://github.com/zalandoresearch/flair"}]}, {"model_name": "Char Bi-LSTM", "metrics": {"Accuracy": "97.78"}, "paper_title": "Finding Function in Form: Compositional Character Models for Open Vocabulary Word Representation", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1176.pdf", "code_links": []}, {"model_name": "Adversarial Bi-LSTM", "metrics": {"Accuracy": "97.59"}, "paper_title": "Robust Multilingual Part-of-Speech Tagging via Adversarial Training", "paper_url": "https://arxiv.org/abs/1711.04903", "code_links": []}, {"model_name": "<PERSON> et al.", "metrics": {"Accuracy": "97.55"}, "paper_title": "Transfer Learning for Sequence Tagging with Hierarchical Recurrent Networks", "paper_url": "https://arxiv.org/abs/1703.06345", "code_links": []}, {"model_name": "<PERSON> and <PERSON><PERSON>", "metrics": {"Accuracy": "97.55"}, "paper_title": "End-to-end Sequence Labeling via Bi-directional LSTM-CNNs-CRF", "paper_url": "https://arxiv.org/abs/1603.01354", "code_links": []}, {"model_name": "LM-LSTM-CRF", "metrics": {"Accuracy": "97.53"}, "paper_title": "Empowering Character-aware Sequence Labeling with Task-Aware Neural Language Model", "paper_url": "https://arxiv.org/pdf/1709.04109.pdf", "code_links": []}, {"model_name": "NCRF++", "metrics": {"Accuracy": "97.49"}, "paper_title": "NCRF++: An Open-source Neural Sequence Labeling Toolkit", "paper_url": "http://www.aclweb.org/anthology/P18-4013", "code_links": [{"title": "NCRF++", "url": "https://github.com/jiesutd/NCRFpp"}]}, {"model_name": "Feed Forward", "metrics": {"Accuracy": "97.4"}, "paper_title": "Supertagging with LSTMs", "paper_url": "https://aclweb.org/anthology/N/N16/N16-1027.pdf", "code_links": []}, {"model_name": "Bi-LSTM", "metrics": {"Accuracy": "97.36"}, "paper_title": "Finding Function in Form: Compositional Character Models for Open Vocabulary Word Representation", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1176.pdf", "code_links": []}, {"model_name": "Bi-LSTM", "metrics": {"Accuracy": "97.22"}, "paper_title": "Multilingual Part-of-Speech Tagging with Bidirectional Long Short-Term Memory Models and Auxiliary Loss", "paper_url": "https://arxiv.org/abs/1604.05529", "code_links": []}]}}, {"dataset": "Social media", "description": "The [<PERSON> (2011)](https://aclanthology.coli.uni-saarland.de/papers/D11-1141/d11-1141) dataset has become the benchmark for social media part-of-speech tagging. This is comprised of  some 50K tokens of English social media sampled in late 2011, and is tagged using an extended version of the PTB tagset.", "dataset_links": [{"title": "<PERSON> (2011)", "url": "2011)](https://aclanthology.coli.uni-saarland.de/papers/D11-1141/d11-1141"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "GATE", "metrics": {"Accuracy": "88.69"}, "paper_title": "Twitter Part-of-Speech Tagging for All: Overcoming Sparse and Noisy Data", "paper_url": "https://aclanthology.coli.uni-saarland.de/papers/R13-1026/r13-1026"}, {"model_name": "CMU", "metrics": {"Accuracy": "90.0 ± 0.5"}, "paper_title": "Improved Part-of-Speech Tagging for Online Conversational Text with Word Clusters", "paper_url": "http://www.cs.cmu.edu/~ark/TweetNLP/owoputi+etal.naacl13.pdf"}]}}, {"dataset": "UD", "description": "[Universal Dependencies (UD)](http://universaldependencies.org/) is a framework for \ncross-linguistic grammatical annotation, which contains more than 100 treebanks in over 60 languages.\nModels are typically evaluated based on the average test accuracy across 28 languages.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "Universal Dependencies (UD)", "url": "UD)](http://universaldependencies.org/"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Avg accuracy"], "rows": [{"model_name": "Adversarial Bi-LSTM", "metrics": {"Avg accuracy": "96.73"}, "paper_title": "Robust Multilingual Part-of-Speech Tagging via Adversarial Training", "paper_url": "https://arxiv.org/abs/1711.04903"}, {"model_name": "Bi-LSTM", "metrics": {"Avg accuracy": "96.40"}, "paper_title": "Multilingual Part-of-Speech Tagging with Bidirectional Long Short-Term Memory Models and Auxiliary Loss", "paper_url": "https://arxiv.org/abs/1604.05529"}, {"model_name": "Joint Bi-LSTM", "metrics": {"Avg accuracy": "95.55"}, "paper_title": "A Novel Neural Network Model for Joint POS Tagging and Graph-based Dependency Parsing", "paper_url": "https://arxiv.org/abs/1705.05952"}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Grammatical Error Correction", "description": "Grammatical Error Correction (GEC) is the task of correcting different kinds of errors in text such as spelling, punctuation, grammatical, and word choice errors. \n\nGEC is typically formulated as a sentence correction task. A GEC system takes a potentially erroneous sentence as input and is expected to transform it to its corrected version. See the example given below: \n\n| Input (Erroneous)          | Output (Corrected)     |\n| -------------------------  | ---------------------- |\n|She see <PERSON> is catched by policeman in park at last night. | She saw <PERSON> caught by a policeman in the park last night.|", "datasets": [{"dataset": "CoNLL-2014 Shared Task", "description": "The [CoNLL-2014 shared task test set](https://www.comp.nus.edu.sg/~nlp/conll14st/conll14st-test-data.tar.gz) is the most widely used dataset to benchmark GEC systems. The test set contains 1,312 English sentences with error annotations by 2 expert annotators. Models are evaluated with <PERSON><PERSON>atch scorer ([<PERSON><PERSON><PERSON><PERSON> and <PERSON>, 2012](http://www.aclweb.org/anthology/N12-1067)) which computes a span-based F<sub>β</sub>-score (β set to 0.5 to weight precision twice as recall).\n\nThe shared task setting restricts that systems use only publicly available datasets for training to ensure a fair comparison between systems. The highest published scores on the the CoNLL-2014 test set are given below. A distinction is made between papers that report results in the restricted CoNLL-2014 shared task setting of training using publicly-available training datasets only (_**Restricted**_) and those that made use of large, non-public datasets (_**Unrestricted**_).\n\n**Restricted**:\n\n\n**Unrestricted**:\n\n\n_**Restricted**_: uses only publicly available datasets. _**Unrestricted**_: uses non-public datasets.", "dataset_links": [{"title": "CoNLL-2014 shared task test set](https://www.comp.nus.edu.sg/~nlp/conll14st/conll14st-test-data.tar.gz) is the most widely used dataset to benchmark GEC systems. The test set contains 1,312 English sentences with error annotations by 2 expert annotators. Models are evaluated with MaxMatch scorer ([<PERSON><PERSON><PERSON><PERSON> and <PERSON>, 2012", "url": "https://www.comp.nus.edu.sg/~nlp/conll14st/conll14st-test-data.tar.gz) is the most widely used dataset to benchmark GEC systems. The test set contains 1,312 English sentences with error annotations by 2 expert annotators. Models are evaluated with MaxMatch scorer ([<PERSON><PERSON><PERSON><PERSON> and <PERSON>, 2012](http://www.aclweb.org/anthology/N12-1067)) which computes a span-based F<sub>β</sub>-score (β set to 0.5 to weight precision twice as recall"}], "subdatasets": [{"subdataset": "Restricted", "sota": {"metrics": ["F0.5"], "rows": [{"model_name": "CNN Seq2Seq + Quality Estimation", "metrics": {"F0.5": "56.52"}, "paper_title": "Neural Quality Estimation of Grammatical Error Correction", "paper_url": "http://aclweb.org/anthology/D18-1274", "code_links": [{"title": "Official", "url": "https://github.com/nusnlp/neuqe/"}]}, {"model_name": "SMT + BiGRU", "metrics": {"F0.5": "56.25"}, "paper_title": "Near Human-Level Performance in Grammatical Error Correction with Hybrid Machine Translation", "paper_url": "http://aclweb.org/anthology/N18-2046", "code_links": []}, {"model_name": "Transformer", "metrics": {"F0.5": "55.8"}, "paper_title": "Approaching Neural Grammatical Error Correction as a Low-Resource Machine Translation Task", "paper_url": "http://aclweb.org/anthology/N18-1055", "code_links": [{"title": "Official", "url": "https://github.com/grammatical/neural-naacl2018"}]}, {"model_name": "CNN Seq2Seq", "metrics": {"F0.5": "54.79"}, "paper_title": "A Multilayer Convolutional Encoder-Decoder Neural Network for Grammatical Error Correction", "paper_url": "https://www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/viewFile/17308/16137", "code_links": [{"title": "Official", "url": "https://github.com/nusnlp/mlconvgec2018"}]}]}}, {"subdataset": "Unrestricted", "sota": {"metrics": ["F0.5"], "rows": [{"model_name": "CNN Seq2Seq + Fluency Boost", "metrics": {"F0.5": "61.34"}, "paper_title": "Reaching Human-level Performance in Automatic Grammatical Error Correction: An Empirical Study", "paper_url": "https://arxiv.org/pdf/1807.01270.pdf", "code_links": []}]}}]}, {"dataset": "CoNLL-2014 10 Annotations", "description": "[<PERSON> and <PERSON>, 2015](http://aclweb.org/anthology/P15-1068) released 8 additional annotations (in addition to the two official annotations) for the CoNLL-2014 shared task test set ([link](http://www.comp.nus.edu.sg/~nlp/sw/10gec_annotations.zip)).\n\n**Restricted**:\n\n\n**Unrestricted**:\n\n\n_**Restricted**_: uses only publicly available datasets. _**Unrestricted**_: uses non-public datasets.", "dataset_links": [{"title": "<PERSON> and <PERSON>, 2015](http://aclweb.org/anthology/P15-1068) released 8 additional annotations (in addition to the two official annotations) for the CoNLL-2014 shared task test set ([link", "url": "http://aclweb.org/anthology/P15-1068) released 8 additional annotations (in addition to the two official annotations) for the CoNLL-2014 shared task test set ([link](http://www.comp.nus.edu.sg/~nlp/sw/10gec_annotations.zip)"}], "subdatasets": [{"subdataset": "Restricted", "sota": {"metrics": ["F0.5"], "rows": [{"model_name": "SMT + BiGRU", "metrics": {"F0.5": "72.04"}, "paper_title": "Near Human-Level Performance in Grammatical Error Correction with Hybrid Machine Translation", "paper_url": "http://aclweb.org/anthology/N18-2046", "code_links": []}, {"model_name": "CNN Seq2Seq", "metrics": {"F0.5": "70.14 (measured by <PERSON><PERSON> et al., 2018)"}, "paper_title": "A Multilayer Convolutional Encoder-Decoder Neural Network for Grammatical Error Correction", "paper_url": "https://www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/viewFile/17308/16137", "code_links": [{"title": "Official", "url": "https://github.com/nusnlp/mlconvgec2018"}]}]}}, {"subdataset": "Unrestricted", "sota": {"metrics": ["F0.5"], "rows": [{"model_name": "CNN Seq2Seq + Fluency Boost", "metrics": {"F0.5": "76.88"}, "paper_title": "Reaching Human-level Performance in Automatic Grammatical Error Correction: An Empirical Study", "paper_url": "https://arxiv.org/pdf/1807.01270.pdf", "code_links": []}]}}]}, {"dataset": "JFLEG", "description": "[JFLEG test set](https://github.com/keisks/jfleg) released by [<PERSON><PERSON><PERSON> et al., 2017](http://aclweb.org/anthology/E17-2037) consists of 747 English sentences with 4 references for each sentence. Models are evaluated with [GLEU](https://github.com/cnap/gec-ranking/) metric ([<PERSON><PERSON><PERSON> et al., 2016](https://arxiv.org/pdf/1605.02592.pdf)).\n\n\n_**Restricted**_:\n\n\n**Unrestricted**:\n\n\n_**Restricted**_: uses only publicly available datasets. _**Unrestricted**_: uses non-public datasets.", "dataset_links": [{"title": "JFLEG test set](https://github.com/keisks/jfleg) released by [<PERSON><PERSON><PERSON> et al., 2017](http://aclweb.org/anthology/E17-2037) consists of 747 English sentences with 4 references for each sentence. Models are evaluated with [GLEU](https://github.com/cnap/gec-ranking/) metric ([<PERSON><PERSON><PERSON> et al., 2016", "url": "https://github.com/keisks/jfleg) released by [<PERSON><PERSON><PERSON> et al., 2017](http://aclweb.org/anthology/E17-2037) consists of 747 English sentences with 4 references for each sentence. Models are evaluated with [GLEU](https://github.com/cnap/gec-ranking/) metric ([<PERSON><PERSON><PERSON> et al., 2016](https://arxiv.org/pdf/1605.02592.pdf)"}], "subdatasets": [{"subdataset": "_Restricted_", "sota": {"metrics": ["GLEU"], "rows": [{"model_name": "SMT + BiGRU", "metrics": {"GLEU": "61.50"}, "paper_title": "Near Human-Level Performance in Grammatical Error Correction with Hybrid Machine Translation", "paper_url": "http://aclweb.org/anthology/N18-2046", "code_links": []}, {"model_name": "Transformer", "metrics": {"GLEU": "59.9"}, "paper_title": "Approaching Neural Grammatical Error Correction as a Low-Resource Machine Translation Task", "paper_url": "http://aclweb.org/anthology/N18-1055", "code_links": []}, {"model_name": "CNN Seq2Seq", "metrics": {"GLEU": "57.47"}, "paper_title": "A Multilayer Convolutional Encoder-Decoder Neural Network for Grammatical Error Correction", "paper_url": "https://www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/viewFile/17308/16137", "code_links": [{"title": "Official", "url": "https://github.com/nusnlp/mlconvgec2018"}]}]}}, {"subdataset": "Unrestricted", "sota": {"metrics": ["GLEU"], "rows": [{"model_name": "CNN Seq2Seq + Fluency Boost and inference", "metrics": {"GLEU": "62.37"}, "paper_title": "Reaching Human-level Performance in Automatic Grammatical Error Correction: An Empirical Study", "paper_url": "https://arxiv.org/pdf/1807.01270.pdf", "code_links": []}]}}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Relation Prediction", "description": "", "subtasks": [{"task": "Task", "description": "Relation Prediction is the task of recognizing a named relation between two named semantic entities. The common test setup is to hide one entity from the relation triplet, asking the system to recover it based on the other entity and the relation type.\n\nFor example, given the triple \\<*Roman Jakobson*, *born-in-city*, *?*\\>, the system is required to replace the question mark with *Moscow*.\n\nRelation Prediction datasets are typically extracted from two types of resources: \n* *Knowledge Bases*: KBs such as [FreeBase](https://developers.google.com/freebase/) contain hundreds or thousands of relation types pertaining to world-knowledge obtained autmoatically or semi-automatically from various resources on millions of entities. These relations include *born-in*, *nationality*, *is-in* (for geographical entities), *part-of* (for organizations, among others), and more.\n* *Semantic Graphs*: SGs such as [WordNet](https://wordnet.princeton.edu/) are often manually-curated resources of semantic concepts, restricted to more \"linguistic\" relations compared to free real-world knowledge. The most common semantic relation is *hypernym*, also known as the *is-a* relation (example: \\<*cat*, *hypernym*, *feline*\\>).", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Evaluation", "description": "Evaluation in Relation Prediction hinges on a list of ranked candidates given by the system to the test instance. The metrics below are derived from the location of correct candidate(s) in that list.\n\nA common action performed before evaluation on a given list is *filtering*, where the list is cleaned of entities whose corresponding triples exist in the knowledge graph. Unless specified otherwise, results here are from filtered lists.", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Metrics", "description": ""}, {"dataset": "Datasets", "description": ""}, {"dataset": "Experimental Results", "description": ""}]}, {"task": "Resources", "description": "[OpenKE](http://aclweb.org/anthology/D18-2024) is an open toolkit for relational learning which provides a standard training and testing framework. Currently, the implemented models in OpenKE include TransE, TransH, TransR, TransD, RESCAL, DistMult, ComplEx and HolE.\n\n[KRLPapers](https://github.com/thunlp/KRLPapers) is a must-read paper list for relational learning.\n\n[Back to README](../README.md)", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Semantic role labeling", "description": "Semantic role labeling aims to model the predicate-argument structure of a sentence\nand is often described as answering \"Who did what to whom\". BIO notation is typically\nused for semantic role labeling.\n\nExample:\n\n| Housing | starts | are | expected | to | quicken | a | bit | from | August’s | pace | \n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | \n| B-ARG1 | I-ARG1 | O |  O  |  O  |   V  | B-ARG2 | I-ARG2 | B-ARG3 | I-ARG3 | I-ARG3 |", "datasets": [{"dataset": "OntoNotes", "description": "Models are typically evaluated on the [OntoNotes benchmark](http://www.aclweb.org/anthology/W13-3516) based on F1.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "OntoNotes benchmark", "url": "http://www.aclweb.org/anthology/W13-3516"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["F1"], "rows": [{"model_name": "<PERSON> et al.,", "metrics": {"F1": "85.5"}, "paper_title": "Jointly Predicting Predicates and Arguments in Neural Semantic Role Labeling", "paper_url": "http://aclweb.org/anthology/P18-2058"}, {"model_name": "(<PERSON> et al., 2017) + ELMo", "metrics": {"F1": "84.6"}, "paper_title": "Deep contextualized word representations", "paper_url": "https://arxiv.org/abs/1802.05365"}, {"model_name": "<PERSON> et al.", "metrics": {"F1": "82.7"}, "paper_title": "Deep Semantic Role Labeling with Self-Attention", "paper_url": "https://arxiv.org/abs/1712.01586"}, {"model_name": "<PERSON> et al.", "metrics": {"F1": "82.1"}, "paper_title": "Jointly Predicting Predicates and Arguments in Neural Semantic Role Labeling", "paper_url": "http://aclweb.org/anthology/P18-2058"}, {"model_name": "<PERSON> et al.", "metrics": {"F1": "81.7"}, "paper_title": "Deep Semantic Role Labeling: What Works and What’s Next", "paper_url": "http://aclweb.org/anthology/P17-1044"}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Temporal Processing", "description": "", "subtasks": [{"task": "Document Dating (Time-stamping)", "description": "Document Dating is the problem of automatically predicting the date of a document based on its content. Date of a document, also referred to as the Document Creation Time (DCT), is at the core of many important tasks, such as, information retrieval, temporal reasoning, text summarization, event detection, and analysis of historical text, among others. \n\nFor example, in the following document, the correct creation year is 1999. This can be inferred by the presence of terms *1995* and *Four years after*.\n\n*Swiss adopted that form of taxation in 1995. The concession was approved by the govt last September. Four years after, the IOC….*", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Datasets", "description": "|                 Datasets                 | # Docs | Start Year | End Year |\n| :--------------------------------------: | :----: | :--------: | :------: |\n| [APW](https://drive.google.com/file/d/1tll04ZBooB3Mohm6It-v8MBcjMCC3Y1w/view) |  675k  |    1995    |   2010   |\n| [NYT](https://drive.google.com/file/d/1wqQRFeA1ESAOJqrwUNakfa77n_S9cmBi/view?usp=sharing) |  647k  |    1987    |   1996   |", "dataset_links": [{"title": "APW", "url": "https://drive.google.com/file/d/1tll04ZBooB3Mohm6It-v8MBcjMCC3Y1w/view"}, {"title": "NYT", "url": "https://drive.google.com/file/d/1wqQRFeA1ESAOJqrwUNakfa77n_S9cmBi/view?usp=sharing"}]}, {"dataset": "Comparison on year level granularity:", "description": "|                                        | APW Dataset | NYT Dataset | Paper/Source                             |\n| -------------------------------------- | :---------: | :---------: | ---------------------------------------- |\n| NeuralDater (Vashishth et. al, 2018)   |    64.1     |    58.9     | [Document Dating using Graph Convolution Networks](https://github.com/malllabiisc/NeuralDater) |\n| Chambers (2012)                        |    52.5     |    42.3     | [Labeling Documents with Timestamps: Learning from their Time Expressions](https://pdfs.semanticscholar.org/87af/a0cb4f829ce861da0c721ca666d48a62c404.pdf) |\n| BurstySimDater (Kotsakos et. al, 2014) |    45.9     |    38.5     | [A Burstiness-aware Approach for Document Dating](https://www.idi.ntnu.no/~noervaag/papers/SIGIR2014short.pdf) |", "dataset_links": [{"title": "Document Dating using Graph Convolution Networks", "url": "https://github.com/malllabiisc/NeuralDater"}, {"title": "Labeling Documents with Timestamps: Learning from their Time Expressions", "url": "https://pdfs.semanticscholar.org/87af/a0cb4f829ce861da0c721ca666d48a62c404.pdf"}, {"title": "A Burstiness-aware Approach for Document Dating", "url": "https://www.idi.ntnu.no/~noervaag/papers/SIGIR2014short.pdf"}]}]}, {"task": "Temporal Information Extraction", "description": "Temporal information extraction is the identification of chunks/tokens corresponding to temporal intervals, and the extraction and determination of the temporal relations between those. The entities extracted may be temporal expressions (timexes), eventualities (events), or auxiliary signals that support the interpretation of an entity or relation. Relations may be temporal links (tlinks), describing the order of events and times, or subordinate links (slinks) describing modality and other subordinative activity, or aspectual links (alinks) around the various influences aspectuality has on event structure.\n\nThe markup scheme used for temporal information extraction is well-described in the ISO-TimeML standard, and also on [www.timeml.org](http://www.timeml.org).\n\n```\n<?xml version=\"1.0\" ?>\n\n<TimeML xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:noNamespaceSchemaLocation=\"http://timeml.org/timeMLdocs/TimeML_1.2.1.xsd\">\n<TEXT>\n\n\n PRI20001020.2000.0127 \n NEWS STORY \n <TIMEX3 tid=\"t0\" type=\"TIME\" value=\"2000-10-20T20:02:07.85\">10/20/2000 20:02:07.85</TIMEX3> \n\n\n The Navy has changed its account of the attack on the USS Cole in Yemen.\n Officials <TIMEX3 tid=\"t1\" type=\"DATE\" value=\"PRESENT_REF\" temporalFunction=\"true\" anchorTimeID=\"t0\">now</TIMEX3> say the ship was hit <TIMEX3 tid=\"t2\" type=\"DURATION\" value=\"PT2H\">nearly two hours </TIMEX3>after it had docked.\n Initially the Navy said the explosion occurred while several boats were helping\n the ship to tie up. The change raises new questions about how the attackers\n were able to get past the Navy security.\n\n\n <TIMEX3 tid=\"t3\" type=\"TIME\" value=\"2000-10-20T20:02:28.05\">10/20/2000 20:02:28.05</TIMEX3> \n\n\n\n<TLINK timeID=\"t2\" relatedToTime=\"t0\" relType=\"BEFORE\"/>\n</TEXT>\n</TimeML>\n```\n\nTo avoid leaking knowledge about temporal structure, train, dev and test splits must be made at document level for temporal information extraction.", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "TimeBank", "description": "TimeBank, based on the TIMEX3 standard embedded in ISO-TimeML, is a benchmark corpus containing 64K tokens of English newswire, and annotated for all asepcts of ISO-TimeML - including temporal expressions. TimeBank is freely distributed by the LDC: [TimeBank 1.2](https://catalog.ldc.upenn.edu/LDC2006T08)\n\nEvaluation is for both entity chunking and attribute annotation, as well as temporal relation accuracy, typically measured with F1 -- although this metric is not sensitive to inconsistencies or free wins from interval logic induction over the whole set.", "dataset_links": [{"title": "TimeBank 1.2", "url": "https://catalog.ldc.upenn.edu/LDC2006T08"}], "sota": {"metrics": ["F1 score"], "rows": [{"model_name": "<PERSON><PERSON>", "metrics": {"F1 score": "0.511"}, "paper_title": "CATENA: CAusal and TEmporal relation extraction from NAtural language texts", "paper_url": "http://www.aclweb.org/anthology/C16-1007"}, {"model_name": "CAEVO", "metrics": {"F1 score": "0.507"}, "paper_title": "Dense Event Ordering with a Multi-Pass Architecture", "paper_url": "https://www.transacl.org/ojs/index.php/tacl/article/download/255/50"}]}}, {"dataset": "TempEval-3", "description": "The TempEval-3 corpus accompanied the shared [TempEval-3](http://www.aclweb.org/anthology/S13-2001) SemEval task in 2013. This uses a timelines-based metric to assess temporal relation structure. The corpus is fresh and somewhat more varied than TimeBank, though markedly smaller. [TempEval-3 data](https://www.cs.york.ac.uk/semeval-2013/task1/index.php%3Fid=data.html)", "dataset_links": [{"title": "TempEval-3](http://www.aclweb.org/anthology/S13-2001) SemEval task in 2013. This uses a timelines-based metric to assess temporal relation structure. The corpus is fresh and somewhat more varied than TimeBank, though markedly smaller. [TempEval-3 data", "url": "http://www.aclweb.org/anthology/S13-2001) SemEval task in 2013. This uses a timelines-based metric to assess temporal relation structure. The corpus is fresh and somewhat more varied than TimeBank, though markedly smaller. [TempEval-3 data](https://www.cs.york.ac.uk/semeval-2013/task1/index.php%3Fid=data.html"}], "sota": {"metrics": ["Temporal awareness"], "rows": [{"model_name": "<PERSON> et al.", "metrics": {"Temporal awareness": "67.2"}, "paper_title": "A Structured Learning Approach to Temporal Relation Extraction", "paper_url": "http://www.aclweb.org/anthology/D17-1108"}, {"model_name": "ClearTK", "metrics": {"Temporal awareness": "30.98"}, "paper_title": "Cleartk-timeml: A minimalist approach to tempeval 2013", "paper_url": "http://www.aclweb.org/anthology/S13-2002"}]}}]}, {"task": "Timex normalisation", "description": "Temporal expression normalisation is the grounding of a lexicalisation of a time to a calendar date or other formal temporal representation.\n\nExample:\n<TIMEX3 tid=\"t0\" type=\"TIME\" value=\"2000-10-18T21:01:00.65\">10/18/2000 21:01:00.65</TIMEX3>\nDozens of Palestinians were wounded in\nscattered clashes in the West Bank and Gaza Strip, <TIMEX3 tid=\"t1\" type=\"DATE\" value=\"2000-10-18\" temporalFunction=\"true\" anchorTimeID=\"t0\">Wednesday</TIMEX3>,\ndespite the Sharm el-Sheikh truce accord. \n\n<PERSON> reports on entertainment <TIMEX3 tid=\"t11\" type=\"SET\" value=\"XXXX-WXX-7\">every Saturday</TIMEX3>", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "TimeBank", "description": "TimeBank, based on the TIMEX3 standard embedded in ISO-TimeML, is a benchmark corpus containing 64K tokens of English newswire, and annotated for all asepcts of ISO-TimeML - including temporal expressions. TimeBank is freely distributed by the LDC: [TimeBank 1.2](https://catalog.ldc.upenn.edu/LDC2006T08)", "dataset_links": [{"title": "TimeBank 1.2", "url": "https://catalog.ldc.upenn.edu/LDC2006T08"}], "sota": {"metrics": ["F1 score"], "rows": [{"model_name": "TIMEN", "metrics": {"F1 score": "0.89"}, "paper_title": "TIMEN: An Open Temporal Expression Normalisation Resource", "paper_url": "http://aclweb.org/anthology/L12-1015"}, {"model_name": "HeidelTime", "metrics": {"F1 score": "0.876"}, "paper_title": "A baseline temporal tagger for all languages", "paper_url": "http://aclweb.org/anthology/D15-1063"}]}}, {"dataset": "PNT", "description": "The [Parsing Time Normalizations corpus](https://github.com/bethard/anafora-annotations/releases) in [SCATE](http://www.lrec-conf.org/proceedings/lrec2016/pdf/288_Paper.pdf) format allows the representation of a wider variety of time expressions than previous approaches. This corpus was release with [SemEval 2018 Task 6](http://aclweb.org/anthology/S18-1011).\n\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "Parsing Time Normalizations corpus](https://github.com/bethard/anafora-annotations/releases) in [SCATE](http://www.lrec-conf.org/proceedings/lrec2016/pdf/288_Paper.pdf) format allows the representation of a wider variety of time expressions than previous approaches. This corpus was release with [SemEval 2018 Task 6", "url": "https://github.com/bethard/anafora-annotations/releases) in [SCATE](http://www.lrec-conf.org/proceedings/lrec2016/pdf/288_Paper.pdf) format allows the representation of a wider variety of time expressions than previous approaches. This corpus was release with [SemEval 2018 Task 6](http://aclweb.org/anthology/S18-1011"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["F1 score"], "rows": [{"model_name": "<PERSON><PERSON><PERSON> et al. 2018", "metrics": {"F1 score": "0.764"}, "paper_title": "From Characters to Time Intervals: New Paradigms for Evaluation and Neural Parsing of Time Normalizations", "paper_url": "http://aclweb.org/anthology/Q18-1025"}, {"model_name": "HeidelTime", "metrics": {"F1 score": "0.74"}, "paper_title": "A baseline temporal tagger for all languages", "paper_url": "http://aclweb.org/anthology/D15-1063"}, {"model_name": "Chrono", "metrics": {"F1 score": "0.70"}, "paper_title": "Chrono at SemEval-2018 task 6: A system for normalizing temporal expressions", "paper_url": "http://aclweb.org/anthology/S18-1012"}]}}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Stance detection", "description": "Stance detection is the extraction of a subject's reaction to a claim made by a primary actor. It is a core part of a set of approaches to fake news assessment.\n\nExample:\n\n* Source: \"Apples are the most delicious fruit in existence\"\n* Reply: \"Obviously not, because that is a reuben from <PERSON>'s\"\n* Stance: deny", "datasets": [{"dataset": "RumourEval", "description": "The [RumourEval 2017](http://www.aclweb.org/anthology/S/S17/S17-2006.pdf) dataset has been used for stance detection in English (subtask A). It features multiple stories and thousands of reply:response pairs, with train, test and evaluation splits each containing a distinct set of over-arching narratives.\n\nThis dataset subsumes the large [PHEME collection of rumors and stance](http://journals.plos.org/plosone/article?id=10.1371/journal.pone.0150989), which includes German.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "RumourEval 2017", "url": "http://www.aclweb.org/anthology/S/S17/S17-2006.pdf) dataset has been used for stance detection in English (subtask A"}, {"title": "PHEME collection of rumors and stance", "url": "http://journals.plos.org/plosone/article?id=10.1371/journal.pone.0150989"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "<PERSON><PERSON><PERSON> et al. 2017", "metrics": {"Accuracy": "0.784"}, "paper_title": "Turing at SemEval-2017 Task 8: Sequential Approach to Rumour Stance Classification with Branch-LSTM", "paper_url": "http://www.aclweb.org/anthology/S/S17/S17-2083.pdf"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> 2017", "metrics": {"Accuracy": "0.780"}, "paper_title": "UWaterloo at SemEval-2017 Task 8: Detecting <PERSON><PERSON> towards Rumours with Topic Independent Features", "paper_url": "http://www.aclweb.org/anthology/S/S17/S17-2080.pdf"}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Domain adaptation", "description": "", "subtasks": [{"task": "Sentiment analysis", "description": "", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Multi-Domain Sentiment Dataset", "description": "The [Multi-Domain Sentiment Dataset](https://www.cs.jhu.edu/~mdredze/datasets/sentiment/) is a common\nevaluation dataset for domain adaptation for sentiment analysis. It contains product reviews from\nAmazon.com from different product categories, which are treated as distinct domains.\nReviews contain star ratings (1 to 5 stars) that are generally converted into binary labels. Models are\ntypically evaluated on a target domain that is different from the source domain they were trained on, while only\nhaving access to unlabeled examples of the target domain (unsupervised domain adaptation). The evaluation\nmetric is accuracy and scores are averaged across each domain.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "Multi-Domain Sentiment Dataset", "url": "https://www.cs.jhu.edu/~mdredze/datasets/sentiment/"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["DVD", "Books", "Electronics", "Kitchen", "Average"], "rows": [{"model_name": "Multi-task tri-training", "metrics": {"DVD": "78.14", "Books": "74.86", "Electronics": "81.45", "Kitchen": "82.14", "Average": "79.15"}, "paper_title": "Strong Baselines for Neural Semi-supervised Learning under Domain Shift", "paper_url": "https://arxiv.org/abs/1804.09530"}, {"model_name": "Asymmetric tri-training", "metrics": {"DVD": "76.17", "Books": "72.97", "Electronics": "80.47", "Kitchen": "83.97", "Average": "78.39"}, "paper_title": "Asymmetric Tri-training for Unsupervised Domain Adaptation", "paper_url": "https://arxiv.org/abs/1702.08400"}, {"model_name": "VFAE", "metrics": {"DVD": "76.57", "Books": "73.40", "Electronics": "80.53", "Kitchen": "82.93", "Average": "78.36"}, "paper_title": "The Variational Fair Autoencoder", "paper_url": "https://arxiv.org/abs/1511.00830"}, {"model_name": "DANN", "metrics": {"DVD": "75.40", "Books": "71.43", "Electronics": "77.67", "Kitchen": "80.53", "Average": "76.26"}, "paper_title": "Domain-Adversarial Training of Neural Networks", "paper_url": "https://arxiv.org/abs/1505.07818"}]}}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Language modeling", "description": "Language modeling is the task of predicting the next word or character in a document.\n\n\\* indicates models using dynamic evaluation; where, at test time, models may adapt to seen tokens in order to improve performance on following tokens. ([<PERSON><PERSON><PERSON> et al., (2010)](https://www.fit.vutbr.cz/research/groups/speech/publi/2010/mi<PERSON><PERSON>_interspeech2010_IS100722.pdf), [<PERSON><PERSON><PERSON> et al., (2017)](https://arxiv.org/pdf/1709.07432))", "subtasks": [{"task": "Word Level Models", "description": "", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Penn Treebank", "description": "A common evaluation dataset for language modeling ist the Penn Treebank,\nas pre-processed by [<PERSON><PERSON><PERSON> et al., (2010)](http://www.fit.vutbr.cz/research/groups/speech/publi/2010/mi<PERSON><PERSON>_interspeech2010_IS100722.pdf).\nThe dataset consists of 929k training words, 73k validation words, and\n82k test words. As part of the pre-processing, words were lower-cased, numbers\nwere replaced with N, newlines were replaced with `<eos>`,\nand all other punctuation was removed. The vocabulary is\nthe most frequent 10k words with the rest of the tokens replaced by an `<unk>` token.\nModels are evaluated based on perplexity, which is the average\nper-word log-probability (lower is better).", "dataset_links": [{"title": "<PERSON><PERSON><PERSON> et al., (2010)", "url": "2010)](http://www.fit.vutbr.cz/research/groups/speech/publi/2010/mikolov_interspeech2010_IS100722.pdf"}], "sota": {"metrics": ["Validation perplexity", "Test perplexity", "Number of params"], "rows": [{"model_name": "AWD-LSTM-DOC x5", "metrics": {"Validation perplexity": "48.63", "Test perplexity": "47.17", "Number of params": "185M"}, "paper_title": "Direct Output Connection for a High-Rank Language Model", "paper_url": "https://arxiv.org/abs/1808.10143", "code_links": [{"title": "Official", "url": "https://github.com/nttcslab-nlp/doc_lm"}]}, {"model_name": "AWD-LSTM-MoS + dynamic eval", "metrics": {"Validation perplexity": "48.33", "Test perplexity": "47.69", "Number of params": "22M"}, "paper_title": "Breaking the Softmax Bottleneck: A High-Rank RNN Language Model", "paper_url": "https://arxiv.org/abs/1711.03953", "code_links": [{"title": "Official", "url": "https://github.com/zihangdai/mos"}]}, {"model_name": "AWD-LSTM + dynamic eval", "metrics": {"Validation perplexity": "51.6", "Test perplexity": "51.1", "Number of params": "24M"}, "paper_title": "Dynamic Evaluation of Neural Sequence Models", "paper_url": "https://arxiv.org/abs/1709.07432", "code_links": [{"title": "Official", "url": "https://github.com/benkrause/dynamic-evaluation"}]}, {"model_name": "AWD-LSTM + continuous cache pointer", "metrics": {"Validation perplexity": "53.9", "Test perplexity": "52.8", "Number of params": "24M"}, "paper_title": "Regularizing and Optimizing LSTM Language Models", "paper_url": "https://arxiv.org/abs/1708.02182", "code_links": [{"title": "Official", "url": "https://github.com/salesforce/awd-lstm-lm"}]}, {"model_name": "AWD-LSTM-DOC", "metrics": {"Validation perplexity": "54.12", "Test perplexity": "52.38", "Number of params": "23M"}, "paper_title": "Direct Output Connection for a High-Rank Language Model", "paper_url": "https://arxiv.org/abs/1808.10143", "code_links": [{"title": "Official", "url": "https://github.com/nttcslab-nlp/doc_lm"}]}, {"model_name": "AWD-LSTM-MoS", "metrics": {"Validation perplexity": "56.54", "Test perplexity": "54.44", "Number of params": "22M"}, "paper_title": "Breaking the Softmax Bottleneck: A High-Rank RNN Language Model", "paper_url": "https://arxiv.org/abs/1711.03953", "code_links": [{"title": "Official", "url": "https://github.com/zihangdai/mos"}]}, {"model_name": "Transformer-XL", "metrics": {"Validation perplexity": "56.72", "Test perplexity": "54.55", "Number of params": "24M"}, "paper_title": "Transformer-XL: Language Modeling with Longer-Term Dependency", "paper_url": "https://openreview.net/pdf?id=HJePno0cYm", "code_links": []}, {"model_name": "AWD-LSTM 3-layer with Fraternal dropout", "metrics": {"Validation perplexity": "58.9", "Test perplexity": "56.8", "Number of params": "24M"}, "paper_title": "Fraternal dropout", "paper_url": "https://arxiv.org/pdf/1711.00066.pdf", "code_links": [{"title": "Official", "url": "https://github.com/kondiz/fraternal-dropout"}]}, {"model_name": "AWD-LSTM", "metrics": {"Validation perplexity": "60.0", "Test perplexity": "57.3", "Number of params": "24M"}, "paper_title": "Regularizing and Optimizing LSTM Language Models", "paper_url": "https://arxiv.org/abs/1708.02182", "code_links": [{"title": "Official", "url": "https://github.com/salesforce/awd-lstm-lm"}]}]}}, {"dataset": "WikiText-2", "description": "[WikiText-2](https://arxiv.org/abs/1609.07843) has been proposed as a more realistic\nbenchmark for language modeling than the pre-processed Penn Treebank. WikiText-2\nconsists of around 2 million words extracted from Wikipedia articles.", "dataset_links": [{"title": "WikiText-2", "url": "https://arxiv.org/abs/1609.07843"}], "sota": {"metrics": ["Validation perplexity", "Test perplexity", "Number of params"], "rows": [{"model_name": "AWD-LSTM-MoS + dynamic eval", "metrics": {"Validation perplexity": "42.41", "Test perplexity": "40.68", "Number of params": "35M"}, "paper_title": "Breaking the Softmax Bottleneck: A High-Rank RNN Language Model", "paper_url": "https://arxiv.org/abs/1711.03953", "code_links": [{"title": "Official", "url": "https://github.com/zihangdai/mos"}]}, {"model_name": "AWD-LSTM + dynamic eval", "metrics": {"Validation perplexity": "46.4", "Test perplexity": "44.3", "Number of params": "33M"}, "paper_title": "Dynamic Evaluation of Neural Sequence Models", "paper_url": "https://arxiv.org/abs/1709.07432", "code_links": [{"title": "Official", "url": "https://github.com/benkrause/dynamic-evaluation"}]}, {"model_name": "AWD-LSTM + continuous cache pointer", "metrics": {"Validation perplexity": "53.8", "Test perplexity": "52.0", "Number of params": "33M"}, "paper_title": "Regularizing and Optimizing LSTM Language Models", "paper_url": "https://arxiv.org/abs/1708.02182", "code_links": [{"title": "Official", "url": "https://github.com/salesforce/awd-lstm-lm"}]}, {"model_name": "AWD-LSTM-DOC x5", "metrics": {"Validation perplexity": "54.19", "Test perplexity": "53.09", "Number of params": "185M"}, "paper_title": "Direct Output Connection for a High-Rank Language Model", "paper_url": "https://arxiv.org/abs/1808.10143", "code_links": [{"title": "Official", "url": "https://github.com/nttcslab-nlp/doc_lm"}]}, {"model_name": "AWD-LSTM-DOC", "metrics": {"Validation perplexity": "60.29", "Test perplexity": "58.03", "Number of params": "37M"}, "paper_title": "Direct Output Connection for a High-Rank Language Model", "paper_url": "https://arxiv.org/abs/1808.10143", "code_links": [{"title": "Official", "url": "https://github.com/nttcslab-nlp/doc_lm"}]}, {"model_name": "AWD-LSTM-MoS", "metrics": {"Validation perplexity": "63.88", "Test perplexity": "61.45", "Number of params": "35M"}, "paper_title": "Breaking the Softmax Bottleneck: A High-Rank RNN Language Model", "paper_url": "https://arxiv.org/abs/1711.03953", "code_links": [{"title": "Official", "url": "https://github.com/zihangdai/mos"}]}, {"model_name": "AWD-LSTM 3-layer with Fraternal dropout", "metrics": {"Validation perplexity": "66.8", "Test perplexity": "64.1", "Number of params": "34M"}, "paper_title": "Fraternal dropout", "paper_url": "https://arxiv.org/pdf/1711.00066.pdf", "code_links": [{"title": "Official", "url": "https://github.com/kondiz/fraternal-dropout"}]}, {"model_name": "AWD-LSTM", "metrics": {"Validation perplexity": "68.6", "Test perplexity": "65.8", "Number of params": "33M"}, "paper_title": "Regularizing and Optimizing LSTM Language Models", "paper_url": "https://arxiv.org/abs/1708.02182", "code_links": [{"title": "Official", "url": "https://github.com/salesforce/awd-lstm-lm"}]}]}}, {"dataset": "WikiText-103", "description": "[WikiText-103](https://arxiv.org/abs/1609.07843) The WikiText-103 corpus contains 267,735 unique words and each word occurs at least three times in the training set.", "dataset_links": [{"title": "WikiText-103", "url": "https://arxiv.org/abs/1609.07843"}], "sota": {"metrics": ["Validation perplexity", "Test perplexity", "Number of params"], "rows": [{"model_name": "Transformer-XL Large", "metrics": {"Validation perplexity": "18.2", "Test perplexity": "18.9", "Number of params": "257M"}, "paper_title": "Transformer-XL: Language Modeling with Longer-Term Dependency", "paper_url": "https://openreview.net/pdf?id=HJePno0cYm", "code_links": []}, {"model_name": "Transformer with tied adaptive embeddings", "metrics": {"Validation perplexity": "19.8", "Test perplexity": "20.5", "Number of params": "247M"}, "paper_title": "Adaptive Input Representations for Neural Language Modeling", "paper_url": "https://arxiv.org/pdf/1809.10853.pdf", "code_links": [{"title": "Link", "url": "https://github.com/AranKomat/adapinp"}]}, {"model_name": "Transformer-XL Standard", "metrics": {"Validation perplexity": "23.1", "Test perplexity": "24.0", "Number of params": "151M"}, "paper_title": "Transformer-XL: Language Modeling with Longer-Term Dependency", "paper_url": "https://openreview.net/pdf?id=HJePno0cYm", "code_links": []}, {"model_name": "LSTM + Hebbian + Cache + MbPA", "metrics": {"Validation perplexity": "29.0", "Test perplexity": "29.2", "Number of params": ""}, "paper_title": "Fast Parametric Learning with Activation Memorization", "paper_url": "http://arxiv.org/abs/1803.10049", "code_links": []}, {"model_name": "LSTM + <PERSON><PERSON>ian", "metrics": {"Validation perplexity": "34.1", "Test perplexity": "34.3", "Number of params": ""}, "paper_title": "Fast Parametric Learning with Activation Memorization", "paper_url": "http://arxiv.org/abs/1803.10049", "code_links": []}, {"model_name": "LSTM", "metrics": {"Validation perplexity": "36.0", "Test perplexity": "36.4", "Number of params": ""}, "paper_title": "Fast Parametric Learning with Activation Memorization", "paper_url": "http://arxiv.org/abs/1803.10049", "code_links": []}, {"model_name": "Gated CNN", "metrics": {"Validation perplexity": "-", "Test perplexity": "37.2", "Number of params": ""}, "paper_title": "Language modeling with gated convolutional networks", "paper_url": "https://arxiv.org/abs/1612.08083", "code_links": []}, {"model_name": "Neural cache model", "metrics": {"Validation perplexity": "-", "Test perplexity": "40.8", "Number of params": ""}, "paper_title": "Improving Neural Language Models with a Continuous Cache", "paper_url": "https://arxiv.org/pdf/1612.04426.pdf", "code_links": [{"title": "Link", "url": "https://github.com/kaishengtai/torch-ntm"}]}, {"model_name": "Temporal CNN", "metrics": {"Validation perplexity": "-", "Test perplexity": "45.2", "Number of params": ""}, "paper_title": "Convolutional sequence modeling revisited", "paper_url": "https://openreview.net/forum?id=BJEX-H1Pf", "code_links": []}, {"model_name": "LSTM", "metrics": {"Validation perplexity": "-", "Test perplexity": "48.7", "Number of params": ""}, "paper_title": "Improving Neural Language Models with a Continuous Cache", "paper_url": "https://arxiv.org/pdf/1612.04426.pdf", "code_links": [{"title": "Link", "url": "https://github.com/kaishengtai/torch-ntm"}]}]}}, {"dataset": "1B Words / Google Billion Word benchmark", "description": "[The One-Billion Word benchmark](https://arxiv.org/pdf/1312.3005.pdf) is a large dataset derived from a news-commentary site.\nThe dataset consists of 829,250,940 tokens over a vocabulary of 793,471 words.\nImportantly, sentences in this model are shuffled and hence context is limited.", "dataset_links": [{"title": "The One-Billion Word benchmark", "url": "https://arxiv.org/pdf/1312.3005.pdf"}], "sota": {"metrics": ["Test perplexity", "Number of params"], "rows": [{"model_name": "Transformer-XL Large", "metrics": {"Test perplexity": "23.5", "Number of params": "0.46B"}, "paper_title": "Transformer-XL: Language Modeling with Longer-Term Dependency", "paper_url": "https://openreview.net/pdf?id=HJePno0cYm", "code_links": []}, {"model_name": "10 LSTM+CNN inputs + SNM10-SKIP", "metrics": {"Test perplexity": "23.7", "Number of params": "43B?"}, "paper_title": "Exploring the Limits of Language Modeling", "paper_url": "https://arxiv.org/pdf/1602.02410.pdf", "code_links": [{"title": "Official", "url": "https://github.com/rafal<PERSON><PERSON>owicz/lm"}]}, {"model_name": "Transformer with shared adaptive embeddings", "metrics": {"Test perplexity": "24.1", "Number of params": "0.46B"}, "paper_title": "Adaptive Input Representations for Neural Language Modeling", "paper_url": "https://arxiv.org/pdf/1809.10853.pdf", "code_links": [{"title": "Link", "url": "https://github.com/AranKomat/adapinp"}]}, {"model_name": "Big LSTM+CNN inputs", "metrics": {"Test perplexity": "30.0", "Number of params": "1.04B"}, "paper_title": "Exploring the Limits of Language Modeling", "paper_url": "https://arxiv.org/pdf/1602.02410.pdf", "code_links": []}, {"model_name": "Gated CNN-14<PERSON><PERSON><PERSON><PERSON>", "metrics": {"Test perplexity": "31.9", "Number of params": "?"}, "paper_title": "Language Modeling with Gated Convolutional Networks", "paper_url": "https://arxiv.org/pdf/1612.08083.pdf", "code_links": []}, {"model_name": "BIGLSTM baseline", "metrics": {"Test perplexity": "35.1", "Number of params": "0.151B"}, "paper_title": "Factorization tricks for LSTM networks", "paper_url": "https://arxiv.org/pdf/1703.10722.pdf", "code_links": [{"title": "Official", "url": "https://github.com/okuchaiev/f-lm"}]}, {"model_name": "BIG F-LSTM F512", "metrics": {"Test perplexity": "36.3", "Number of params": "0.052B"}, "paper_title": "Factorization tricks for LSTM networks", "paper_url": "https://arxiv.org/pdf/1703.10722.pdf", "code_links": [{"title": "Official", "url": "https://github.com/okuchaiev/f-lm"}]}, {"model_name": "BIG G-LSTM G-8", "metrics": {"Test perplexity": "39.4", "Number of params": "0.035B"}, "paper_title": "Factorization tricks for LSTM networks", "paper_url": "https://arxiv.org/pdf/1703.10722.pdf", "code_links": [{"title": "Official", "url": "https://github.com/okuchaiev/f-lm"}]}]}}]}, {"task": "Character Level Models", "description": "", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Hutter Prize", "description": "[The Hutter Prize](http://prize.hutter1.net) Wikipedia dataset, also known as enwiki8, is a byte-level dataset consisting of the\nfirst 100 million bytes of a Wikipedia XML dump. For simplicity we shall refer to it as a character-level dataset.\nWithin these 100 million bytes are 205 unique tokens.", "dataset_links": [{"title": "The Hutter Prize", "url": "http://prize.hutter1.net"}], "sota": {"metrics": ["Bit per Character (BPC)", "Number of params"], "rows": [{"model_name": "24-layer Transformer-XL", "metrics": {"Bit per Character (BPC)": "0.99", "Number of params": "277M"}, "paper_title": "Transformer-XL: Language Modeling with Longer-Term Dependency", "paper_url": "https://openreview.net/pdf?id=HJePno0cYm", "code_links": []}, {"model_name": "18-layer Transformer-XL", "metrics": {"Bit per Character (BPC)": "1.03", "Number of params": "88M"}, "paper_title": "Transformer-XL: Language Modeling with Longer-Term Dependency", "paper_url": "https://openreview.net/pdf?id=HJePno0cYm", "code_links": []}, {"model_name": "12-layer Transformer-XL", "metrics": {"Bit per Character (BPC)": "1.06", "Number of params": "41M"}, "paper_title": "Transformer-XL: Language Modeling with Longer-Term Dependency", "paper_url": "https://openreview.net/pdf?id=HJePno0cYm", "code_links": []}, {"model_name": "64-layer Character Transformer Model", "metrics": {"Bit per Character (BPC)": "1.06", "Number of params": "235M"}, "paper_title": "Character-Level Language Modeling with Deeper Self-Attention", "paper_url": "https://arxiv.org/abs/1808.04444", "code_links": []}, {"model_name": "mLSTM + dynamic eval", "metrics": {"Bit per Character (BPC)": "1.08", "Number of params": "46M"}, "paper_title": "Dynamic Evaluation of Neural Sequence Models", "paper_url": "https://arxiv.org/abs/1709.07432", "code_links": [{"title": "Official", "url": "https://github.com/benkrause/dynamic-evaluation"}]}, {"model_name": "12-layer Character Transformer Model", "metrics": {"Bit per Character (BPC)": "1.11", "Number of params": "44M"}, "paper_title": "Character-Level Language Modeling with Deeper Self-Attention", "paper_url": "https://arxiv.org/abs/1808.04444", "code_links": []}, {"model_name": "3-layer AWD-LSTM", "metrics": {"Bit per Character (BPC)": "1.232", "Number of params": "47M"}, "paper_title": "An Analysis of Neural Language Modeling at Multiple Scales", "paper_url": "https://arxiv.org/abs/1803.08240", "code_links": [{"title": "Official", "url": "https://github.com/salesforce/awd-lstm-lm"}]}, {"model_name": "Large mLSTM +emb +WN +VD", "metrics": {"Bit per Character (BPC)": "1.24", "Number of params": "46M"}, "paper_title": "Multiplicative LSTM for sequence modelling", "paper_url": "https://arxiv.org/abs/1609.07959", "code_links": [{"title": "Official", "url": "https://github.com/benkrause/mLSTM"}]}, {"model_name": "Large FS-LSTM-4", "metrics": {"Bit per Character (BPC)": "1.245", "Number of params": "47M"}, "paper_title": "Fast-Slow Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1705.08639", "code_links": [{"title": "Official", "url": "https://github.com/amujika/Fast-Slow-LSTM"}]}, {"model_name": "Large RHN", "metrics": {"Bit per Character (BPC)": "1.27", "Number of params": "46M"}, "paper_title": "Recurrent Highway Networks", "paper_url": "https://arxiv.org/abs/1607.03474", "code_links": [{"title": "Official", "url": "https://github.com/jzilly/RecurrentHighwayNetworks"}]}, {"model_name": "FS-LSTM-4", "metrics": {"Bit per Character (BPC)": "1.277", "Number of params": "27M"}, "paper_title": "Fast-Slow Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1705.08639", "code_links": [{"title": "Official", "url": "https://github.com/amujika/Fast-Slow-LSTM"}]}]}}, {"dataset": "Text8", "description": "[The text8 dataset](http://mattmahoney.net/dc/textdata.html) is also derived from Wikipedia text, but has all XML removed, and is lower cased to only have 26 characters of English text plus spaces.", "dataset_links": [{"title": "The text8 dataset", "url": "http://mattmahoney.net/dc/textdata.html"}], "sota": {"metrics": ["Bit per Character (BPC)", "Number of params"], "rows": [{"model_name": "64-layer Character Transformer Model", "metrics": {"Bit per Character (BPC)": "1.13", "Number of params": "235M"}, "paper_title": "Character-Level Language Modeling with Deeper Self-Attention", "paper_url": "https://arxiv.org/abs/1808.04444", "code_links": []}, {"model_name": "12-layer Character Transformer Model", "metrics": {"Bit per Character (BPC)": "1.18", "Number of params": "44M"}, "paper_title": "Character-Level Language Modeling with Deeper Self-Attention", "paper_url": "https://arxiv.org/abs/1808.04444", "code_links": []}, {"model_name": "mLSTM + dynamic eval", "metrics": {"Bit per Character (BPC)": "1.19", "Number of params": "45M"}, "paper_title": "Dynamic Evaluation of Neural Sequence Models", "paper_url": "https://arxiv.org/abs/1709.07432", "code_links": [{"title": "Official", "url": "https://github.com/benkrause/dynamic-evaluation"}]}, {"model_name": "Large mLSTM +emb +WN +VD", "metrics": {"Bit per Character (BPC)": "1.27", "Number of params": "45M"}, "paper_title": "Multiplicative LSTM for sequence modelling", "paper_url": "https://arxiv.org/abs/1609.07959", "code_links": [{"title": "Official", "url": "https://github.com/benkrause/mLSTM"}]}, {"model_name": "Large RHN", "metrics": {"Bit per Character (BPC)": "1.27", "Number of params": "46M"}, "paper_title": "Recurrent Highway Networks", "paper_url": "https://arxiv.org/abs/1607.03474", "code_links": [{"title": "Official", "url": "https://github.com/jzilly/RecurrentHighwayNetworks"}]}, {"model_name": "LayerNorm HM-LSTM", "metrics": {"Bit per Character (BPC)": "1.29", "Number of params": "35M"}, "paper_title": "Hierarchical Multiscale Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1609.01704", "code_links": []}, {"model_name": "BN LSTM", "metrics": {"Bit per Character (BPC)": "1.36", "Number of params": "16M"}, "paper_title": "Recurrent Batch Normalization", "paper_url": "https://arxiv.org/abs/1603.09025", "code_links": [{"title": "Official", "url": "https://github.com/cooijmanstim/recurrent-batch-normalization"}]}, {"model_name": "Unregularised mLSTM", "metrics": {"Bit per Character (BPC)": "1.40", "Number of params": "45M"}, "paper_title": "Multiplicative LSTM for sequence modelling", "paper_url": "https://arxiv.org/abs/1609.07959", "code_links": [{"title": "Official", "url": "https://github.com/benkrause/mLSTM"}]}]}}, {"dataset": "Penn Treebank", "description": "The vocabulary of the words in the character-level dataset is limited to 10 000 - the same vocabulary as used in the word level dataset.  This vastly simplifies the task of character-level language modeling as character transitions will be limited to those found within the limited word level vocabulary.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Bit per Character (BPC)", "Number of params"], "rows": [{"model_name": "3-layer AWD-LSTM", "metrics": {"Bit per Character (BPC)": "1.175", "Number of params": "13.8M"}, "paper_title": "An Analysis of Neural Language Modeling at Multiple Scales", "paper_url": "https://arxiv.org/abs/1803.08240", "code_links": [{"title": "Official", "url": "https://github.com/salesforce/awd-lstm-lm"}]}, {"model_name": "6-layer QRNN", "metrics": {"Bit per Character (BPC)": "1.187", "Number of params": "13.8M"}, "paper_title": "An Analysis of Neural Language Modeling at Multiple Scales", "paper_url": "https://arxiv.org/abs/1803.08240", "code_links": [{"title": "Official", "url": "https://github.com/salesforce/awd-lstm-lm"}]}, {"model_name": "FS-LSTM-4", "metrics": {"Bit per Character (BPC)": "1.190", "Number of params": "27M"}, "paper_title": "Fast-Slow Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1705.08639", "code_links": [{"title": "Official", "url": "https://github.com/amujika/Fast-Slow-LSTM"}]}, {"model_name": "FS-LSTM-2", "metrics": {"Bit per Character (BPC)": "1.193", "Number of params": "27M"}, "paper_title": "Fast-Slow Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1705.08639", "code_links": [{"title": "Official", "url": "https://github.com/amujika/Fast-Slow-LSTM"}]}, {"model_name": "NASCell", "metrics": {"Bit per Character (BPC)": "1.214", "Number of params": "16.3M"}, "paper_title": "Neural Architecture Search with Reinforcement Learning", "paper_url": "https://arxiv.org/abs/1611.01578", "code_links": []}, {"model_name": "2-layer Norm HyperLSTM", "metrics": {"Bit per Character (BPC)": "1.219", "Number of params": "14.4M"}, "paper_title": "HyperNetworks", "paper_url": "https://arxiv.org/abs/1609.09106", "code_links": []}]}}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Semantic textual similarity", "description": "Semantic textual similarity deals with determining how similar two pieces of texts are.\nThis can take the form of assigning a score from 1 to 5. Related tasks are paraphrase or duplicate identification.", "datasets": [{"dataset": "Sent<PERSON><PERSON>", "description": "[SentEval](https://arxiv.org/abs/1803.05449) is an evaluation toolkit for evaluating sentence\nrepresentations. It includes 17 downstream tasks, including common semantic textual similarity\ntasks. The semantic textual similarity (STS) benchmark tasks from 2012-2016 (STS12, STS13, STS14, STS15, STS16, STSB) measure the relatedness\nof two sentences based on the cosine similarity of the two representations. The evaluation criterion is Pearson correlation.\n\nThe SICK relatedness (SICK-R) task trains a linear model to output a score from 1 to 5 indicating the relatedness of two sentences. For\nthe same dataset (SICK-E) can be treated as a three-class classification problem using the entailment labels (classes are 'entailment', 'contradiction', and 'neutral').\nThe evaluation metric for SICK-R is Pearson correlation and classification accuracy for SICK-E.\n\nThe Microsoft Research Paraphrase Corpus (MRPC) corpus is a paraphrase identification dataset, where systems\naim to identify if two sentences are paraphrases of each other. The evaluation metric is classification accuracy and F1.\n\nThe data can be downloaded from [here](https://github.com/facebookresearch/SentEval).", "dataset_links": [{"title": "Sent<PERSON><PERSON>", "url": "https://arxiv.org/abs/1803.05449"}, {"title": "here", "url": "https://github.com/facebookresearch/SentEval"}], "sota": {"metrics": ["MRPC", "SICK-R", "SICK-E", "STS"], "rows": [{"model_name": "GenSen", "metrics": {"MRPC": "78.6/84.4", "SICK-R": "0.888", "SICK-E": "87.8", "STS": "78.9/78.6"}, "paper_title": "Learning General Purpose Distributed Sentence Representations via Large Scale Multi-task Learning", "paper_url": "https://arxiv.org/abs/1804.00079", "code_links": [{"title": "Official", "url": "https://github.com/Maluuba/gensen"}]}, {"model_name": "InferSent", "metrics": {"MRPC": "76.2/83.1", "SICK-R": "0.884", "SICK-E": "86.3", "STS": "75.8/75.5"}, "paper_title": "Supervised Learning of Universal Sentence Representations from Natural Language Inference Data", "paper_url": "https://arxiv.org/abs/1705.02364", "code_links": [{"title": "Official", "url": "https://github.com/facebookresearch/InferSent"}]}, {"model_name": "TF-KLD", "metrics": {"MRPC": "80.4/85.9", "SICK-R": "-", "SICK-E": "-", "STS": "-"}, "paper_title": "Discriminative Improvements to Distributional Sentence Similarity", "paper_url": "http://www.aclweb.org/anthology/D/D13/D13-1090.pdf", "code_links": []}]}}], "subtasks": [{"task": "Paraphrase identification", "description": "", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Quora Question Pairs", "description": "The [Quora Question Pairs dataset](https://data.quora.com/First-Quora-Dataset-Release-Question-Pairs)\nconsists of over 400,000 pairs of questions on Quora. Systems must identify whether one question is a\nduplicate of the other. Models are evaluated based on accuracy.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "Quora Question Pairs dataset", "url": "https://data.quora.com/First-Quora-Dataset-Release-Question-Pairs"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "DIIN", "metrics": {"Accuracy": "89.06"}, "paper_title": "Natural Language Inference Over Interaction Space", "paper_url": "https://arxiv.org/pdf/1709.04348.pdf", "code_links": [{"title": "Official", "url": "https://github.com/YichenGong/Densely-Interactive-Inference-Network"}]}, {"model_name": "pt-Dec<PERSON>tt", "metrics": {"Accuracy": "88.40"}, "paper_title": "Neural Paraphrase Identification of Questions with Noisy Pretraining", "paper_url": "https://arxiv.org/abs/1704.04565", "code_links": []}, {"model_name": "BiMPM", "metrics": {"Accuracy": "88.17"}, "paper_title": "Bilateral Multi-Perspective Matching for Natural Language Sentences", "paper_url": "https://arxiv.org/abs/1702.03814", "code_links": [{"title": "Official", "url": "https://github.com/zhiguowang/BiMPM"}]}, {"model_name": "GenSen", "metrics": {"Accuracy": "87.01"}, "paper_title": "Learning General Purpose Distributed Sentence Representations via Large Scale Multi-task Learning", "paper_url": "https://arxiv.org/abs/1804.00079", "code_links": [{"title": "Official", "url": "https://github.com/Maluuba/gensen"}]}]}}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Common sense", "description": "Common sense reasoning tasks are intended to require the model to go beyond pattern \nrecognition. Instead, the model should use \"common sense\" or world knowledge\nto make inferences.", "datasets": [{"dataset": "Event2Mind", "description": "Event2Mind is a crowdsourced corpus of 25,000 event phrases covering a diverse range of everyday events and situations.\nGiven an event described in a short free-form text, a model should reason about the likely intents and reactions of the\nevent's participants. Models are evaluated based on average cross-entropy (lower is better).", "sota": {"metrics": ["<PERSON>", "Test"], "rows": [{"model_name": "BiRNN 100d", "metrics": {"Dev": "4.25", "Test": "4.22"}, "paper_title": "Event2Mind: Commonsense Inference on Events, Intents, and Reactions", "paper_url": "https://arxiv.org/abs/1805.06939", "code_links": []}, {"model_name": "ConvNet", "metrics": {"Dev": "4.44", "Test": "4.40"}, "paper_title": "Event2Mind: Commonsense Inference on Events, Intents, and Reactions", "paper_url": "https://arxiv.org/abs/1805.06939", "code_links": []}]}}, {"dataset": "SWAG", "description": "Situations with Adversarial Generations (SWAG) is a dataset consisting of 113k multiple\nchoice questions about a rich spectrum of grounded situations.", "sota": {"metrics": ["<PERSON>", "Test"], "rows": [{"model_name": "BERT Large", "metrics": {"Dev": "86.6", "Test": "86.3"}, "paper_title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding", "paper_url": "https://arxiv.org/abs/1810.04805", "code_links": []}, {"model_name": "BERT Base", "metrics": {"Dev": "81.6", "Test": "-"}, "paper_title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding", "paper_url": "https://arxiv.org/abs/1810.04805", "code_links": []}, {"model_name": "ESIM + ELMo", "metrics": {"Dev": "59.1", "Test": "59.2"}, "paper_title": "SWAG: A Large-Scale Adversarial Dataset for Grounded Commonsense Inference", "paper_url": "http://arxiv.org/abs/1808.05326", "code_links": []}, {"model_name": "ESIM + GloVe", "metrics": {"Dev": "51.9", "Test": "52.7"}, "paper_title": "SWAG: A Large-Scale Adversarial Dataset for Grounded Commonsense Inference", "paper_url": "http://arxiv.org/abs/1808.05326", "code_links": []}]}}, {"dataset": "Winograd <PERSON> Challenge", "description": "The [Winograd Schema Challenge](https://www.aaai.org/ocs/index.php/KR/KR12/paper/view/4492)\nis a dataset for common sense reasoning. It employs Winograd Schema questions that\nrequire the resolution of anaphora: the system must identify the antecedent of an ambiguous pronoun in a statement. Models\nare evaluated based on accuracy.\n\nExample:\n\nThe trophy doesn’t fit in the suitcase because _it_ is too big. What is too big?\nAnswer 0: the trophy. Answer 1: the suitcase", "dataset_links": [{"title": "Winograd <PERSON> Challenge", "url": "https://www.aaai.org/ocs/index.php/KR/KR12/paper/view/4492"}], "sota": {"metrics": ["Score"], "rows": [{"model_name": "Word-LM-partial", "metrics": {"Score": "62.6"}, "paper_title": "A Simple Method for Commonsense Reasoning", "paper_url": "https://arxiv.org/abs/1806.02847"}, {"model_name": "Char-LM-partial", "metrics": {"Score": "57.9"}, "paper_title": "A Simple Method for Commonsense Reasoning", "paper_url": "https://arxiv.org/abs/1806.02847"}, {"model_name": "USSM + Supervised DeepNet + KB", "metrics": {"Score": "52.8"}, "paper_title": "Combing Context and Commonsense Knowledge Through Neural Networks for Solving Winograd <PERSON> Problems", "paper_url": "https://aaai.org/ocs/index.php/SSS/SSS17/paper/view/15392"}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Constituency parsing", "description": "Consituency parsing aims to extract a constituency-based parse tree from a sentence that\nrepresents its syntactic structure according to a [phrase structure grammar](https://en.wikipedia.org/wiki/Phrase_structure_grammar).\n\nExample:\n\n                 Sentence (S)\n                     |\n       +-------------+------------+\n       |                          |\n     Noun (N)                Verb Phrase (VP)\n       |                          |\n     John                 +-------+--------+\n                          |                |\n                        Verb (V)         Noun (N)\n                          |                |\n                        sees              Bill\n\n[Recent approaches](https://papers.nips.cc/paper/5635-grammar-as-a-foreign-language.pdf)\nconvert the parse tree into a sequence following a depth-first traversal in order to\nbe able to apply sequence-to-sequence models to it. The linearized version of the\nabove parse tree looks as follows: (S (N) (VP V N)).", "datasets": [{"dataset": "Penn Treebank", "description": "The Wall Street Journal section of the [Penn Treebank](https://catalog.ldc.upenn.edu/LDC99T42) is used for\nevaluating constituency parsers. Section 22 is used for development and Section 23 is used for evaluation.\nModels are evaluated based on F1. Most of the below models incorporate external data or features.\nFor a comparison of single models trained only on WSJ, refer to [<PERSON><PERSON><PERSON> and <PERSON> (2018)](https://arxiv.org/abs/1805.01052).\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "Penn Treebank", "url": "https://catalog.ldc.upenn.edu/LDC99T42"}, {"title": "<PERSON><PERSON><PERSON> and <PERSON> (2018)", "url": "2018)](https://arxiv.org/abs/1805.01052"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["F1 score"], "rows": [{"model_name": "Self-attentive encoder + ELMo", "metrics": {"F1 score": "95.13"}, "paper_title": "Constituency Parsing with a Self-Attentive Encoder", "paper_url": "https://arxiv.org/abs/1805.01052"}, {"model_name": "Model combination", "metrics": {"F1 score": "94.66"}, "paper_title": "Improving Neural Parsing by Disentangling Model Combination and Reranking Effects", "paper_url": "https://arxiv.org/abs/1707.03058"}, {"model_name": "In-order", "metrics": {"F1 score": "94.2"}, "paper_title": "In-Order Transition-based Constituent Parsing", "paper_url": "http://aclweb.org/anthology/Q17-1029"}, {"model_name": "Semi-supervised LSTM-LM", "metrics": {"F1 score": "93.8"}, "paper_title": "Parsing as Language Modeling", "paper_url": "http://www.aclweb.org/anthology/D16-1257"}, {"model_name": "Stack-only RNNG", "metrics": {"F1 score": "93.6"}, "paper_title": "What Do Recurrent Neural Network Grammars Learn About Syntax?", "paper_url": "https://arxiv.org/abs/1611.05774"}, {"model_name": "RNN Grammar", "metrics": {"F1 score": "﻿93.3"}, "paper_title": "Recurrent Neural Network Grammars", "paper_url": "https://www.aclweb.org/anthology/N16-1024"}, {"model_name": "Transformer", "metrics": {"F1 score": "92.7"}, "paper_title": "Attention Is All You Need", "paper_url": "https://arxiv.org/abs/1706.03762"}, {"model_name": "Semi-supervised LSTM", "metrics": {"F1 score": "92.1"}, "paper_title": "Grammar as a Foreign Language", "paper_url": "https://papers.nips.cc/paper/5635-grammar-as-a-foreign-language.pdf"}, {"model_name": "Self-trained parser", "metrics": {"F1 score": "92.1"}, "paper_title": "Effective Self-Training for Parsing", "paper_url": "https://pdfs.semanticscholar.org/6f0f/64f0dab74295e5eb139c160ed79ff262558a.pdf"}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Sentiment analysis", "description": "Sentiment analysis is the task of classifying the polarity of a given text.", "datasets": [{"dataset": "IMDb", "description": "The [IMDb dataset](https://ai.stanford.edu/~ang/papers/acl11-WordVectorsSentimentAnalysis.pdf) is a binary\nsentiment analysis dataset consisting of 50,000 reviews from the Internet Movie Database (IMDb) labeled as positive or\nnegative. The dataset contains an even number of positive and negative reviews. Only highly polarizing reviews are considered. \nA negative review has a score ≤ 4 out of 10, and a positive review has a score ≥ 7 out of 10. No more than 30 reviews are \nincluded per movie. Models are evaluated based on accuracy.", "dataset_links": [{"title": "IMDb dataset", "url": "https://ai.stanford.edu/~ang/papers/acl11-WordVectorsSentimentAnalysis.pdf"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "ULMFiT", "metrics": {"Accuracy": "95.4"}, "paper_title": "Universal Language Model Fine-tuning for Text Classification", "paper_url": "https://arxiv.org/abs/1801.06146"}, {"model_name": "Block-sparse LSTM", "metrics": {"Accuracy": "94.99"}, "paper_title": "GPU Kernels for Block-Sparse Weights", "paper_url": "https://s3-us-west-2.amazonaws.com/openai-assets/blocksparse/blocksparsepaper.pdf"}, {"model_name": "oh-LSTM", "metrics": {"Accuracy": "94.1"}, "paper_title": "Supervised and Semi-Supervised Text Categorization using LSTM for Region Embeddings", "paper_url": "https://arxiv.org/abs/1602.02373"}, {"model_name": "Virtual adversarial training", "metrics": {"Accuracy": "94.1"}, "paper_title": "Adversarial Training Methods for Semi-Supervised Text Classification", "paper_url": "https://arxiv.org/abs/1605.07725"}, {"model_name": "BCN+Char+CoVe", "metrics": {"Accuracy": "91.8"}, "paper_title": "Learned in Translation: Contextualized Word Vectors", "paper_url": "https://arxiv.org/abs/1708.00107"}]}}, {"dataset": "SST", "description": "The [Stanford Sentiment Treebank](https://nlp.stanford.edu/sentiment/index.html) \ncontains of 215,154 phrases with fine-grained sentiment labels in the parse trees\nof 11,855 sentences in movie reviews. Models are evaluated either on fine-grained\n(five-way) or binary classification based on accuracy.\n\nFine-grained classification (SST-5, 94,2k examples):\n\n\nBinary classification (SST-2, 56.4k examples):", "dataset_links": [{"title": "Stanford Sentiment Treebank", "url": "https://nlp.stanford.edu/sentiment/index.html"}], "subdatasets": [{"subdataset": "Fine-grained classification (SST-5, 94,2k examples)", "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "BCN+ELMo", "metrics": {"Accuracy": "54.7"}, "paper_title": "Deep contextualized word representations", "paper_url": "https://arxiv.org/abs/1802.05365"}, {"model_name": "BCN+Char+CoVe", "metrics": {"Accuracy": "53.7"}, "paper_title": "Learned in Translation: Contextualized Word Vectors", "paper_url": "https://arxiv.org/abs/1708.00107"}]}}, {"subdataset": "Binary classification (SST-2, 56.4k examples)", "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "Block-sparse LSTM", "metrics": {"Accuracy": "93.2"}, "paper_title": "GPU Kernels for Block-Sparse Weights", "paper_url": "https://s3-us-west-2.amazonaws.com/openai-assets/blocksparse/blocksparsepaper.pdf"}, {"model_name": "bmLSTM", "metrics": {"Accuracy": "91.8"}, "paper_title": "Learning to Generate Reviews and Discovering Sentiment", "paper_url": "https://arxiv.org/abs/1704.01444"}, {"model_name": "BCN+Char+CoVe", "metrics": {"Accuracy": "90.3"}, "paper_title": "Learned in Translation: Contextualized Word Vectors", "paper_url": "https://arxiv.org/abs/1708.00107"}, {"model_name": "Neural Semantic Encoder", "metrics": {"Accuracy": "89.7"}, "paper_title": "Neural Semantic Encoders", "paper_url": "http://www.aclweb.org/anthology/E17-1038"}, {"model_name": "BLSTM-2DCNN", "metrics": {"Accuracy": "89.5"}, "paper_title": "Text Classification Improved by Integrating Bidirectional LSTM with Two-dimensional Max Pooling", "paper_url": "http://www.aclweb.org/anthology/C16-1329"}]}}]}, {"dataset": "Yelp", "description": "The [Yelp Review dataset](https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf)\nconsists of more than 500,000 Yelp reviews. There is both a binary and a fine-grained (five-class)\nversion of the dataset. Models are evaluated based on error (1 - accuracy; lower is better).\n\nFine-grained classification: \n\n\nBinary classification:", "dataset_links": [{"title": "Yelp Review dataset", "url": "https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf"}], "subdatasets": [{"subdataset": "Fine-grained classification", "sota": {"metrics": ["Error"], "rows": [{"model_name": "ULMFiT", "metrics": {"Error": "29.98"}, "paper_title": "Universal Language Model Fine-tuning for Text Classification", "paper_url": "https://arxiv.org/abs/1801.06146"}, {"model_name": "DPCNN", "metrics": {"Error": "30.58"}, "paper_title": "Deep Pyramid Convolutional Neural Networks for Text Categorization", "paper_url": "http://aclweb.org/anthology/P17-1052"}, {"model_name": "CNN", "metrics": {"Error": "32.39"}, "paper_title": "Supervised and Semi-Supervised Text Categorization using LSTM for Region Embeddings", "paper_url": "https://arxiv.org/abs/1602.02373"}, {"model_name": "Char-level CNN", "metrics": {"Error": "37.95"}, "paper_title": "Character-level Convolutional Networks for Text Classification", "paper_url": "https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf"}]}}, {"subdataset": "Binary classification", "sota": {"metrics": ["Error"], "rows": [{"model_name": "ULMFiT", "metrics": {"Error": "2.16"}, "paper_title": "Universal Language Model Fine-tuning for Text Classification", "paper_url": "https://arxiv.org/abs/1801.06146"}, {"model_name": "DPCNN", "metrics": {"Error": "2.64"}, "paper_title": "Deep Pyramid Convolutional Neural Networks for Text Categorization", "paper_url": "http://aclweb.org/anthology/P17-1052"}, {"model_name": "CNN", "metrics": {"Error": "2.90"}, "paper_title": "Supervised and Semi-Supervised Text Categorization using LSTM for Region Embeddings", "paper_url": "https://arxiv.org/abs/1602.02373"}, {"model_name": "Char-level CNN", "metrics": {"Error": "4.88"}, "paper_title": "Character-level Convolutional Networks for Text Classification", "paper_url": "https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf"}]}}]}, {"dataset": "Sem<PERSON><PERSON>", "description": "SemEval (International Workshop on Semantic Evaluation) has a specific task for Sentiment analysis.\nLatest year overview of such task (Task 4) can be reached at: http://www.aclweb.org/anthology/S17-2088\n\nSemEval-2017 Task 4 consists of five subtasks, each offered for both Arabic and English:\n\n1. Subtask A: Given a tweet, decide whether it expresses POSITIVE, NEGATIVE or NEUTRAL\nsentiment.\n\n2. Subtask B: Given a tweet and a topic, classify the sentiment conveyed towards that\ntopic on a two-point scale: POSITIVE vs. NEGATIVE.\n\n3. Subtask C: Given a tweet and a topic, classify the sentiment conveyed in the\ntweet towards that topic on a five-point scale: STRONGLYPOSITIVE, WEAKLYPOSITIVE,\nNEUTRAL, WEA<PERSON>LY<PERSON>GATIVE, and STRONGLYNEGATIVE.\n\n4. Subtask D: Given a set of tweets about a topic, estimate the distribution of tweets\nacross the POSITIVE and NEGATIVE classes. \n\n5. Subtask E: Given a set of tweets about a topic, estimate the distribution of tweets\nacross the five classes: STRONGLYPOSITIVE, WEA<PERSON><PERSON><PERSON><PERSON>ITIVE, NEUTRAL, WEAKL<PERSON><PERSON><PERSON>TIVE, and ST<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>TI<PERSON>.\n\nSubtask A  results:", "sota": {"metrics": ["F1-score"], "rows": [{"model_name": "LSTMs+CNNs ensemble with multiple conv. ops", "metrics": {"F1-score": "0.685"}, "paper_title": "BB twtr at SemEval-2017 Task 4: Twitter Sentiment Analysis with CNNs and LSTMs", "paper_url": "http://www.aclweb.org/anthology/S17-2094"}, {"model_name": "Deep Bi-LSTM+attention", "metrics": {"F1-score": "0.677"}, "paper_title": "DataStories at SemEval-2017 Task 4: Deep LSTM with Attention for Message-level and Topic-based Sentiment Analysis", "paper_url": "http://aclweb.org/anthology/S17-2126"}]}}], "subtasks": [{"task": "Aspect-based sentiment analysis", "description": "", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Sentihood", "description": "[Sentihood](http://www.aclweb.org/anthology/C16-1146) is a dataset for targeted aspect-based sentiment analysis (TABSA), which aims\nto identify fine-grained polarity towards a specific aspect. The dataset consists of 5,215 sentences,\n3,862 of which contain a single target, and the remainder multiple targets. F1 is used as evaluation metric\nfor aspect detection and accuracy as evaluation metric for sentiment analysis.", "dataset_links": [{"title": "Sentihood", "url": "http://www.aclweb.org/anthology/C16-1146) is a dataset for targeted aspect-based sentiment analysis (TABSA"}], "sota": {"metrics": ["Aspect", "Sentiment"], "rows": [{"model_name": "<PERSON> et al.", "metrics": {"Aspect": "78.5", "Sentiment": "91.0"}, "paper_title": "Recurrent Entity Networks with Delayed Memory Update for Targeted Aspect-based Sentiment Analysis", "paper_url": "http://aclweb.org/anthology/N18-2045", "code_links": [{"title": "Official", "url": "https://github.com/liufly/delayed-memory-update-entnet"}]}, {"model_name": "SenticLSTM", "metrics": {"Aspect": "78.2", "Sentiment": "89.3"}, "paper_title": "Targeted Aspect-Based Sentiment Analysis via Embedding Commonsense Knowledge into an Attentive LSTM", "paper_url": "http://sentic.net/sentic-lstm.pdf", "code_links": []}, {"model_name": "LSTM-LOC", "metrics": {"Aspect": "69.3", "Sentiment": "81.9"}, "paper_title": "Sentihood: Targeted aspect based sentiment analysis dataset for urban neighbourhoods", "paper_url": "http://www.aclweb.org/anthology/C16-1146", "code_links": []}]}}]}]}, {"task": "Subjectivity analysis", "description": "A related task to sentiment analysis is the subjectivity analysis with the goal of labeling an opinion as either subjective or objective.", "datasets": [{"dataset": "SUBJ", "description": "[Subjectivity dataset](http://www.cs.cornell.edu/people/pabo/movie-review-data/) includes 5,000 subjective and 5,000 objective processed sentences. \n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "Subjectivity dataset", "url": "http://www.cs.cornell.edu/people/pabo/movie-review-data/"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Accuracy"], "rows": [{"model_name": "AdaSent", "metrics": {"Accuracy": "95.50"}, "paper_title": "Self-Adaptive Hierarchical Sentence Model", "paper_url": "https://arxiv.org/pdf/1504.05070.pdf"}, {"model_name": "CNN+MCFA", "metrics": {"Accuracy": "94.80"}, "paper_title": "Translations as Additional Contexts for Sentence Classification", "paper_url": "https://arxiv.org/abs/1806.05516"}, {"model_name": "Byte mLSTM", "metrics": {"Accuracy": "94.60"}, "paper_title": "Learning to Generate Reviews and Discovering Sentiment", "paper_url": "https://arxiv.org/pdf/1704.01444.pdf"}, {"model_name": "USE", "metrics": {"Accuracy": "93.90"}, "paper_title": "Universal Sentence Encoder", "paper_url": "https://arxiv.org/pdf/1803.11175.pdf"}, {"model_name": "Fast Dropout", "metrics": {"Accuracy": "93.60"}, "paper_title": "Fast Dropout Training", "paper_url": "http://proceedings.mlr.press/v28/wang13a.pdf"}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Shallow syntax", "description": "Shallow syntactic tasks provide an analysis of a text on the level of the syntactic structure \nof the text.", "subtasks": [{"task": "Chunking", "description": "Chunking, also known as shallow parsing, identifies continuous spans of tokens that form syntactic units such as noun phrases or verb phrases.\n\nExample:\n\n| Vinken | , | 61 | years | old |\n| --- | ---| --- | --- | --- |\n| B-NLP| I-NP | I-NP | I-NP | I-NP |", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}, "datasets": [{"dataset": "Penn Treebank", "description": "The [Penn Treebank](https://catalog.ldc.upenn.edu/LDC99T42) is typically used for evaluating chunking.\nSections 15-18 are used for training, section 19 for development, and and section 20\nfor testing. Models are evaluated based on F1.", "dataset_links": [{"title": "Penn Treebank", "url": "https://catalog.ldc.upenn.edu/LDC99T42"}], "sota": {"metrics": ["F1 score"], "rows": [{"model_name": "Flair embeddings", "metrics": {"F1 score": "96.72"}, "paper_title": "Contextual String Embeddings for Sequence Labeling", "paper_url": "http://aclweb.org/anthology/C18-1139"}, {"model_name": "JMT", "metrics": {"F1 score": "95.77"}, "paper_title": "A Joint Many-Task Model: Growing a Neural Network for Multiple NLP Tasks", "paper_url": "https://www.aclweb.org/anthology/D17-1206"}, {"model_name": "Low supervision", "metrics": {"F1 score": "95.57"}, "paper_title": "Deep multi-task learning with low level tasks supervised at lower layers", "paper_url": "http://anthology.aclweb.org/P16-2038"}, {"model_name": "Suzuki and <PERSON><PERSON><PERSON>", "metrics": {"F1 score": "95.15"}, "paper_title": "Semi-Supervised Sequential Labeling and Segmentation using Giga-word Scale Unlabeled Data", "paper_url": "https://aclanthology.info/pdf/P/P08/P08-1076.pdf"}, {"model_name": "NCRF++", "metrics": {"F1 score": "95.06"}, "paper_title": "NCRF++: An Open-source Neural Sequence Labeling Toolkit", "paper_url": "http://www.aclweb.org/anthology/P18-4013"}]}}]}, {"task": "Resolving the Scope and focus of negation", "description": "Scope of negation is the part of the meaning that is negated and focus the part of the scope that is most prominently negated (<PERSON><PERSON><PERSON> and <PERSON> 2002).\n\nExample:\n\n`[<PERSON> had] never [said %as much% before].`\n\nScope is enclosed in square brackets and focus marked between % signs.\n\nThe [CD-SCO (<PERSON>) dataset](https://www.clips.uantwerpen.be/sem2012-st-neg/data.html) is for scope detection.\n The [PB-FOC (PropBank Focus) dataset](https://www.clips.uantwerpen.be/sem2012-st-neg/data.html) is for focus detection.\nThe public leaderboard is available on the [*SEM Shared Task 2012 website](https://www.clips.uantwerpen.be/sem2012-st-neg/results.html).\n\n[Go back to the README](../README.md)", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Coreference resolution", "description": "Coreference resolution is the task of clustering mentions in text that refer to the same underlying real world entities.\n\nExample:\n\n```\n               +-----------+\n               |           |\nI voted for <PERSON> because he was most aligned with my values\", she said.\n |                                                 |            |\n +-------------------------------------------------+------------+\n```\n\n\"<PERSON>\", \"my\", and \"she\" belong to the same cluster and \"<PERSON>\" and \"he\" belong to the same cluster.", "datasets": [{"dataset": "CoNLL 2012", "description": "Experiments are conducted on the data of the [CoNLL-2012 shared task](http://www.aclweb.org/anthology/W12-4501), which\nuses OntoNotes coreference annotations. Papers\nreport the precision, recall, and F1 of the MUC, B3, and CEAFφ4 metrics using the official\nCoNLL-2012 evaluation scripts. The main evaluation metric is the average F1 of the three metrics.\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "CoNLL-2012 shared task", "url": "http://www.aclweb.org/anthology/W12-4501"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["Avg F1"], "rows": [{"model_name": "(<PERSON> et al., 2017)+ELMo", "metrics": {"Avg F1": "73.0"}, "paper_title": "Higher-order Coreference Resolution with Coarse-to-fine Inference", "paper_url": "http://aclweb.org/anthology/N18-2108", "code_links": [{"title": "Official", "url": "https://github.com/kentonl/e2e-coref"}]}, {"model_name": "(<PERSON> et al., 2017)+ELMo", "metrics": {"Avg F1": "70.4"}, "paper_title": "Deep contextualized word representatIions", "paper_url": "https://arxiv.org/abs/1802.05365", "code_links": []}, {"model_name": "<PERSON> et al.", "metrics": {"Avg F1": "67.2"}, "paper_title": "End-to-end Neural Coreference Resolution", "paper_url": "https://arxiv.org/abs/1707.07045", "code_links": []}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Dependency parsing", "description": "Dependency parsing is the task of extracting a dependency parse of a sentence that represents its grammatical\nstructure and defines the relationships between \"head\" words and words, which modify those heads.\n\nExample:\n\n```\n     root\n      |\n      | +-------dobj---------+\n      | |                    |\nnsubj | |   +------det-----+ | +-----nmod------+\n+--+  | |   |              | | |               |\n|  |  | |   |      +-nmod-+| | |      +-case-+ |\n+  |  + |   +      +      || + |      +      | |\nI  prefer  the  morning   flight  through  Denver\n```\n\nRelations among the words are illustrated above the sentence with directed, labeled\narcs from heads to dependents (+ indicates the dependent).", "datasets": [{"dataset": "Penn Treebank", "description": "Models are evaluated on the [Stanford Dependency](https://nlp.stanford.edu/software/dependencies_manual.pdf)\nconversion (**v3.3.0**) of the Penn Treebank with __predicted__ POS-tags. Punctuation symbols\nare excluded from the evaluation. Evaluation metrics are unlabeled attachment score (UAS) and\nlabeled attachment score (LAS). Here, we also mention the predicted POS tagging accuracy.\n\n\nThe following results are just for references:", "dataset_links": [{"title": "<PERSON> Dependency", "url": "https://nlp.stanford.edu/software/dependencies_manual.pdf"}], "subdatasets": [{"subdataset": "labeled attachment score (LAS). Here, we also mention the predicted POS tagging accuracy.", "sota": {"metrics": ["POS", "UAS", "LAS"], "rows": [{"model_name": "CVT + Multi-Task", "metrics": {"POS": "---", "UAS": "96.61", "LAS": "95.02"}, "paper_title": "Semi-Supervised Sequence Modeling with Cross-View Training", "paper_url": "https://arxiv.org/abs/1809.08370", "code_links": [{"title": "Official", "url": "https://github.com/tensorflow/models/tree/master/research/cvt_text"}]}, {"model_name": "Deep Biaffine", "metrics": {"POS": "97.3", "UAS": "95.44", "LAS": "93.76"}, "paper_title": "Deep Biaffine Attention for Neural Dependency Parsing", "paper_url": "https://arxiv.org/abs/1611.01734", "code_links": [{"title": "Official", "url": "https://github.com/tdozat/Parser-v1"}]}, {"model_name": "jPTDP", "metrics": {"POS": "97.97", "UAS": "94.51", "LAS": "92.87"}, "paper_title": "An improved neural network model for joint POS tagging and dependency parsing", "paper_url": "https://arxiv.org/abs/1807.03955", "code_links": [{"title": "Official", "url": "https://github.com/datquocnguyen/jPTDP"}]}, {"model_name": "<PERSON><PERSON> et al.", "metrics": {"POS": "97.44", "UAS": "94.61", "LAS": "92.79"}, "paper_title": "Globally Normalized Transition-Based Neural Networks", "paper_url": "https://www.aclweb.org/anthology/P16-1231", "code_links": []}, {"model_name": "Distilled neural FOG", "metrics": {"POS": "97.3", "UAS": "94.26", "LAS": "92.06"}, "paper_title": "Distilling an Ensemble of Greedy Dependency Parsers into One MST Parser", "paper_url": "https://arxiv.org/abs/1609.07561", "code_links": []}, {"model_name": "<PERSON> et al.", "metrics": {"POS": "97.44", "UAS": "93.99", "LAS": "92.05"}, "paper_title": "Structured Training for Neural Network Transition-Based Parsing", "paper_url": "http://anthology.aclweb.org/P/P15/P15-1032.pdf", "code_links": []}, {"model_name": "BIST transition-based parser", "metrics": {"POS": "97.3", "UAS": "93.9", "LAS": "91.9"}, "paper_title": "Simple and Accurate Dependency Parsing Using Bidirectional LSTM Feature Representations", "paper_url": "https://aclweb.org/anthology/Q16-1023", "code_links": [{"title": "Official", "url": "https://github.com/elikip/bist-parser/tree/master/barchybrid/src"}]}, {"model_name": "Arc-hybrid", "metrics": {"POS": "97.3", "UAS": "93.56", "LAS": "91.42"}, "paper_title": "Training with Exploration Improves a Greedy Stack-LSTM Parser", "paper_url": "https://arxiv.org/abs/1603.03793", "code_links": []}, {"model_name": "BIST graph-based parser", "metrics": {"POS": "97.3", "UAS": "93.1", "LAS": "91.0"}, "paper_title": "Simple and Accurate Dependency Parsing Using Bidirectional LSTM Feature Representations", "paper_url": "https://aclweb.org/anthology/Q16-1023", "code_links": [{"title": "Official", "url": "https://github.com/elikip/bist-parser/tree/master/bmstparser/src"}]}]}}, {"subdataset": "The following results are just for references", "sota": {"metrics": ["UAS", "LAS", "Note"], "rows": [{"model_name": "Stack-only RNNG", "metrics": {"UAS": "95.8", "LAS": "94.6", "Note": "Constituent parser"}, "paper_title": "What Do Recurrent Neural Network Grammars Learn About Syntax?", "paper_url": "https://arxiv.org/abs/1611.05774"}, {"model_name": "Semi-supervised LSTM-LM", "metrics": {"UAS": "95.9", "LAS": "94.1", "Note": "Constituent parser"}, "paper_title": "Parsing as Language Modeling", "paper_url": "http://www.aclweb.org/anthology/D16-1257"}, {"model_name": "Deep Biaffine", "metrics": {"UAS": "95.66", "LAS": "94.03", "Note": "Stanford conversion **v3.5.0**"}, "paper_title": "Deep Biaffine Attention for Neural Dependency Parsing", "paper_url": "https://arxiv.org/abs/1611.01734"}]}}]}]}, {"task": "Unsupervised dependency parsing", "description": "Unsupervised dependency parsing is the task of inferring the dependency parse of sentences without any labeled training data.", "subtasks": [{"task": "Penn Treebank", "description": "As with supervised parsing, models are evaluated against the Penn Treebank. The most common evaluation setup is to use\ngold POS-tags as input and to evaluate systems using the unlabeled attachment score (also called 'directed dependency\naccuracy').\n  \n| Model           | UAS | Paper / Source |  \n| ------------- | :-----:| ---- | \n| Iterative reranking (Le & Zuidema, 2015) | 66.2 | [Unsupervised Dependency Parsing - Let’s Use Supervised Parsers](http://www.aclweb.org/anthology/N15-1067) |\n| Combined System (<PERSON><PERSON><PERSON><PERSON> et al., 2013) | 64.4 | [Breaking Out of Local Optima with Count Transforms and Model Recombination - A Study in Grammar Induction](http://www.aclweb.org/anthology/D13-1204) |\n| Tree Substitution Grammar DMV (<PERSON>nsom & <PERSON>hn, 2010) | 55.7 | [Unsupervised Induction of Tree Substitution Grammars for Dependency Parsing](http://www.aclweb.org/anthology/D10-1117) |\n| Shared Logistic Normal DMV (<PERSON> & <PERSON>, 2009) | 41.4 | [Shared Logistic Normal Distributions for Soft Parameter Tying in Unsupervised Grammar Induction](http://www.aclweb.org/anthology/N09-1009) |\n| DMV (<PERSON> & <PERSON>, 2004) | 35.9 | [Corpus-Based Induction of Syntactic Structure - Models of Dependency and Constituency](http://www.aclweb.org/anthology/P04-1061) |\n\n[Go back to the README](../README.md)", "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Multi-task learning", "description": "Multi-task learning aims to learn multiple different tasks simultaneously while maximizing\nperformance on one or all of the tasks.", "datasets": [{"dataset": "DecaNLP", "description": "The [Natural Language Decathlon](https://arxiv.org/abs/1806.08730) (decaNLP) is a benchmark for studying general NLP \nmodels that can perform a variety of complex, natural language tasks. \nIt evaluates performance on ten disparate natural language tasks.\n\nResults can be seen on the [public leaderboard](https://decanlp.com/).", "dataset_links": [{"title": "Natural Language Decathlon", "url": "https://arxiv.org/abs/1806.08730) (decaNLP"}, {"title": "public leaderboard", "url": "https://decanlp.com/"}]}, {"dataset": "GLUE", "description": "The [General Language Understanding Evaluation benchmark](https://arxiv.org/abs/1804.07461) (GLUE)\nis a tool for evaluating and analyzing the performance of models across a diverse\nrange of existing natural language understanding tasks. Models are evaluated based on their\naverage accuracy across all tasks.\n\nThe state-of-the-art results can be seen on the public [GLUE leaderboard](https://gluebenchmark.com/leaderboard).\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "General Language Understanding Evaluation benchmark", "url": "https://arxiv.org/abs/1804.07461) (GLUE"}, {"title": "GLUE leaderboard", "url": "https://gluebenchmark.com/leaderboard"}, {"title": "Go back to the README", "url": "../README.md"}]}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}, {"task": "Named entity recognition", "description": "Named entity recognition (NER) is the task of tagging entities in text with their corresponding type.\nApproaches typically use BIO notation, which differentiates the beginning (B) and the inside (I) of entities.\nO is used for non-entity tokens.\n\nExample:\n\n| Mark | Watney | visited | Mars |\n| --- | ---| --- | --- |\n| B-PER | I-PER | O | B-LOC |", "datasets": [{"dataset": "CoNLL 2003 (English)", "description": "The [CoNLL 2003 NER task](http://www.aclweb.org/anthology/W03-0419.pdf) consists of newswire text from the Reuters RCV1 \ncorpus tagged with four different entity types (PER, LOC, ORG, MISC). Models are evaluated based on span-based F1 on the test set.", "dataset_links": [{"title": "CoNLL 2003 NER task", "url": "http://www.aclweb.org/anthology/W03-0419.pdf"}], "sota": {"metrics": ["F1"], "rows": [{"model_name": "Flair embeddings", "metrics": {"F1": "93.09"}, "paper_title": "Contextual String Embeddings for Sequence Labeling", "paper_url": "https://drive.google.com/file/d/17yVpFA7MmXaQFTe-HDpZuqw9fJlmzg56/view", "code_links": [{"title": "Flair framework", "url": "https://github.com/zalandoresearch/flair"}]}, {"model_name": "BERT Large", "metrics": {"F1": "92.8"}, "paper_title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding", "paper_url": "https://arxiv.org/abs/1810.04805", "code_links": []}, {"model_name": "CVT + Multi-Task", "metrics": {"F1": "92.61"}, "paper_title": "Semi-Supervised Sequence Modeling with Cross-View Training", "paper_url": "https://arxiv.org/abs/1809.08370", "code_links": [{"title": "Official", "url": "https://github.com/tensorflow/models/tree/master/research/cvt_text"}]}, {"model_name": "BERT Base", "metrics": {"F1": "92.4"}, "paper_title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding", "paper_url": "https://arxiv.org/abs/1810.04805", "code_links": []}, {"model_name": "BiLSTM-CRF+ELMo", "metrics": {"F1": "92.22"}, "paper_title": "Deep contextualized word representations", "paper_url": "https://arxiv.org/abs/1802.05365", "code_links": [{"title": "AllenNLP Project](https://allennlp.org/elmo) [AllenNLP GitHub", "url": "https://allennlp.org/elmo) [AllenNLP GitHub](https://github.com/allenai/allennlp"}]}, {"model_name": "<PERSON> et al.", "metrics": {"F1": "91.93"}, "paper_title": "Semi-supervised sequence tagging with bidirectional language models", "paper_url": "https://arxiv.org/abs/1705.00108", "code_links": []}, {"model_name": "HSCRF", "metrics": {"F1": "91.38"}, "paper_title": "Hybrid semi-Markov CRF for Neural Sequence Labeling", "paper_url": "http://aclweb.org/anthology/P18-2038", "code_links": [{"title": "HSCRF", "url": "https://github.com/ZhixiuYe/HSCRF-pytorch"}]}, {"model_name": "NCRF++", "metrics": {"F1": "91.35"}, "paper_title": "NCRF++: An Open-source Neural Sequence Labeling Toolkit", "paper_url": "http://www.aclweb.org/anthology/P18-4013", "code_links": [{"title": "NCRF++", "url": "https://github.com/jiesutd/NCRFpp"}]}, {"model_name": "LM-LSTM-CRF", "metrics": {"F1": "91.24"}, "paper_title": "Empowering Character-aware Sequence Labeling with Task-Aware Neural Language Model", "paper_url": "https://arxiv.org/pdf/1709.04109.pdf", "code_links": [{"title": "LM-LSTM-CRF", "url": "https://github.com/LiyuanLucasLiu/LM-LSTM-CRF"}]}, {"model_name": "<PERSON> et al.", "metrics": {"F1": "91.26"}, "paper_title": "Transfer Learning for Sequence Tagging with Hierarchical Recurrent Networks", "paper_url": "https://arxiv.org/abs/1703.06345", "code_links": []}, {"model_name": "<PERSON> and <PERSON><PERSON>", "metrics": {"F1": "91.21"}, "paper_title": "End-to-end Sequence Labeling via Bi-directional LSTM-CNNs-CRF", "paper_url": "https://arxiv.org/abs/1603.01354", "code_links": []}, {"model_name": "LSTM-CRF", "metrics": {"F1": "90.94"}, "paper_title": "Neural Architectures for Named Entity Recognition", "paper_url": "https://arxiv.org/abs/1603.01360", "code_links": []}]}}, {"dataset": "Long-tail emerging entities", "description": "The [WNUT 2017 Emerging Entities task](http://aclweb.org/anthology/W17-4418) operates over a wide range of English \ntext and focuses on generalisation beyond memorisation in high-variance environments. Scores are given both over\nentity chunk instances, and unique entity surface forms, to normalise the biasing impact of entities that occur frequently.\n\n| Feature | Train | Dev | Test |\n| --- | --- | --- | --- |\n| Posts | 3,395 | 1,009 | 1,287 |\n| Tokens | 62,729 | 15,733 | 23,394 |\n| NE tokens | 3,160 | 1,250 | 1,589 |\n\nThe data is annotated for six classes - person, location, group, creative work, product and corporation.\n\nLinks: [WNUT 2017 Emerging Entity task page](https://noisy-text.github.io/2017/emerging-rare-entities.html) (including direct download links for data and scoring script)", "dataset_links": [{"title": "WNUT 2017 Emerging Entities task", "url": "http://aclweb.org/anthology/W17-4418"}, {"title": "WNUT 2017 Emerging Entity task page", "url": "https://noisy-text.github.io/2017/emerging-rare-entities.html) (including direct download links for data and scoring script"}], "sota": {"metrics": ["F1", "F1 (surface form)"], "rows": [{"model_name": "Flair embeddings", "metrics": {"F1": "50.20", "F1 (surface form)": ""}, "paper_title": "Contextual String Embeddings for Sequence Labeling](http://aclweb.org/anthology/C18-1139) / [Flair framework", "paper_url": "http://aclweb.org/anthology/C18-1139) / [Flair framework](https://github.com/zalandoresearch/flair"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON> et al.", "metrics": {"F1": "45.55", "F1 (surface form)": ""}, "paper_title": "Modeling Noisiness to Recognize Named Entities using Multitask Neural Networks on Social Media", "paper_url": "http://aclweb.org/anthology/N18-1127.pdf"}, {"model_name": "SpinningBytes", "metrics": {"F1": "40.78", "F1 (surface form)": "39.33"}, "paper_title": "Transfer Learning and Sentence Level Features for Named Entity Recognition on Tweets", "paper_url": "http://aclweb.org/anthology/W17-4422.pdf"}]}}, {"dataset": "Ontonotes v5 (English)", "description": "The [Ontonotes corpus v5](https://catalog.ldc.upenn.edu/docs/LDC2013T19/OntoNotes-Release-5.0.pdf) is a richly annotated corpus with several layers of annotation, including named entities, coreference, part of speech, word sense, propositions, and syntactic parse trees. These annotations are over a large number of tokens, a broad cross-section of domains, and 3 languages (English, Arabic, and Chinese). The NER dataset (of interest here) includes 18 tags, consisting of 11 _types_ (PERSON, ORGANIZATION, etc) and 7 _values_ (DATE, PERCENT, etc), and contains 2 million tokens. The common datasplit used in NER is defined in [<PERSON><PERSON><PERSON> et al 2013](https://www.semanticscholar.org/paper/Towards-Robust-Linguistic-Analysis-using-OntoNotes-Pradhan-Moschitti/a94e4fe6f475e047be5dcc9077f445e496240852) and can be found [here](http://cemantix.org/data/ontonotes.html).\n\n\n\n\n[Go back to the README](../README.md)", "dataset_links": [{"title": "Ontonotes corpus v5](https://catalog.ldc.upenn.edu/docs/LDC2013T19/OntoNotes-Release-5.0.pdf) is a richly annotated corpus with several layers of annotation, including named entities, coreference, part of speech, word sense, propositions, and syntactic parse trees. These annotations are over a large number of tokens, a broad cross-section of domains, and 3 languages (English, Arabic, and Chinese). The NER dataset (of interest here) includes 18 tags, consisting of 11 _types_ (PERSON, ORGANIZATION, etc) and 7 _values_ (DATE, PERCENT, etc), and contains 2 million tokens. The common datasplit used in NER is defined in [<PERSON><PERSON><PERSON> et al 2013](https://www.semanticscholar.org/paper/Towards-Robust-Linguistic-Analysis-using-OntoNotes-Pradhan-Moschitti/a94e4fe6f475e047be5dcc9077f445e496240852) and can be found [here", "url": "https://catalog.ldc.upenn.edu/docs/LDC2013T19/OntoNotes-Release-5.0.pdf) is a richly annotated corpus with several layers of annotation, including named entities, coreference, part of speech, word sense, propositions, and syntactic parse trees. These annotations are over a large number of tokens, a broad cross-section of domains, and 3 languages (English, Arabic, and Chinese). The NER dataset (of interest here) includes 18 tags, consisting of 11 _types_ (PERSON, ORGANIZATION, etc) and 7 _values_ (DATE, PERCENT, etc), and contains 2 million tokens. The common datasplit used in NER is defined in [<PERSON><PERSON><PERSON> et al 2013](https://www.semanticscholar.org/paper/Towards-Robust-Linguistic-Analysis-using-OntoNotes-<PERSON><PERSON><PERSON>-<PERSON>schitti/a94e4fe6f475e047be5dcc9077f445e496240852) and can be found [here](http://cemantix.org/data/ontonotes.html"}, {"title": "Go back to the README", "url": "../README.md"}], "sota": {"metrics": ["F1"], "rows": [{"model_name": "Flair embeddings", "metrics": {"F1": "89.71"}, "paper_title": "Contextual String Embeddings for Sequence Labeling", "paper_url": "http://aclweb.org/anthology/C18-1139", "code_links": [{"title": "Official", "url": "https://github.com/zalandoresearch/flair"}]}, {"model_name": "CVT + Multi-Task", "metrics": {"F1": "88.81"}, "paper_title": "Semi-Supervised Sequence Modeling with Cross-View Training", "paper_url": "https://arxiv.org/abs/1809.08370", "code_links": [{"title": "Official", "url": "https://github.com/tensorflow/models/tree/master/research/cvt_text"}]}, {"model_name": "Bi-LSTM-CRF + Lexical Features", "metrics": {"F1": "87.95"}, "paper_title": "Robust Lexical Features for Improved Neural Network Named-Entity Recognition", "paper_url": "https://arxiv.org/pdf/1806.03489.pdf", "code_links": []}, {"model_name": "BiLSTM-CRF", "metrics": {"F1": "86.99"}, "paper_title": "Fast and Accurate Entity Recognition with Iterated Dilated Convolutions", "paper_url": "https://arxiv.org/pdf/1702.02098.pdf", "code_links": [{"title": "Official", "url": "https://github.com/iesl/dilated-cnn-ner"}]}, {"model_name": "Iterated Dilated CNN", "metrics": {"F1": "86.84"}, "paper_title": "Fast and Accurate Entity Recognition with Iterated Dilated Convolutions", "paper_url": "https://arxiv.org/pdf/1702.02098.pdf", "code_links": [{"title": "Official", "url": "https://github.com/iesl/dilated-cnn-ner"}]}, {"model_name": "Joint Model", "metrics": {"F1": "84.04"}, "paper_title": "A Joint Model for Entity Analysis: Coreference, Typing, and Linking", "paper_url": "https://pdfs.semanticscholar.org/2eaf/f2205c56378e715d8d12c521d045c0756a76.pdf", "code_links": []}, {"model_name": "Averaged Perceptron", "metrics": {"F1": "83.45"}, "paper_title": "Design Challenges and Misconceptions in Named Entity Recognition](https://www.semanticscholar.org/paper/Design-Challenges-and-Misconceptions-in-Named-<PERSON><PERSON><PERSON>-<PERSON>/27496a2ee337db705e7c611dea1fd8e6f41437c2) (These scores reported in ([<PERSON><PERSON><PERSON> and <PERSON> 2014", "paper_url": "https://www.semanticscholar.org/paper/Design-Challenges-and-Misconceptions-in-Named-<PERSON><PERSON><PERSON>-<PERSON>/27496a2ee337db705e7c611dea1fd8e6f41437c2) (These scores reported in ([<PERSON><PERSON><PERSON> and <PERSON> 2014](https://pdfs.semanticscholar.org/2eaf/f2205c56378e715d8d12c521d045c0756a76.pdf))", "code_links": [{"title": "Official", "url": "https://github.com/CogComp/cogcomp-nlp/tree/master/ner"}]}]}}], "source_link": {"title": "NLP-progress", "url": "https://github.com/sebastianruder/NLP-progress"}}]
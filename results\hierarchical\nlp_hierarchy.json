{"domain": "nlp", "hierarchical_tasks": [{"name": "1 Image, 2*2 Stitchi", "children": [{"name": "Pose Estimation", "children": []}, {"name": "Style Transfer", "children": [], "description": "", "dataset_count": 20, "benchmarks": ["WikiArt", "StyleBench", "01/01/1967' AND 2*3*8=6*8 AND 'AncJ'='AncJ", "GYAFC", "^(#$!@#$)(()))******"], "metrics": [], "task_id": "", "area": ""}, {"name": "Text-to-Image Generation", "children": [{"name": "text-guided-image-editing", "children": []}, {"name": "Text-based Image Editing", "children": []}, {"name": "Concept Alignment", "children": []}, {"name": "Zero-Shot Text-to-Image Generation", "children": []}, {"name": "Conditional Text-to-Image Synthesis", "children": []}, {"name": "Consistent Character Generation", "children": []}, {"name": "DreamBooth Personalized Generation", "children": []}]}, {"name": "Music Generation", "children": [], "description": "", "dataset_count": 33, "benchmarks": ["Song Describer Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Voice Conversion", "children": []}, {"name": "Image Deblurring", "children": []}, {"name": "Virtual Try-on", "children": []}, {"name": "Talking Face Generation", "children": []}, {"name": "Image to Video Generation", "children": []}, {"name": "Pneumonia Detection", "children": []}]}, {"name": "2D Semantic Segmentation", "children": [{"name": "Image Segmentation", "children": []}, {"name": "Text Style Transfer", "children": [{"name": "Formality Style Transfer", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Word Attribute Transfer", "children": []}, {"name": "Semi-Supervised Formality Style Transfer", "children": []}], "description": "", "dataset_count": 7, "benchmarks": ["Yelp Review Dataset (Large)", "Yelp Review Dataset (Small)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Scene Parsing", "children": []}, {"name": "Reflection Removal", "children": []}, {"name": "Foreground Segmentation", "children": []}, {"name": "Continual Semantic Segmentation", "children": []}, {"name": "Building Damage Assessment", "children": []}, {"name": "Human Part Segmentation", "children": []}, {"name": "Overlapped 15-1", "children": []}, {"name": "Disjoint 15-1", "children": []}]}, {"name": "3D Action Recognition", "children": [{"name": "Skeleton Based Action Recognition", "children": []}, {"name": "Model Editing", "children": [{"name": "knowledge editing", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Image Manipulation Detection", "children": []}, {"name": "motion retargeting", "children": []}, {"name": "Zero Shot Skeletal Action Recognition", "children": []}, {"name": "Generalized Zero Shot skeletal action recognition", "children": []}]}, {"name": "Abuse Detection", "children": [{"name": "Hate Speech Detection", "children": [{"name": "Hope Speech Detection", "children": [{"name": "<PERSON> Speech Detection for English", "children": []}, {"name": "<PERSON> Speech Detection for Tamil", "children": []}, {"name": "Hope Speech Detection for Malayalam", "children": []}]}, {"name": "Hate Speech Normalization", "children": []}, {"name": "Hate Speech Detection CrisisHateMM Benchmark", "children": []}]}]}, {"name": "Ad-Hoc Information Retrieval", "children": [{"name": "Document Ranking", "children": [{"name": "Session Search", "children": []}], "description": "", "dataset_count": 7, "benchmarks": ["ClueWeb09-B", "DaReCzech"], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Age And Gender Classification", "children": [{"name": "Author Profiling", "children": []}], "description": "", "dataset_count": 6, "benchmarks": ["BN-AuthProf", "Adience Age", "Adience Gender"], "metrics": [], "task_id": "", "area": ""}, {"name": "Anaphora Resolution", "children": [{"name": "Bridging Anaphora Resolution", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Abstract Anaphora Resolution", "children": []}], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Attribute Extraction", "children": [{"name": "legal outcome extraction", "children": []}]}, {"name": "Authorship Attribution", "children": [{"name": "Source Code Authorship Attribution", "children": []}]}, {"name": "Bias Detection", "children": [{"name": "Selection bias", "children": []}]}, {"name": "Binary Classification", "children": [{"name": "LLM-generated Text Detection", "children": []}, {"name": "Cancer-no cancer per image classification", "children": []}, {"name": "Cancer-no cancer per breast classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Suspicous (BIRADS 4,5)-no suspicous (BIRADS 1,2,3) per image classification", "children": []}, {"name": "Stable MCI vs Progressive MCI", "children": []}, {"name": "Cancer-no cancer per view classification", "children": []}], "description": "", "dataset_count": 17, "benchmarks": ["dev", "quora tr", "Glyphnet Dataset", "quora duplicates", "wmt16", "snli tr", "msmarco tr", "xnli tr", "kickstarter", "fake"], "metrics": [], "task_id": "", "area": ""}, {"name": "Binary text classification", "children": [{"name": "Detection of potentially void clauses", "children": []}], "description": "", "dataset_count": 10, "benchmarks": ["MixSet (Binary)", "MAGE (Arbitrary-domains & Arbitrary-models)", "Ghostbuster (All Domains)", "ECHR Non-Anonymized", "TURINGBENCH (Turing Test, GPT-3)", "TweepFake", "TURINGBENCH (Turing Test, FAIR_wmt20)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Biomedical Information Retrieval", "children": [{"name": "PICO", "children": []}, {"name": "SpO2 estimation", "children": []}]}, {"name": "<PERSON><PERSON><PERSON>", "children": [{"name": "Dialogue Generation", "children": [{"name": "Multi-modal Dialogue Generation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 34, "benchmarks": ["Ubuntu Dialogue (Entity)", "Twitter Dialogue (<PERSON>un)", "FusedChat", "Persona<PERSON><PERSON><PERSON>", "CMU-DoG", "Ubuntu Dialogue (Cmd)", "PG-19", "Ubuntu Dialogue (Tense)", "Reddit (multi-ref)", "Amazon-5"], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 11, "benchmarks": ["AlpacaEval"], "metrics": [], "task_id": "", "area": ""}, {"name": "Chinese", "children": [{"name": "Chinese Word Segmentation", "children": []}, {"name": "Handwritten Chinese Text Recognition", "children": []}, {"name": "Offline Handwritten Chinese Character Recognition", "children": []}, {"name": "Chinese Zero Pronoun Resolution", "children": []}, {"name": "Chinese Spelling Error Correction", "children": []}]}, {"name": "Classification", "children": [{"name": "Text Classification", "children": [{"name": "Document Classification", "children": [{"name": "Page Stream Segmentation", "children": []}], "description": "", "dataset_count": 18, "benchmarks": ["Recipe", "SciDocs (MAG)", "Yelp-14", "SciDocs (MeSH)", "BBCSport", "MPQA", "AAPD", "Cora", "Reuters-21578", "Reuters De-En"], "metrics": [], "task_id": "", "area": ""}, {"name": "Topic Models", "children": [{"name": "Topic coverage", "children": []}, {"name": "Dynamic Topic Modeling", "children": []}]}, {"name": "Emotion Classification", "children": []}, {"name": "Sentence Classification", "children": [], "description": "", "dataset_count": 14, "benchmarks": ["CHIP-CTC", "ScienceCite", "SciCite", "PubMed 20k RCT", "Paper Field", "ACL-ARC"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Label Text Classification", "children": [], "description": "", "dataset_count": 13, "benchmarks": ["MIMIC-III", "USPTO-3M", "Wiki-30K", "EUR-Lex", "MIMIC-III-50", "RCV1", "AAPD", "Amazon-12K", "CC3M-TagMask", "Reuters-21578"], "metrics": [], "task_id": "", "area": ""}, {"name": "Few-Shot Text Classification", "children": [{"name": "Zero-Shot Out-of-Domain Detection", "children": []}], "description": "", "dataset_count": 4, "benchmarks": ["ODIC 10-way (10-shot)", "RAFT", "SST-5", "Average on NLP datasets", "ODIC 5-way (5-shot)", "ODIC 5-way (10-shot)", "Amazon Counterfeit", "ODIC 10-way (5-shot)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Categorization", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Semi-Supervised Text Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Coherence Evaluation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Toxic Comment Classification", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 167, "benchmarks": ["HateXplain", "Overruling", "Twitter Sentiment Analysis", "GLUE", "reuters21578", "NICE-45", "RCV1", "OneStopEnglish (Readability Assessment)", "20 Newsgroups", "IMDb"], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Classification", "children": [], "description": "", "dataset_count": 54, "benchmarks": ["Citeseer", "IPC-grounded", "NC1", "HIV-fMRI-77", "IMDB-BINARY", "MUTAG", "MalNet-Tiny", "MSRC-21 (per-class)", "UK Biobank Brain MRI", "CSL"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-class Classification", "children": [], "description": "", "dataset_count": 14, "benchmarks": ["COVID-19 CXR Dataset", "Training and validation dataset of capsule vision 2024 challenge.", " Reuters-52", "COVID chest X-ray", "TII-SSRC-23"], "metrics": [], "task_id": "", "area": ""}, {"name": "Audio Classification", "children": [], "description": "", "dataset_count": 43, "benchmarks": ["Multimodal PISA", "DiCOVA", "EPIC-KITCHENS-100", "MeerKAT: <PERSON><PERSON><PERSON> Audio Transcripts", "DEEP-VOICE: DeepFake Voice Recognition", "EPIC-SOUNDS", "ICBHI Respiratory Sound Database", "GTZAN", "Common Voice 16.1", "MNIST"], "metrics": [], "task_id": "", "area": ""}, {"name": "Medical Image Classification", "children": []}, {"name": "Plant Phenotyping", "children": []}, {"name": "Classifier calibration", "children": []}, {"name": "Morphology classification", "children": []}, {"name": "Multi-modal Classification", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["VGG-Sound", "AudioSet"], "metrics": [], "task_id": "", "area": ""}, {"name": "IoT Device Identification", "children": []}], "description": "", "dataset_count": 222, "benchmarks": ["RTE", "Radar Dataset (DIAT-μRadHAR: Radar micro-Doppler Signature dataset for Human Suspicious Activity Recognition)", "MixedWM38", "N-ImageNet", "Chest X-Ray Images (Pneumonia)", "MHIST", "SGD", "HOWS long", "Coordinated Reply Attacks in Influence Operations: Characterization and Detection", "CWRU Bearing Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Clinical Concept Extraction", "children": [{"name": "Clinical Information Retreival", "children": []}]}, {"name": "Code Generation", "children": [{"name": "Code Translation", "children": [], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Code Documentation Generation", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["CodeSearchNet - JavaScript", "CodeSearchNet", "CodeSearchNet - Ruby", "CodeSearchNet - Java", "CodeSearchNet - Python", "CodeSearchNet - Php", "CodeSearchNet - Go"], "metrics": [], "task_id": "", "area": ""}, {"name": "GitHub issue resolution", "children": []}, {"name": "Class-level Code Generation", "children": []}, {"name": "Library-Oriented Code Generation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 70, "benchmarks": ["BigCodeBench-Instruct", "HumanEval-ET", "Multi-Source Python Code Corpus", "TACO-BAAI", "WebApp1K-React", "CoNaLa-Ext", "PECC", "Android Repos", "Django", "MBPP-ET"], "metrics": [], "task_id": "", "area": ""}, {"name": "Common Sense Reasoning", "children": [{"name": "HellaSwag", "children": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "children": []}, {"name": "Physical Commonsense Reasoning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Riddle Sense", "children": []}, {"name": "Causal Judgment", "children": []}, {"name": "<PERSON><PERSON><PERSON>", "children": []}, {"name": "Anachronisms", "children": []}, {"name": "Disambiguation QA", "children": []}, {"name": "Discourse Marker Prediction", "children": []}, {"name": "Empirical Judgments", "children": []}], "description": "", "dataset_count": 56, "benchmarks": ["RWSD", "ReCoRD", "CODAH", "PARus", "Russian Event2Mind", "Visual Dialog v0.9", "Event2Mind test", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ARC (Challenge)", "BIG-bench (Sports Understanding)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Constituency Parsing", "children": [{"name": "Constituency Grammar Induction", "children": []}], "description": "", "dataset_count": 6, "benchmarks": ["CTB7", "ATB", "Penn Treebank", "CTB5"], "metrics": [], "task_id": "", "area": ""}, {"name": "Constrained Clustering", "children": [{"name": "Only Connect Walls Dataset Task 1 (Grouping)", "children": []}, {"name": "Incremental Constrained Clustering", "children": []}]}, {"name": "Continual Learning", "children": [{"name": "Class Incremental Learning", "children": []}, {"name": "Continual Named Entity Recognition", "children": [{"name": "FG-1-PG-1", "children": []}]}, {"name": "unsupervised class-incremental learning", "children": []}, {"name": "Continual Panoptic Segmentation", "children": []}, {"name": "TiROD", "children": []}]}, {"name": "Conversational Response Generation", "children": [{"name": "Personalized and Emotional Conversation", "children": []}], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Coreference Resolution", "children": [{"name": "coreference-resolution", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross Document Coreference Resolution", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 45, "benchmarks": ["STM-coref", "Quizbowl", "WikiCoref", "GAP", "CoNLL12", "XWinograd EN", "DocRED-IE", "The ARRAU Corpus", "CoNLL 2012", "Winograd <PERSON> Challenge"], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual", "children": [{"name": "Cross-Lingual Transfer", "children": [{"name": "Zero-Shot Cross-Lingual Transfer", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual NER", "children": [], "description": "", "dataset_count": 9, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 15, "benchmarks": ["XCOPA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual Document Classification", "children": [{"name": "News Classification", "children": [], "description": "", "dataset_count": 12, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 2, "benchmarks": ["MLDoc Zero-Shot English-to-Japanese", "MLDoc Zero-Shot German-to-French", "MLDoc Zero-Shot English-to-Russian", "MLDoc Zero-Shot English-to-French", "MLDoc Zero-Shot English-to-Italian", "MLDoc Zero-Shot English-to-Spanish", "Reuters RCV1/RCV2 German-to-English", "Reuters RCV1/RCV2 English-to-German", "MLDoc Zero-Shot English-to-German", "MLDoc Zero-Shot English-to-Chinese"], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual Entity Linking", "children": [{"name": "Variable Disambiguation", "children": []}], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Language Text Summarization", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Modal Retrieval", "children": [{"name": "Image-text matching", "children": []}, {"name": "Cross-modal retrieval with noisy correspondence", "children": []}, {"name": "multilingual cross-modal retrieval", "children": []}, {"name": "Zero-shot Composed Person Retrieval", "children": []}, {"name": "Cross-Modal Retrieval on RSITMD", "children": []}]}, {"name": "Data Augmentation", "children": [{"name": "Image Augmentation", "children": []}, {"name": "Text Augmentation", "children": []}]}, {"name": "Data Integration", "children": [{"name": "Entity Alignment", "children": [{"name": "Multi-modal Entity Alignment", "children": []}]}, {"name": "Entity Resolution", "children": [{"name": "Blocking", "children": []}]}, {"name": "Table annotation", "children": [{"name": "Column Type Annotation", "children": []}, {"name": "Cell Entity Annotation", "children": []}, {"name": "Columns Property Annotation", "children": []}, {"name": "Table Type Detection", "children": []}, {"name": "Row Annotation", "children": []}, {"name": "Metric-Type Identification", "children": []}]}]}, {"name": "Data Mining", "children": [{"name": "Argument Mining", "children": [{"name": "Key Point Matching", "children": []}, {"name": "Component Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Argument Pair Extraction (APE)", "children": []}, {"name": "Claim Extraction with Stance Classification (CESC)", "children": []}, {"name": "Claim-Evidence Pair Extraction (CEPE)", "children": []}, {"name": "<PERSON><PERSON><PERSON>", "children": []}]}, {"name": "Opinion Mining", "children": [], "description": "", "dataset_count": 11, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "cognitive diagnosis", "children": []}, {"name": "Subgroup Discovery", "children": []}, {"name": "Sequential Pattern Mining", "children": []}, {"name": "Parallel Corpus Mining", "children": []}, {"name": "CSV dialect detection", "children": []}]}, {"name": "De-identification", "children": [{"name": "Privacy Preserving Deep Learning", "children": []}, {"name": "Full-body anonymization", "children": []}]}, {"name": "Deep Clustering", "children": [{"name": "Trajectory Clustering", "children": []}, {"name": "NONPARAMETRIC DEEP CLUSTERING", "children": []}, {"name": "Deep Nonparametric Clustering", "children": []}]}, {"name": "Deep Learning", "children": [{"name": "Polynomial Neural Networks", "children": []}]}, {"name": "Dependency Parsing", "children": [{"name": "Transition-Based Dependency Parsing", "children": []}, {"name": "Prepositional Phrase Attachment", "children": []}, {"name": "Unsupervised Dependency Parsing", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Dependency Grammar Induction", "children": []}, {"name": "Cross-lingual zero-shot dependency parsing", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 15, "benchmarks": ["UD2.5 test", "CoNLL-2009", "Spoken Corpus", "Sequoia Treebank", "Chinese Treebank", "Penn Treebank", "DaNE", "GENIA - LAS", "Universal Dependency Treebank", "Tweebank"], "metrics": [], "task_id": "", "area": ""}, {"name": "Dialogue", "children": [{"name": "Dialogue Generation", "children": [{"name": "Multi-modal Dialogue Generation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 34, "benchmarks": ["Ubuntu Dialogue (Entity)", "Twitter Dialogue (<PERSON>un)", "FusedChat", "Persona<PERSON><PERSON><PERSON>", "CMU-DoG", "Ubuntu Dialogue (Cmd)", "PG-19", "Ubuntu Dialogue (Tense)", "Reddit (multi-ref)", "Amazon-5"], "metrics": [], "task_id": "", "area": ""}, {"name": "Dialogue State Tracking", "children": []}, {"name": "Task-Oriented Dialogue Systems", "children": [{"name": "SSTOD", "children": []}], "description": "", "dataset_count": 22, "benchmarks": ["<PERSON><PERSON><PERSON>", "KVRET", "MULTIWOZ 2.0", "SGD"], "metrics": [], "task_id": "", "area": ""}, {"name": "Visual Dialog", "children": []}, {"name": "Dialogue Understanding", "children": [{"name": "Spoken Language Understanding", "children": [], "description": "", "dataset_count": 14, "benchmarks": ["Snips-SmartLights", "Snips-SmartSpeaker", "Spoken-SQuAD", "Timers and Such", "Fluent Speech Commands"], "metrics": [], "task_id": "", "area": ""}, {"name": "Dialogue Safety Prediction", "children": []}], "description": "", "dataset_count": 9, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Empathetic Response Generation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Dialogue Management", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Goal-Oriented Dialog", "children": [{"name": "User Simulation", "children": []}]}, {"name": "Dialogue Act Classification", "children": []}, {"name": "Goal-Oriented Dialogue Systems", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 0, "benchmarks": ["Persona<PERSON><PERSON><PERSON>"], "metrics": [], "task_id": "", "area": ""}, {"name": "Discourse Parsing", "children": [{"name": "Discourse Segmentation", "children": []}, {"name": "End-to-End RST Parsing", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Connective Detection", "children": []}], "description": "", "dataset_count": 10, "benchmarks": ["Instructional-DT (Instr-DT)", "RST-DT", "<PERSON><PERSON><PERSON><PERSON>", "STAC"], "metrics": [], "task_id": "", "area": ""}, {"name": "Document AI", "children": [{"name": "document understanding", "children": [{"name": "Line Items Extraction", "children": []}], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 1, "benchmarks": ["EPHOIE"], "metrics": [], "task_id": "", "area": ""}, {"name": "Document Text Classification", "children": [{"name": "Learning with noisy labels", "children": []}, {"name": "Multi-Label Classification Of Biomedical Texts", "children": []}, {"name": "Political Salient Issue Orientation Detection", "children": []}], "description": "", "dataset_count": 6, "benchmarks": ["Tobacco-3482", "CUB-200-2011", "Food-101", "Tobacco small-3482"], "metrics": [], "task_id": "", "area": ""}, {"name": "Emotion Recognition", "children": [{"name": "Speech Emotion Recognition", "children": []}, {"name": "Emotion Recognition in Conversation", "children": []}, {"name": "Multimodal Emotion Recognition", "children": []}, {"name": "Facial Emotion Recognition", "children": []}, {"name": "Emotion-Cause Pair Extraction", "children": []}, {"name": "EEG Emotion Recognition", "children": []}, {"name": "Emotion Cause Extraction", "children": []}, {"name": "Video Emotion Recognition", "children": []}, {"name": "Emotion Recognition in Context", "children": []}, {"name": "A-VB High", "children": []}]}, {"name": "Emotional Intelligence", "children": [{"name": "SNARKS", "children": []}, {"name": "Ruin <PERSON>", "children": []}, {"name": "Dark Humor Detection", "children": []}]}, {"name": "En<PERSON>ty Ty<PERSON>", "children": [{"name": "Entity Typing on DH-KGs", "children": []}]}, {"name": "Few-Shot Learning", "children": [{"name": "One-Shot Learning", "children": []}, {"name": "Few-Shot Semantic Segmentation", "children": []}, {"name": "Cross-Domain Few-Shot", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["CropDisease", "miniImagenet", "Plantae", "CUB", "ChestX", "Places", "EuroSAT", "ISIC2018", "cars"], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Few-Shot Learning", "children": []}, {"name": "Few-Shot Relation Classification", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Few-Shot Imitation Learning", "children": []}, {"name": "Few-Shot Audio Classification", "children": [], "description": "", "dataset_count": 9, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Few-Shot Camera-Adaptive Color Constancy", "children": []}, {"name": "Few-shot HTC", "children": []}, {"name": "Few-Shot Video Object Detection", "children": []}], "description": "", "dataset_count": 45, "benchmarks": ["Mini-ImageNet - 20-Shot Learning", "Mini-Imagenet 5-way (1-shot)", "MedNLI", "OxfordPets", "EuroSAT", "MR", "food101", "UCF101", "Mini-ImageNet - 1-Shot Learning", "CaseHOLD"], "metrics": [], "task_id": "", "area": ""}, {"name": "Grammatical Error Correction", "children": [{"name": "Grammatical Error Detection", "children": []}]}, {"name": "Handwriting Verification", "children": [{"name": "Bangla Spelling E<PERSON><PERSON> Correction", "children": []}]}, {"name": "Image Captioning", "children": [{"name": "3D dense captioning", "children": []}, {"name": "controllable image captioning", "children": []}, {"name": "Relational Captioning", "children": []}, {"name": "Semi Supervised Learning for Image Captioning", "children": []}, {"name": "Aesthetic Image Captioning", "children": []}, {"name": "Vietnamese Image Captioning", "children": []}, {"name": "Patent Figure Description Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hindi Image Captioning", "children": []}]}, {"name": "Image Generation", "children": [{"name": "Image-to-Image Translation", "children": []}, {"name": "Text-to-Image Generation", "children": [{"name": "text-guided-image-editing", "children": []}, {"name": "Text-based Image Editing", "children": []}, {"name": "Concept Alignment", "children": []}, {"name": "Zero-Shot Text-to-Image Generation", "children": []}, {"name": "Conditional Text-to-Image Synthesis", "children": []}, {"name": "Consistent Character Generation", "children": []}, {"name": "DreamBooth Personalized Generation", "children": []}]}, {"name": "Image Inpainting", "children": []}, {"name": "Image Manipulation", "children": []}, {"name": "Conditional Image Generation", "children": []}, {"name": "Face Generation", "children": []}, {"name": "3D Generation", "children": []}, {"name": "Pose Transfer", "children": []}, {"name": "Image Harmonization", "children": []}, {"name": "3D-Aware Image Synthesis", "children": []}]}, {"name": "Information Extraction", "children": [{"name": "Event Extraction", "children": [{"name": "NER", "children": [], "description": "", "dataset_count": 27, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Event Causality Identification", "children": []}, {"name": "Zero-shot Event Extraction", "children": []}]}, {"name": "Extractive Summarization", "children": []}, {"name": "Joint Entity and Relation Extraction", "children": []}, {"name": "Temporal Information Extraction", "children": [{"name": "Temporal Tagging", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Document-level Event Extraction", "children": []}, {"name": "Attribute Value Extraction", "children": []}, {"name": "Low Resource Named Entity Recognition", "children": []}, {"name": "Drug–drug Interaction Extraction", "children": []}, {"name": "Definition Extraction", "children": []}, {"name": "Event Relation Extraction", "children": []}]}, {"name": "Information Retrieval", "children": [{"name": "Passage Retrieval", "children": []}, {"name": "TAR", "children": []}, {"name": "Cross-Lingual Information Retrieval", "children": []}, {"name": "Table Search", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Scientific Results Extraction", "children": []}, {"name": "Zero Shot on BEIR (Inference Free Model)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Instruction Following", "children": [{"name": "visual instruction following", "children": []}]}, {"name": "Intent Detection", "children": [{"name": "Open Intent Detection", "children": []}]}, {"name": "Intrusion Detection", "children": [{"name": "Network Intrusion Detection", "children": []}]}, {"name": "Key Information Extraction", "children": [{"name": "Key-value Pair Extraction", "children": []}]}, {"name": "Knowledge Distillation", "children": [{"name": "Data-free Knowledge Distillation", "children": [{"name": "Benchmarking", "children": []}], "description": "", "dataset_count": 3, "benchmarks": ["SQuAD", "QNLI"], "metrics": [], "task_id": "", "area": ""}, {"name": "Self-Knowledge Distillation", "children": []}], "description": "", "dataset_count": 6, "benchmarks": ["en pt br", "en es", "CIFAR-100", "PASCAL VOC", "big content", "ImageNet", "COCO 2017 val", "COCO (Common Objects in Context)", "KITTI", "Cityscapes"], "metrics": [], "task_id": "", "area": ""}, {"name": "Knowledge Graph Completion", "children": [{"name": "Large Language Model", "children": [{"name": "Knowledge Graphs", "children": [], "description": "", "dataset_count": 46, "benchmarks": ["MARS (Multimodal Analogical Reasoning dataSet)", " FB15k", "JerichoWorld", "WikiKG90M-LSC"], "metrics": [], "task_id": "", "area": ""}, {"name": "RAG", "children": []}, {"name": "AI Agent", "children": []}], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Triple Classification", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["YAGO39K"], "metrics": [], "task_id": "", "area": ""}, {"name": "Inductive knowledge graph completion", "children": [{"name": "Large Language Model", "children": [{"name": "Knowledge Graphs", "children": [], "description": "", "dataset_count": 46, "benchmarks": ["MARS (Multimodal Analogical Reasoning dataSet)", " FB15k", "JerichoWorld", "WikiKG90M-LSC"], "metrics": [], "task_id": "", "area": ""}, {"name": "RAG", "children": []}, {"name": "AI Agent", "children": []}], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Inductive Relation Prediction", "children": []}], "description": "", "dataset_count": 16, "benchmarks": ["DBbook2014", "WN18RR", "DBP-5L (English)", "DBP-5L (Greek)", "DPB-5L (French)", "MovieLens 1M", "FB15k-237"], "metrics": [], "task_id": "", "area": ""}, {"name": "Language Acquisition", "children": [{"name": "Grounded language learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 7, "benchmarks": ["SLAM 2018"], "metrics": [], "task_id": "", "area": ""}, {"name": "Language Identification", "children": [{"name": "Dialect Identification", "children": []}, {"name": "Native Language Identification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 20, "benchmarks": ["Nordic Language Identification", "VoxForge", "OpenSubtitles", "GlotLID-C", "VOXLINGUA107", "Universal Dependencies"], "metrics": [], "task_id": "", "area": ""}, {"name": "Language Modeling", "children": [{"name": "Dream Generation", "children": []}], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Language Modelling", "children": [{"name": "XLM-R", "children": []}, {"name": "Long-range modeling", "children": []}, {"name": "Protein Language Model", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentence Pair Modeling", "children": [{"name": "Semantic Similarity", "children": [{"name": "Semantic Shift Detection", "children": []}, {"name": "Similarity Explanation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}]}], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Document Language Modeling", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Controllable Language Modelling", "children": []}], "description": "", "dataset_count": 166, "benchmarks": ["Text8 dev", "PhilPapers", "100 sleep nights of 8 caregivers", "FewCLUE (BUSTM)", "BIG-bench-lite", "CLUE (WSC1.1)", "FewCLUE (OCNLI-FC)", "OpenWebText", "CLUE (DRCD)", "OpenWebtext2"], "metrics": [], "task_id": "", "area": ""}, {"name": "Lexical Analysis", "children": [{"name": "Lexical Complexity Prediction", "children": []}]}, {"name": "Lexical Normalization", "children": [{"name": "Pronunciation Dictionary Creation", "children": []}]}, {"name": "Link Prediction", "children": [{"name": "Inductive Link Prediction", "children": []}, {"name": "Dynamic Link Prediction", "children": []}, {"name": "Hyperedge Prediction", "children": []}, {"name": "Calibration for Link Prediction", "children": []}, {"name": "Anchor link prediction", "children": []}, {"name": "Link prediction on DH-KGs", "children": []}]}, {"name": "Literature Mining", "children": [{"name": "Systematic Literature Review", "children": []}]}, {"name": "Machine Translation", "children": [{"name": "Transliteration", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multimodal Machine Translation", "children": [{"name": "Face to Face Translation", "children": []}, {"name": "Multimodal Lexical Translation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 5, "benchmarks": ["Hindi Visual Genome (Challenge Set)", "Multi30K", "Hindi Visual Genome (Test Set)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Bilingual Lexicon Induction", "children": []}, {"name": "Unsupervised Machine Translation", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Low-Resource Neural Machine Translation", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Automatic Post-Editing", "children": []}, {"name": "Low Resource Neural Machine Translation", "children": []}, {"name": "Low Resource NMT", "children": []}, {"name": "Clinical Language Translation", "children": []}, {"name": "Legal Document Translation", "children": []}], "description": "", "dataset_count": 83, "benchmarks": ["Business Scene Dialogue EN-JA", "IWSLT2015 English-Vietnamese", "IWSLT2015 German-English", "WMT 2018 English-Finnish", "IWSLT2017 French-English", "WMT 2022 English-Russian", "WMT 2022 Czech-English", "WMT2017 Finnish-English", "WMT 2017 Latvian-English", "WMT 2018 English-Estonian"], "metrics": [], "task_id": "", "area": ""}, {"name": "Mathematical Reasoning", "children": [{"name": "Math Word Problem Solving", "children": [], "description": "", "dataset_count": 23, "benchmarks": ["ALG514", "MATH minival", "MATH", "ParaMAWPS", "Math23K", "SVAMP (1:N)", "SVAMP", "DRAW-1K", "MAWPS", "PEN"], "metrics": [], "task_id": "", "area": ""}, {"name": "Formal Logic", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Geometry Problem Solving", "children": []}, {"name": "Abstract Algebra", "children": []}, {"name": "Mathematical Induction", "children": []}, {"name": "High School Mathematics", "children": []}, {"name": "Professional Accounting", "children": []}], "description": "", "dataset_count": 26, "benchmarks": ["Lila (IID)", "PGPS9K", "MATH500", "FrontierMath", "AMC23", "<PERSON> (OOD)", "GSM8K", "UniGeo", "GeoQA", "UniGeo (PRV)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Meme Classification", "children": [{"name": "Hateful Meme Classification", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 6, "benchmarks": ["Hateful Memes", "Tamil Memes", "MultiOFF"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multimodal Association", "children": [{"name": "multimodal generation", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Multimodal Deep Learning", "children": [{"name": "Multimodal Text and Image Classification", "children": [{"name": "image-sentence alignment", "children": []}, {"name": "Open-World Social Event Classification", "children": []}]}]}, {"name": "NLP based Person Retrival", "children": [{"name": "Decoder", "children": []}], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "NMT", "children": [{"name": "Direct NMT", "children": []}]}, {"name": "Named Entity Recognition (NER)", "children": [{"name": "NER", "children": [], "description": "", "dataset_count": 27, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Nested Named Entity Recognition", "children": []}, {"name": "Few-shot NER", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Chinese Named Entity Recognition", "children": []}, {"name": "Multilingual Named Entity Recognition", "children": []}, {"name": "Medical Named Entity Recognition", "children": []}, {"name": "Cross-Domain Named Entity Recognition", "children": []}, {"name": "Zero-shot Named Entity Recognition (NER)", "children": []}, {"name": "Multi-modal Named Entity Recognition", "children": []}, {"name": "Named Entity Recognition In Vietnamese", "children": []}]}, {"name": "Natural Language Inference", "children": [{"name": "Answer Generation", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Visual Entailment", "children": []}, {"name": "Cross-Lingual Natural Language Inference", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 81, "benchmarks": ["AX", "RTE", "MultiNLI", "ANLI test", "fever-nli", "MedNLI", "MNLI + SNLI + ANLI + FEVER", "QNLI", "GLUE", "MultiNLI Dev"], "metrics": [], "task_id": "", "area": ""}, {"name": "Natural Language Queries", "children": [{"name": "Text-to-CQL", "children": []}], "description": "", "dataset_count": 3, "benchmarks": ["Ego4D"], "metrics": [], "task_id": "", "area": ""}, {"name": "Natural Language Transduction", "children": [{"name": "Lipreading", "children": []}]}, {"name": "Natural Language Understanding", "children": [{"name": "Vietnamese Social Media Text Processing", "children": []}, {"name": "Emotional Dialogue Acts", "children": []}], "description": "", "dataset_count": 72, "benchmarks": ["DialoGLUE full", "LexGLUE", "GLUE", "STREUSLE", "PDP60", "DialoGLUE fewshot"], "metrics": [], "task_id": "", "area": ""}, {"name": "Negation Detection", "children": [{"name": "Negation Scope Resolution", "children": []}]}, {"name": "News Generation", "children": [{"name": "Headline Generation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Open Information Extraction", "children": [{"name": "Event Extraction", "children": [{"name": "NER", "children": [], "description": "", "dataset_count": 27, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Event Causality Identification", "children": []}, {"name": "Zero-shot Event Extraction", "children": []}]}]}, {"name": "Open-Domain Dialog", "children": [{"name": "Dialogue Evaluation", "children": [], "description": "", "dataset_count": 8, "benchmarks": ["USR-TopicalChat", "USR-PersonaChat"], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Optical Character Recognition (OCR)", "children": [{"name": "Active Learning", "children": [{"name": "Active Object Detection", "children": []}]}, {"name": "Handwritten Text Recognition", "children": []}, {"name": "Handwriting Recognition", "children": []}, {"name": "Handwritten Digit Recognition", "children": []}, {"name": "Irregular Text Recognition", "children": []}, {"name": "Handwritten Chinese Text Recognition", "children": []}, {"name": "Offline Handwritten Chinese Character Recognition", "children": []}, {"name": "Word Spotting In Handwritten Documents", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Handwritten Digit Image Synthesis", "children": []}, {"name": "Grapheme Detection", "children": []}]}, {"name": "Optical Charater Recogntion", "children": [{"name": "Bangla Text Detection", "children": []}]}, {"name": "Part-Of-Speech Tagging", "children": [{"name": "Unsupervised Part-Of-Speech Tagging", "children": []}], "description": "", "dataset_count": 26, "benchmarks": ["ANTILLES", "UD2.5 test", "Sequoia Treebank", "Spoken Corpus", "Penn Treebank", "ARK", "Morphosyntactic-analysis-dataset", "DaNE", "Tweebank", "ParTUT"], "metrics": [], "task_id": "", "area": ""}, {"name": "Pcl Detection", "children": [{"name": "SemEval-2022 Task 4-1 (Binary PCL Detection)", "children": []}, {"name": "SemEval-2022 Task 4-2 (Multi-label PCL Detection)", "children": []}]}, {"name": "Personality Generation", "children": [{"name": "Personality Alignment", "children": []}], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Phrase Grounding", "children": [{"name": "Grounded Open Vocabulary Acquisition", "children": []}]}, {"name": "Political evalutation", "children": [{"name": "Alignement visualisation", "children": []}]}, {"name": "Prompt Engineering", "children": [{"name": "Visual Prompting", "children": []}]}, {"name": "Propaganda detection", "children": [{"name": "Propaganda span identification", "children": []}, {"name": "Propaganda technique identification", "children": []}]}, {"name": "Question Answering", "children": [{"name": "Open-Domain Question Answering", "children": [], "description": "", "dataset_count": 26, "benchmarks": ["KILT: ELI5", "ELI5", "KILT: TriviaQA", "SQuAD1.1 dev", "Quasar", "Natural Questions (short)", "TQA", "Natural Questions", "SearchQA", "WebQuestions"], "metrics": [], "task_id": "", "area": ""}, {"name": "Open-Ended Question Answering", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-Shot Video Question Answer", "children": []}, {"name": "Knowledge Base Question Answering", "children": [], "description": "", "dataset_count": 15, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Conversational Question Answering", "children": [{"name": "Question Rewriting", "children": []}], "description": "", "dataset_count": 10, "benchmarks": ["ConvFinQA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Answer Selection", "children": []}, {"name": "Community Question Answering", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multiple Choice Question Answering (MCQA)", "children": [], "description": "", "dataset_count": 9, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Long Form Question Answering", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Science Question Answering", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 416, "benchmarks": ["NExT-QA (Open-ended VideoQA)", "DROP Test", "MuLD (NarrativeQA)", "MedQA", "SIQA", "MRQA", "CODAH", "<PERSON><PERSON><PERSON><PERSON>", "OpenBookQA", "SchizzoSQUAD"], "metrics": [], "task_id": "", "area": ""}, {"name": "Question Generation", "children": [{"name": "Poll Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 25, "benchmarks": ["GrailQA-IID", "COCO Visual Question Answering (VQA) real images 1.0 open ended", "GrailQA-Compositional", "SQuAD", "GrailQA-Zero-Shot", "Natural Questions", "FairytaleQA", "Visual Question Generation", "WeiboPolls", "SQuAD1.1"], "metrics": [], "task_id": "", "area": ""}, {"name": "Question Similarity", "children": [{"name": "Medical question pair similarity computation", "children": []}], "description": "", "dataset_count": 1, "benchmarks": ["Q2Q Arabic Benchmark"], "metrics": [], "task_id": "", "area": ""}, {"name": "Reading Comprehension", "children": [{"name": "Machine Reading Comprehension", "children": [], "description": "", "dataset_count": 43, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Intent Recognition", "children": []}, {"name": "Implicit Relations", "children": []}, {"name": "Question Selection", "children": []}, {"name": "LAMBADA", "children": []}, {"name": "Multi-Hop Reading Comprehension", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Implicatures", "children": []}, {"name": "Belebele", "children": []}, {"name": "Logical Reasoning Reading Comprehension", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Formal Fallacies Syllogisms Negation", "children": []}], "description": "", "dataset_count": 95, "benchmarks": ["ReCAM", "RACE", "MuSeRC", "RadQA", "AdversarialQA", "ReClor", "CrowdSource QA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Recognizing Emotion Cause in Conversations", "children": [{"name": "Causal Emotion Entailment", "children": []}]}, {"name": "Reinforcement Learning", "children": [{"name": "Deep Reinforcement Learning", "children": []}]}, {"name": "Relation Extraction", "children": [{"name": "Relation Classification", "children": [{"name": "Few-Shot Relation Classification", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Implicit Discourse Relation Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cause-Effect Relation Classification", "children": []}], "description": "", "dataset_count": 23, "benchmarks": ["SemEval 2010 Task 8", "Discovery", "DRI Corpus", "TACRED", "CDCP", "MATRES", "AbstRCT - Neoplasm", "FewRel"], "metrics": [], "task_id": "", "area": ""}, {"name": "Document-level Relation Extraction", "children": [{"name": "Document-level RE with incomplete labeling", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Joint Entity and Relation Extraction", "children": []}, {"name": "Temporal Relation Extraction", "children": [{"name": "Temporal Relation Classification", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Dialog Relation Extraction", "children": []}, {"name": "Relationship Extraction (Distant Supervised)", "children": []}, {"name": "Continual Relation Extraction", "children": []}, {"name": "Binary Relation Extraction", "children": []}, {"name": "Zero-shot Relation Triplet Extraction", "children": []}, {"name": "4-ary Relation Extraction", "children": []}]}, {"name": "Representation Learning", "children": [{"name": "Disentanglement", "children": []}, {"name": "Graph Representation Learning", "children": []}, {"name": "Sentence Embeddings", "children": [{"name": "Sentence Embedding", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentence Compression", "children": [{"name": "Unsupervised Sentence Compression", "children": []}]}, {"name": "Joint Multilingual Sentence Representations", "children": [{"name": "Abstract Meaning Representation", "children": []}], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentence Embeddings For Biomedical Texts", "children": []}], "description": "", "dataset_count": 11, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Network Embedding", "children": []}, {"name": "Knowledge Graph Embeddings", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Document Embedding", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Learning Word Embeddings", "children": []}, {"name": "Feature Upsampling", "children": []}, {"name": "Learning Semantic Representations", "children": []}, {"name": "Multilingual Word Embeddings", "children": []}]}, {"name": "Retrieval", "children": [{"name": "Text Retrieval", "children": []}, {"name": "Deep Hashing", "children": []}, {"name": "Table Retrieval", "children": []}]}, {"name": "Semantic Parsing", "children": [{"name": "Text-To-SQL", "children": [{"name": "MMSQL performance", "children": []}], "description": "", "dataset_count": 21, "benchmarks": ["Text-To-SQL", "spider", "SQL-Eval", "KaggleDBQA", "SParC", "2D KITTI Cars Easy", "Spider 2.0", "SEDE", "SPIDER", "BIRD (BIg Bench for LaRge-scale Database Grounded Text-to-SQL Evaluation)"], "metrics": [], "task_id": "", "area": ""}, {"name": "AMR Parsing", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["LDC2020T02", "LDC2017T10", "LDC2014T12", "LDC2015E86", "The Little Prince", "New3", "LDC2014T12:", "Bio"], "metrics": [], "task_id": "", "area": ""}, {"name": "Semantic Dependency Parsing", "children": []}, {"name": "DRS Parsing", "children": []}, {"name": "UCCA Parsing", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "text-to-Cypher", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised semantic parsing", "children": []}]}, {"name": "Semantic Role Labeling", "children": [{"name": "Predicate Detection", "children": []}, {"name": "Semantic Role Labeling (predicted predicates)", "children": []}, {"name": "Textual Analogy Parsing", "children": []}]}, {"name": "Semantic Textual Similarity", "children": [{"name": "Paraphrase Identification", "children": [], "description": "", "dataset_count": 18, "benchmarks": ["Quora Question Pairs", "Translated SNLI Dataset in Marathi", "MSRP", "WikiHop", "TURL", "2017_test set", "PIT", "Quora Question Pairs Dev", "IMDb", "AP"], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual Semantic Textual Similarity", "children": []}]}, {"name": "Sentence Completion", "children": [{"name": "Hurtful Sentence Completion", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 2, "benchmarks": ["HellaSwag"], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentiment Analysis", "children": [{"name": "Aspect-Based Sentiment Analysis (ABSA)", "children": [{"name": "Aspect Extraction", "children": [{"name": "Hidden Aspect Detection", "children": []}, {"name": "Latent Aspect Detection", "children": []}]}, {"name": "Aspect Category Sentiment Analysis", "children": []}, {"name": "Extract Aspect", "children": []}, {"name": "Aspect-oriented  Opinion Extraction", "children": []}, {"name": "Aspect-Category-Opinion-Sentiment Quadruple Extraction", "children": [{"name": "Conversational Sentiment Quadruple Extraction", "children": []}]}, {"name": "Extract aspect-polarity tuple", "children": []}, {"name": "Aspect-Sentiment-Opinion Triplet Extraction", "children": []}, {"name": "Aspect Category Sentiment Classification", "children": []}]}, {"name": "Multimodal Sentiment Analysis", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Aspect Sentiment Triplet Extraction", "children": []}, {"name": "Twitter Sentiment Analysis", "children": [{"name": "Tweet-Reply Sentiment Analysis", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Aspect Term Extraction and Sentiment Classification", "children": []}, {"name": "Arabic Sentiment Analysis", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["!(()&&!|*|*|"], "metrics": [], "task_id": "", "area": ""}, {"name": "Persian Sentiment Analysis", "children": [{"name": "Transition-Based Dependency Parsing", "children": []}], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "target-oriented opinion words extraction", "children": []}, {"name": "Fine-Grained Opinion Analysis", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vietnamese Aspect-Based Sentiment Analysis", "children": [{"name": "Sentiment Dependency Learning", "children": []}]}], "description": "", "dataset_count": 105, "benchmarks": ["ChnSentiCorp", "IMDb Movie Reviews", "FiQA", "SLUE", "IITP Movie Reviews Sentiment", "122 People - Passenger Behavior Recognition Data", "TweetEval", "RuSentiment", "BanglaBook", "Amazon Polarity"], "metrics": [], "task_id": "", "area": ""}, {"name": "Shallow Syntax", "children": [{"name": "Chunking", "children": []}], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Slot Filling", "children": [{"name": "Zero-shot Slot Filling", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Extracting COVID-19 Events from Twitter", "children": []}], "description": "", "dataset_count": 28, "benchmarks": ["Dialogue State Tracking Challenge", "CAIS", "KILT: Zero Shot RE", "MASSIVE", "MULTIWOZ 2.2", "KILT: T-REx", "MixATIS", "MixSNIPS", "ATIS", "ATIS (vi)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Source Code Summarization", "children": [{"name": "Method name prediction", "children": []}], "description": "", "dataset_count": 7, "benchmarks": ["Java scripts", "CodeSearchNet", "Summarizing Source Code using a Neural Attention Model - C#", "DeepCom-Java", "CodeSearchNet - Python", "ParallelCorpus-Python", "Summarizing Source Code using a Neural Attention Model - SQL", "Summarizing Source Code using a Neural Attention Model - Python", "CoDesc"], "metrics": [], "task_id": "", "area": ""}, {"name": "Spam detection", "children": [{"name": "Traditional Spam Detection", "children": []}, {"name": "Context-specific Spam Detection", "children": []}]}, {"name": "Speculation Detection", "children": [{"name": "Speculation Scope Resolution", "children": []}]}, {"name": "Speech-to-Text Translation", "children": [{"name": "Simultaneous Speech-to-Text Translation", "children": []}], "description": "", "dataset_count": 4, "benchmarks": ["MuST-C", "FLEURS eng-X", "MediBeng", "CoVoST 2 eng-X", "MuST-C EN->FR", "libri-trans", "CoVoST 2 X-eng", "MuST-C EN->ES", "MuST-C EN->DE", "MuST-C EN->NL"], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON> Detection", "children": [{"name": "Zero-Shot <PERSON> Detection", "children": []}, {"name": "Few-Shot <PERSON><PERSON> Detection", "children": []}, {"name": "<PERSON><PERSON> (US Election 2020 - Biden)", "children": []}, {"name": "<PERSON><PERSON> (US Election 2020 - Trump)", "children": []}]}, {"name": "Stock Prediction", "children": [{"name": "Text-Based Stock Prediction", "children": []}, {"name": "PAIR TRADING", "children": []}, {"name": "Event-Driven Trading", "children": []}]}, {"name": "Summarization", "children": [{"name": "Unsupervised Extractive Summarization", "children": []}, {"name": "Query-focused Summarization", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 8, "benchmarks": ["ccdv/arxiv-summarization", "scientific_papers", "XSum", "SAMSum", "<PERSON><PERSON><PERSON>", "big_patent", "launch/gov_report", "bazzhangz/sumdataset", "kmfoda/booksum", "SAMSum Corpus: A Human-annotated Dialogue Dataset for Abstractive Summarization"], "metrics": [], "task_id": "", "area": ""}, {"name": "Symbolic Regression", "children": [{"name": "Equation Discovery", "children": []}]}, {"name": "Taxonomy Learning", "children": [{"name": "Taxonomy Expansion", "children": []}, {"name": "Hypernym Discovery", "children": []}]}, {"name": "Temporal Processing", "children": [{"name": "Temporal Information Extraction", "children": [{"name": "Temporal Tagging", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Timex normalization", "children": []}, {"name": "Document Dating", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Term Extraction", "children": [{"name": "Nested Term Extraction", "children": []}, {"name": "Nested Term Recognition", "children": [{"name": "Nested Term Recognition from Flat Supervision", "children": []}]}]}, {"name": "Text Clustering", "children": [{"name": "Short Text Clustering", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Open Intent Discovery", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["BANKING77", "DBpedia", "Stackoverflow", "ATIS", "CLINC150", "SNIPS"], "metrics": [], "task_id": "", "area": ""}, {"name": "Hierarchical Text Clustering", "children": []}], "description": "", "dataset_count": 5, "benchmarks": ["Urdu News Headlines Dataset", "20 Newsgroups", "MTEB"], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Generation", "children": [{"name": "Dialogue Generation", "children": [{"name": "Multi-modal Dialogue Generation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 34, "benchmarks": ["Ubuntu Dialogue (Entity)", "Twitter Dialogue (<PERSON>un)", "FusedChat", "Persona<PERSON><PERSON><PERSON>", "CMU-DoG", "Ubuntu Dialogue (Cmd)", "PG-19", "Ubuntu Dialogue (Tense)", "Reddit (multi-ref)", "Amazon-5"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Document Summarization", "children": [], "description": "", "dataset_count": 15, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Data-to-Text Generation", "children": [{"name": "Visual Storytelling", "children": [{"name": "Image-guided Story Ending Generation", "children": []}]}, {"name": "KG-to-Text Generation", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["AGENDA", "WikiGraphs", "WebNLG (All)", "WebNLG (Unseen)", "EventNarrative", "WebNLG 2.0 (Unconstrained)", "WebQuestions", "WebNLG (Seen)", "PathQuestion", "ENT-DESC"], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised KG-to-Text Generation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 24, "benchmarks": ["WebNLG ru", "SR11Deep", "Wikipedia Person and Animal Dataset", "XAlign", "Czech Restaurant NLG", "MLB Dataset (Content Ordering)", "MLB Dataset", "E2E", "WikiOFGraph", "MLB Dataset (Content Selection)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Story Generation", "children": [{"name": "Visual Storytelling", "children": [{"name": "Image-guided Story Ending Generation", "children": []}]}], "description": "", "dataset_count": 8, "benchmarks": ["WritingPrompts", "TVMegaSite test", "Fandom test", "Fandom dev", "TVMegaSite dev"], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Style Transfer", "children": [{"name": "Formality Style Transfer", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Word Attribute Transfer", "children": []}, {"name": "Semi-Supervised Formality Style Transfer", "children": []}], "description": "", "dataset_count": 7, "benchmarks": ["Yelp Review Dataset (Large)", "Yelp Review Dataset (Small)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Paraphrase Generation", "children": [{"name": "Multilingual Paraphrase Generation", "children": []}], "description": "", "dataset_count": 17, "benchmarks": ["Quora Question Pairs", "Paralex", "MSCOCO"], "metrics": [], "task_id": "", "area": ""}, {"name": "Spelling Correction", "children": [{"name": "Bangla Spelling E<PERSON><PERSON> Correction", "children": []}]}, {"name": "Table-to-Text Generation", "children": [{"name": "KB-to-Language Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 6, "benchmarks": ["RotoWire", "Wikipedia Person and Animal Dataset", "WebNLG (All)", "E2E", "WikiBio", "WebNLG (Unseen)", "WebNLG (Seen)", "DART"], "metrics": [], "task_id": "", "area": ""}, {"name": "Headline Generation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Conditional Text Generation", "children": [{"name": "Multimedia Generative Script Learning", "children": []}, {"name": "Contextualized Literature-based Discovery", "children": []}], "description": "", "dataset_count": 4, "benchmarks": ["Lipogram-e"], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 162, "benchmarks": ["CNN/Daily Mail", "LDC2016E25", "CrimeStats", "ReDial", "Winogrande TR", "Unruly", "OpenBookQA", "ARC Challenge (25-Shot)", "Open-Minded<PERSON> (0-shot)", "OpenWebText"], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Summarization", "children": [{"name": "Abstractive Text Summarization", "children": [{"name": "Timeline Summarization", "children": []}, {"name": "Multimodal Abstractive Text Summarization", "children": []}, {"name": "Reader-Aware Summarization", "children": []}]}, {"name": "Document Summarization", "children": [{"name": "Email Thread Summarization", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 28, "benchmarks": ["BBC XSum", "CNN / Daily Mail", "WikiLingua (tr->en)", "Arxiv HEP-TH citation graph", "arXiv Summarization Dataset", "HowSumm-Method", "HowSumm-Step"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Document Summarization", "children": [], "description": "", "dataset_count": 15, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Opinion Summarization", "children": []}, {"name": "Extractive Text Summarization", "children": [{"name": "Reader-Aware Summarization", "children": []}]}, {"name": "Sentence Compression", "children": [{"name": "Unsupervised Sentence Compression", "children": []}]}, {"name": "Sentence Summarization", "children": [{"name": "Unsupervised Sentence Summarization", "children": []}], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Scientific Document Summarization", "children": [{"name": "Lay Summarization", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 7, "benchmarks": ["CL-SciSumm"], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Opinion Summarization", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Long-Form Narrative Summarization", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 98, "benchmarks": ["BBC XSum", "WikiHow", "GigaWord", "MeQSum", "DUC 2004 Task 1", "OrangeSum", "GigaWord-10k", "CL-SciSumm", "LCSTS", "ACI-Bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Text to Image Generation", "children": [{"name": "Text to 3D", "children": []}]}, {"name": "Text-To-Speech Synthesis", "children": [{"name": "Prosody Prediction", "children": []}, {"name": "Zero-Shot Multi-Speaker TTS", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}]}, {"name": "Text-to-Video Generation", "children": [{"name": "Text-to-Video Editing", "children": []}, {"name": "Subject-driven Video Generation", "children": []}]}, {"name": "Text2text Generation", "children": [{"name": "Keyphrase Generation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sketch-to-text Generation", "children": []}, {"name": "Figurative Language Visualization", "children": []}], "description": "", "dataset_count": 4, "benchmarks": ["lmqg/qg_squad", "lmqg/qg_jaquad", "MTTN: Multi-Pair Text to Text Narratives for Prompt Generation"], "metrics": [], "task_id": "", "area": ""}, {"name": "Token Classification", "children": [{"name": "Toxic Spans Detection", "children": []}, {"name": "Blackout Poetry Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "description": "", "dataset_count": 15, "benchmarks": ["conll2002", "peoples_daily_ner", "x_glue", "mim_gold_ner", "grit-id/id_nergrit_corpus ner", "nielsr/funsd-layoutlmv3", "wikiann sk", "ingredients_yes_no", "MNIST", "SROIE"], "metrics": [], "task_id": "", "area": ""}, {"name": "Video Generation", "children": [{"name": "Image to Video Generation", "children": []}, {"name": "Unconditional Video Generation", "children": []}]}, {"name": "Vietnamese Sentiment Analysis", "children": [{"name": "Vietnamese Multimodal Sentiment Analysis", "children": []}], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Visual Question Answering (VQA)", "children": [{"name": "Visual Question Answering", "children": [{"name": "Spatial Reasoning", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Object Hallucination", "children": []}, {"name": "Explanatory Visual Question Answering", "children": []}, {"name": "Vietnamese Visual Question Answering", "children": []}, {"name": "MM-Vet v2", "children": []}]}, {"name": "Machine Reading Comprehension", "children": [], "description": "", "dataset_count": 43, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Chart Understanding", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Chart Question Answering", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["ChartQA", "RealCQA", "PlotQA"], "metrics": [], "task_id": "", "area": ""}, {"name": "3D Question Answering (3D-QA)", "children": []}, {"name": "Embodied Question Answering", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Generative Visual Question Answering", "children": []}, {"name": "Factual Visual Question Answering", "children": []}, {"name": "Multimodal Colonoscopy", "children": []}]}, {"name": "Weakly Supervised Classification", "children": [{"name": "Weakly Supervised Data Denoising", "children": []}], "description": "", "dataset_count": 4, "benchmarks": ["ShARe/CLEF 2014: Task 2 Disorders", "THYME-2016"], "metrics": [], "task_id": "", "area": ""}, {"name": "Word Embeddings", "children": [{"name": "Learning Word Embeddings", "children": []}, {"name": "Multilingual Word Embeddings", "children": []}, {"name": "Embeddings Evaluation", "children": []}, {"name": "Contextualised Word Representations", "children": []}], "description": "", "dataset_count": 53, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Word Sense Disambiguation", "children": [{"name": "Word Sense Induction", "children": []}], "description": "", "dataset_count": 16, "benchmarks": ["BIG-bench (Anachronisms)", "WiC-TSV", "SensEval 2 Lexical Sample", "FEWS", "SemEval 2007 Task 7", "SemEval 2015 Task 13", "Words in Context", "TS50", "RUSSE", "SensEval 3 Task 1"], "metrics": [], "task_id": "", "area": ""}, {"name": "trustable and focussed LLM generated content", "children": [{"name": "Game Design", "children": []}], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "standalone_tasks": [{"name": "Domain Adaptation", "children": [], "description": "", "dataset_count": 96, "benchmarks": ["HMDBfull-to-UCF", "LeukemiaAttri", "GTA-to-FoggyCityscapes", "Sim10k", "Noisy-SYND-to-MNIST", "GTAV+Synscapes to Cityscapes", "<PERSON><PERSON><PERSON>", "GTAV to Cityscapes+Mapillary", "UCF-to-Olympic", "SYNTHIA-to-FoggyCityscapes"], "metrics": [], "task_id": "", "area": ""}, {"name": "Node Classification", "children": [], "description": "", "dataset_count": 76, "benchmarks": ["MuMiN-medium", "pokec", "Coauthor CS", "DBLP: 20 nodes per class", "London", "Telegram (Directed Graph label rate 60%)", "Wiki-Vote", "Amazon Photo", "Wisconsin", "ogbn-products: 20 nodes per class"], "metrics": [], "task_id": "", "area": ""}, {"name": "Recommendation Systems", "children": [], "description": "", "dataset_count": 56, "benchmarks": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Amazon Games", "MovieLens 10M", "Yelp2018", "Amazon-Movies", "Amazon Books", "ReDial", "Flixster", "Amazon Men"], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-Shot Learning", "children": [], "description": "", "dataset_count": 43, "benchmarks": ["How2QA", "TVQA", "COCO-MLT", "AwA2", "CUB-200-2011", "VOC-MLT", "EuroSAT", "ImageNet_CN", "UCF101", "Oxford-IIIT Pets"], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Domain Adaptation", "children": [], "description": "", "dataset_count": 34, "benchmarks": ["Duke to Market", "PreSIL to KITTI", "ImageNet-R", "GTAV-to-Cityscapes Labels", "UCF-HMDB", "VehicleID to VERI-Wild Medium", "EPIC-KITCHENS-100", "Veri-776 to VehicleID Medium", "PACS", "Duke to MSMT"], "metrics": [], "task_id": "", "area": ""}, {"name": "Domain Generalization", "children": [], "description": "", "dataset_count": 31, "benchmarks": ["ImageNet-R", "Stylized-ImageNet", "NICO Vehicle", "PACS", "ImageNet-Sketch", "ImageNet-A", "NICO Animal", "GTA-to-Avg(Cityscapes,BDD,Mapillary)", "VLCS", "VizWiz-Classification"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Label Classification", "children": [], "description": "", "dataset_count": 30, "benchmarks": ["ChestX-ray14", "MRNet", "MIMIC-CXR", "PASCAL VOC 2007", "MLRSNet", "MS-COCO", "PASCAL VOC 2012", "CheXpert", "OpenImages-v6", "NUS-WIDE"], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Simplification", "children": [], "description": "", "dataset_count": 20, "benchmarks": ["DEplain-web-doc", "<PERSON><PERSON><PERSON><PERSON>", "Wiki-Auto + Turk", "WikiLargeFR", "TurkCorpus", "ASSET", "PWKP / WikiSmall", "DEplain-web-sent", "DEplain-APA-doc", "DEplain-APA-sent"], "metrics": [], "task_id": "", "area": ""}, {"name": "Time Series Classification", "children": [], "description": "", "dataset_count": 19, "benchmarks": ["DigitShapes", "ACSF1", "Heartbeat", "AATLD Gesture Recognition", "LP5", "ERing", "ArabicDigits", "UEA", "EigenWorms", "KickvsPunch"], "metrics": [], "task_id": "", "area": ""}, {"name": "Transfer Learning", "children": [], "description": "", "dataset_count": 18, "benchmarks": ["COCO70", "100 sleep nights of 8 caregivers", "KITTI Object Tracking Evaluation 2012", "Office-Home", "BanglaLekha Isolated Dataset", "Retinal Fundus MultiDisease Image Dataset (RFMiD)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Logical Reasoning", "children": [], "description": "", "dataset_count": 18, "benchmarks": ["BIG-bench (Formal Fallacies Syllogisms Negation)", "Winograd Automatic", "BIG-bench (Reasoning About Colored Objects)", "BIG-bench (Temporal Sequences)", "BIG-bench (Penguins In A Table)", "RuWorldTree", "BIG-bench (Logic Grid Puzzle)", "LingOly", "BIG-bench (Logical Fallacy Detection)", "BIG-bench (StrategyQA)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Sign Language Translation", "children": [], "description": "", "dataset_count": 17, "benchmarks": ["RWTH-PHOENIX-Weather 2014 T", "CSL-Daily", "How2Sign", "ASLG-PC12", "LSA-T", "Mediapi-RGB"], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation", "children": [], "description": "", "dataset_count": 17, "benchmarks": ["MuLD (OpenSubtitles)", "small content", "Oxford Radar RobotCar Dataset", "PhoMT"], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-shot Text Search", "children": [], "description": "", "dataset_count": 16, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentiment Classification", "children": [], "description": "", "dataset_count": 15, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Node Classification on Non-Homophilic (Heterophilic) Graphs", "children": [], "description": "", "dataset_count": 15, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hierarchical Multi-label Classification", "children": [], "description": "", "dataset_count": 15, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Code Search", "children": [], "description": "", "dataset_count": 14, "benchmarks": ["", "CodeSearchNet", "CodeXGLUE - WebQueryTest", "CodeSearchNet - Ruby", "CoIR", "CodeXGLUE - AdvTest", "CoDesc"], "metrics": [], "task_id": "", "area": ""}, {"name": "Intent Classification", "children": [], "description": "", "dataset_count": 14, "benchmarks": ["KUAKE-QIC", "ORCAS-I", "SLURP", "MASSIVE"], "metrics": [], "task_id": "", "area": ""}, {"name": "Document Layout Analysis", "children": [], "description": "", "dataset_count": 13, "benchmarks": ["Document Layout Recognition Challenge mini-dev", "RVL-CDIP", "D4LA", "Document Layout Recognition Challenge test", "PubLayNet val", "U-DIADS-Bib"], "metrics": [], "task_id": "", "area": ""}, {"name": "Relational Reasoning", "children": [], "description": "", "dataset_count": 12, "benchmarks": ["CLUTRR (k=3)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Code Completion", "children": [], "description": "", "dataset_count": 12, "benchmarks": ["Defects4J", "CodeXGLUE - Github Java Corpus", "Rambo Benchmark", "SAFIM", "CodeXGLUE - PY150", "DotPrompts"], "metrics": [], "task_id": "", "area": ""}, {"name": "Generalizable Person Re-identification", "children": [], "description": "", "dataset_count": 11, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sequential Recommendation", "children": [], "description": "", "dataset_count": 11, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Audio Generation", "children": [], "description": "", "dataset_count": 11, "benchmarks": ["Symphony music", "Classical music, 5 seconds at 12 kHz", "AudioCaps"], "metrics": [], "task_id": "", "area": ""}, {"name": "Synthetic Data Generation", "children": [], "description": "", "dataset_count": 11, "benchmarks": ["UNSW-NB15", "UCI Epileptic Seizure Recognition"], "metrics": [], "task_id": "", "area": ""}, {"name": "Mathematical Question Answering", "children": [], "description": "", "dataset_count": 11, "benchmarks": ["Geometry3K", "GeoS"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multimodal Reasoning", "children": [], "description": "", "dataset_count": 11, "benchmarks": ["AlgoPuzzleVQA", "MATH-V", "REBUS"], "metrics": [], "task_id": "", "area": ""}, {"name": "Generalized Zero-Shot Learning", "children": [], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Robust classification", "children": [], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Topic Classification", "children": [], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Audio Tagging", "children": [], "description": "", "dataset_count": 10, "benchmarks": ["AudioSet"], "metrics": [], "task_id": "", "area": ""}, {"name": "Citation Recommendation", "children": [], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Music Classification", "children": [], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Handwriting generation", "children": [], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text-to-Code Generation", "children": [], "description": "", "dataset_count": 10, "benchmarks": ["CodeXGLUE - CONCODE"], "metrics": [], "task_id": "", "area": ""}, {"name": "Explanation Generation", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["VQA-X", "e-SNLI-VE", "WHOOPS!", "CLEVR-X", "VCR"], "metrics": [], "task_id": "", "area": ""}, {"name": "Person Search", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["CUHK-SYSU", "PRW"], "metrics": [], "task_id": "", "area": ""}, {"name": "Anomaly Classification", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["VisA-AC", "MVTec-AC", "GoodsAD", "VisA", "MVTecAD"], "metrics": [], "task_id": "", "area": ""}, {"name": "Abusive Language", "children": [], "description": "", "dataset_count": 9, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Arithmetic Reasoning", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["MathMC", "MultiArith", "Game of 24", "GSM8K", "MathToF"], "metrics": [], "task_id": "", "area": ""}, {"name": "General Classification", "children": [], "description": "", "dataset_count": 8, "benchmarks": ["iris", "XOR", "Fashion-MNIST", "<PERSON><PERSON>", "CVR", "<PERSON><PERSON><PERSON><PERSON>", "ISOLET", "CVE to CWE mapping", "MNIST", "Wine"], "metrics": [], "task_id": "", "area": ""}, {"name": "Few-Shot Learning - 4 shots", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Formation Energy", "children": [], "description": "", "dataset_count": 8, "benchmarks": ["OQMD v1.2", "Salicylic Acid", "GeTe", "QM9", "Materials Project", "LiPS20", "JARVIS-DFT", "Ethanol", "OQM9HK", "<PERSON><PERSON><PERSON>"], "metrics": [], "task_id": "", "area": ""}, {"name": "Gloss-free Sign Language Translation", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ECG Classification", "children": [], "description": "", "dataset_count": 8, "benchmarks": ["UCR Time Series Classification Archive", "PhysioNet Challenge 2021", "PhysioNet Challenge 2020", "PTB-XL", "Electrocardiography (ECG) on Telehealth Network of Minas Gerais (TNMG)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Chinese Reading Comprehension", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Heterogeneous Node Classification", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation deu-eng", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eng-deu", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Keyword Spotting", "children": [], "description": "", "dataset_count": 8, "benchmarks": ["Google Speech Commands (v2)", "FKD", "VoxForge", "QUESST", "TAU Urban Acoustic Scenes 2019", "TensorFlow", "Google Speech Commands V2 12", "Google Speech Commands", "hey <PERSON><PERSON>", "Google Speech Commands V2 35"], "metrics": [], "task_id": "", "area": ""}, {"name": "Causal Inference", "children": [], "description": "", "dataset_count": 8, "benchmarks": ["Jobs", "IDHP", "IHDP"], "metrics": [], "task_id": "", "area": ""}, {"name": "Stance Classification", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Code Classification", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Code Summarization", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multilingual NLP", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Response Generation", "children": [], "description": "", "dataset_count": 8, "benchmarks": ["MMConv", "ArgSciChat", "SIMMC2.0"], "metrics": [], "task_id": "", "area": ""}, {"name": "imbalanced classification", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Bayesian Inference", "children": [], "description": "", "dataset_count": 7, "benchmarks": ["cifar100"], "metrics": [], "task_id": "", "area": ""}, {"name": "Sequence-to-sequence Language Modeling", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "News Recommendation", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Tabular Data Generation", "children": [], "description": "", "dataset_count": 7, "benchmarks": ["Diabetes", "SICK", "Travel", "California Housing Prices", "HELOC", "Adult Census Income"], "metrics": [], "task_id": "", "area": ""}, {"name": "Malware Classification", "children": [], "description": "", "dataset_count": 7, "benchmarks": ["Microsoft Malware Classification Challenge", "Malimg Dataset", "Male<PERSON><PERSON>"], "metrics": [], "task_id": "", "area": ""}, {"name": "Extreme Summarization", "children": [], "description": "", "dataset_count": 7, "benchmarks": ["GEM-XSum", "XSum", "CiteSum", "TLDR9+"], "metrics": [], "task_id": "", "area": ""}, {"name": "Systematic Generalization", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "General Reinforcement Learning", "children": [], "description": "", "dataset_count": 7, "benchmarks": ["Obstacle Tower (Strong Gen) varied", "Obstacle Tower (No Gen) varied", "Obstacle Tower (No Gen) fixed", "Obstacle Tower (Weak Gen) varied", "Obstacle Tower (Weak Gen) fixed", "Obstacle Tower (Strong Gen) fixed"], "metrics": [], "task_id": "", "area": ""}, {"name": "News Summarization", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Masked Language Modeling", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Concept-based Classification", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["CelebA", "AwA2", "CUB-200-2011", "CheXpert", "aPY"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Modal Document Classification", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Generative Question Answering", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Environmental Sound Classification", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["ESC-50", "UrbanSound8K", "FSD50K"], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Spotting", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["Inverse-Text", "SCUT-CTW1500", "Total-Text", "ICDAR 2015"], "metrics": [], "task_id": "", "area": ""}, {"name": "Source-Free Domain Adaptation", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["Cityscapes to Dark Zurich", "SYNTHIA-to-Cityscapes", "Cityscapes to ACDC", "VisDA-2017", "PACS", "GTA5 to Cityscapes", "\tVIPER-to-Cityscapes"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Source Unsupervised Domain Adaptation", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Compositional Zero-Shot Learning", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Lexical Entailment", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multimodal Recommendation", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Genre classification", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Meeting Summarization", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["ICSI Meeting Corpus", "AMI Meeting Corpus"], "metrics": [], "task_id": "", "area": ""}, {"name": "Text-to-Music Generation", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["MusicCaps", "MusicBench"], "metrics": [], "task_id": "", "area": ""}, {"name": "text annotation", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Generalized Few-Shot Learning", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["SUN", "CUB", "AwA2"], "metrics": [], "task_id": "", "area": ""}, {"name": "Reading Comprehension (Zero-Shot)", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Reading Comprehension (One-Shot)", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Reading Comprehension (Few-Shot)", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Synthetic-to-Real Translation", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Variational Inference", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Universal Domain Adaptation", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["Office-Home", "Office-31", "DomainNet", "VisDA2017"], "metrics": [], "task_id": "", "area": ""}, {"name": "Paper generation", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "AMR-to-Text Generation", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eng-fra", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Morphological Analysis", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-shot Classification (unified classes)", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual Question Answering", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multilabel Text Classification", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Material Classification", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Generation", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["Toulouse Road Network"], "metrics": [], "task_id": "", "area": ""}, {"name": "Texture Classification", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Crop Classification", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["CropHarvest - Brazil", "CropHarvest multicrop - Global", "CropHarvest - Global", "CropHarvest - Kenya", "CropHarvest - Togo"], "metrics": [], "task_id": "", "area": ""}, {"name": "Knowledge Probing", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-task Language Understanding", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Speech-to-Speech Translation", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["TAT", "CVSS", "FLEURS X-eng"], "metrics": [], "task_id": "", "area": ""}, {"name": "Gender Classification", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Long-Context Understanding", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["Ada-LEval (TSort)", "Ada-LEval (BestAnswer)", "<PERSON><PERSON><PERSON><PERSON>", "L-Eval", "MMNeedle"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi Label Text Classification", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Human Parsing", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Predicate Classification", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-shot Audio Classification", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Intent Discovery", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["SNIPS", "ATIS", "Persian-ATIS"], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Text Classification", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-Shot Text Classification", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Citation Intent Classification", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Partial Domain Adaptation", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-target Domain Adaptation", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["Office-Home", "Office-31", "DomainNet", "OBJ-MDA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Knowledge Graph Embedding", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["FB15k"], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON>", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["PCam"], "metrics": [], "task_id": "", "area": ""}, {"name": "Logical Reasoning Question Answering", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ces-eng", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation deu-fra", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Spoken language identification", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Music Auto-Tagging", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["TimeTravel", "Million Song Dataset", "MagnaTagATune", "MagnaTagATune (clean)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Music Tagging", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "End-To-End Dialogue Modelling", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON> (multi-choices) (Zero-Shot)", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON> (multi-choices) (Few-Shot)", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Morphological Tagging", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eng-spa", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ben-eng", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Grasp Generation", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "text-based games", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-hop Question Answering", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["MuSiQue-Ans", "ConcurrentQA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Temporal/Casual QA", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Math Word Problem SolvingΩ", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Music Recommendation", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Reranking", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sports Understanding", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Out-of-Distribution Generalization", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Referring expression generation", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["ColonINST-v1 (Seen)", "ColonINST-v1 (Unseen)"], "metrics": [], "task_id": "", "area": ""}, {"name": "tabular-classification", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Classification with Binary Weight Network", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentence-Embedding", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Classification with Binary Neural Network", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Parsing", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["4D-DRESS", "PASCAL Context"], "metrics": [], "task_id": "", "area": ""}, {"name": "Few-Shot Class-Incremental Learning", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "text2text-generation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SQL Parsing", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Chinese Sentiment Analysis", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Text Style Transfer", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multibehavior Recommendation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Molecular Graph Generation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Blended-target Domain Adaptation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Talking Head Generation", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["VoxCeleb1 - 1-shot learning", "100 sleep nights of 8 caregivers", "VoxCeleb2 - 32-shot learning", "VoxCeleb1 - 8-shot learning", "VoxCeleb2 - 1-shot learning", "VoxCeleb1 - 32-shot learning", "VoxCeleb2 - 8-shot learning"], "metrics": [], "task_id": "", "area": ""}, {"name": "Annotated Code Search", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Automatic Sleep Stage Classification", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["Sleep-EDF", "ISRUC-Sleep"], "metrics": [], "task_id": "", "area": ""}, {"name": "Topological Data Analysis", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentence Fusion", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Few-Shot Point Cloud Classification", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Recipe Generation", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["allrecipes.com", "Food.com", "Now You're Cooking!", "Recipe1M", "RecipeNLG"], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ces-deu", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Similarity", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["IMDb"], "metrics": [], "task_id": "", "area": ""}, {"name": "Spoken Dialogue Systems", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation spa-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation afr-deu", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation afr-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cat-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation rus-por", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eng-por", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ara-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cym-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation dan-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation heb-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hun-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cat-fra", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation glg-spa", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation por-fra", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation por-spa", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation dan-spa", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ara-fra", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation heb-fra", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation heb-por", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation afr-spa", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ceb-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ces-spa", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hin-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hun-deu", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hun-fra", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hun-por", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ind-deu", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ind-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ind-fra", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mar-eng", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Arabic Text Diacritization", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["CATT", "<PERSON><PERSON><PERSON><PERSON>"], "metrics": [], "task_id": "", "area": ""}, {"name": "Font Generation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "automatic-speech-translation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Contextual Embedding for Source Code", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Short-Text Conversation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Food recommendation", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["Oktoberfest Food Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Knowledge Base Population", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["LM-KBC 2023"], "metrics": [], "task_id": "", "area": ""}, {"name": "Session-Based Recommendations", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Intent Classification and Slot Filling", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Facial Makeup Transfer", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text to Speech", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Document Translation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hin-fra", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Music Style Transfer", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Probing Language Models", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["KAMEL"], "metrics": [], "task_id": "", "area": ""}, {"name": "Word Similarity", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["WS353"], "metrics": [], "task_id": "", "area": ""}, {"name": "ContextNER", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Land Cover Classification", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text-based de novo Molecule Generation", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["ChEBI-20"], "metrics": [], "task_id": "", "area": ""}, {"name": "text similarity", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Lemmatization", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Word Translation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Data Summarization", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Pair Classification", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Document Shadow Removal", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentence-Pair Classification", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Music Question Answering", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["MusicQA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Commonsense Causal Reasoning", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-modal Recommendation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "TruthfulQA", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Informal-to-formal Style Transfer", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Facial Expression Translation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Point Cloud Generation", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["ShapeNet Chair", "ShapeNet", "ShapeNet Car", "ShapeNet Airplane"], "metrics": [], "task_id": "", "area": ""}, {"name": "NR-IQA", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-shot Relation Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Passage Re-Ranking", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["TREC-PM", "MS MARCO"], "metrics": [], "task_id": "", "area": ""}, {"name": "Chinese Sentence Pair Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Linear-Probe Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Environment Sound Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "One-shot Unsupervised Domain Adaptation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Explainable Recommendation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Movie Recommendation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-label zero-shot learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "CCG Supertagging", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["CCGbank"], "metrics": [], "task_id": "", "area": ""}, {"name": "Unconditional Molecule Generation", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["QM9", "GEOM-DRUGS"], "metrics": [], "task_id": "", "area": ""}, {"name": "Extended Summarization", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Open-Set Multi-Target Domain Adaptation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Few-Shot NLI", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-Shot Learning + Domain Generalization", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual POS Tagging", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Reasoning Chain Explanations", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fra-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ces-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Dialogue Rewriting", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["CANARD", "Rewrite", "Multi-Rewrite"], "metrics": [], "task_id": "", "area": ""}, {"name": "domain classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual Sentiment Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Phrase Ranking", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["KP20k", "KPTimes"], "metrics": [], "task_id": "", "area": ""}, {"name": "Phrase Tagging", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["KP20k", "KPTimes"], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-shot Audio Captioning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Music Genre Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual Paraphrase Identification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Re-Ranking", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Natural Language Inference (Few-Shot)", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Veracity Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation tur-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation deu-afr", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eng-afr", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eng-nld", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nld-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation rus-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation rus-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation rus-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bel-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eng-cat", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eng-tur", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bul-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation gle-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ell-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation est-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation isl-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fao-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hrv-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pol-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kor-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cat-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cat-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fra-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fra-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation glg-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ita-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ita-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ron-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ron-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bul-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hrv-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hrv-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mkd-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation dan-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation isl-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ara-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bul-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hrv-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation deu-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation heb-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fas-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation dan-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation deu-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ell-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ell-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ell-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ell-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation epo-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation epo-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation epo-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation epo-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation est-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eus-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hun-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hye-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ilo-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation isl-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ita-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation jpn-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation jpn-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation jpn-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kat-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mal-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mlt-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pes-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pol-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pol-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pol-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pol-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation por-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ron-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ron-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation run-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation run-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Common Sense Reasoning (Zero-Shot)", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Knowledge Tracing", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["EdNet", "Assistments"], "metrics": [], "task_id": "", "area": ""}, {"name": "Paper generation (Conclusion-to-title)", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Age Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Physiological Computing", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "English-Ukrainian Translation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Context Query Reformulation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-Shot Region Description", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Extreme Multi-Label Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Inference Attack", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Adversarial Text", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vietnamese Machine Reading Comprehension", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-modal Knowledge Graph", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Claim Verification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Caption Generation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cancer type classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Robot Manipulation Generalization", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bel-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation afr-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation afr-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation amh-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ben-<PERSON><PERSON>", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ceb-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ceb-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ceb-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cym-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cym-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation est-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation gle-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hau-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hye-deu", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Core Psychological Reasoning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentence ReWriting", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multilingual text classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Knowledge Base Completion", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Code Comment Generation", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["DeepCom"], "metrics": [], "task_id": "", "area": ""}, {"name": "General Knowledge", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Logical Fallacies", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "single catogory classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Behavioral Malware Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Job classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vocal technique classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentence Ordering", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["EconLogicQA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multilingual Machine Comprehension in English Hindi", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["Extended XQuAD"], "metrics": [], "task_id": "", "area": ""}, {"name": "Point Cloud Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["ISPRS", "PointCloud-C"], "metrics": [], "task_id": "", "area": ""}, {"name": "Text based Person Search", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation rus-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ben-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ben-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ben-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hau-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hin-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hin-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ind-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mar-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mar-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mar-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation por-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation prs-eng", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation prs-fra", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation prs-por", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation prs-spa", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Lung Sound Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["ICBHI Respiratory Sound Database"], "metrics": [], "task_id": "", "area": ""}, {"name": "Generalized Few-Shot Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-Shot Machine Translation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Conversational Search", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Genome Understanding", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sound Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Atomic number classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["CHILI-3K", "CHILI-100K"], "metrics": [], "task_id": "", "area": ""}, {"name": "Crystal system classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Space group classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Distance regression", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["CHILI-3K", "CHILI-100K"], "metrics": [], "task_id": "", "area": ""}, {"name": "World Knowledge", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "POS Tagging", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["WSJ POS", "Twitter POS"], "metrics": [], "task_id": "", "area": ""}, {"name": "Patent classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Drug ATC Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["ATC-SMILES", "ATC-GRAPH"], "metrics": [], "task_id": "", "area": ""}, {"name": "2-task Classification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Continuously Indexed Domain Adaptation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "candy animation generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "text-to-speech", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SQL-to-Text", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["WikiSQL"], "metrics": [], "task_id": "", "area": ""}, {"name": "Sql Chatbots", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Error Understanding", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["CUB-200-2011", "CUB-200-2011 (ResNet-101)"], "metrics": [], "task_id": "", "area": ""}, {"name": "TREC 2019 Passage Ranking", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Passage Ranking", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["MS MARCO"], "metrics": [], "task_id": "", "area": ""}, {"name": "Temporal Sentence Grounding", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Ego4D-Goalstep", "Charades-STA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Self-Supervised Audio Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-Shot Environment Sound Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Paraphrase Identification within Bi-Encoder", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Domain Document Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Media Recommendation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Recommendation Systems (Item cold-start)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sparse Representation-based Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Concept-To-Text Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Few-shot Age Estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Domain Adaptationn", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "cross-domain few-shot learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Memex Question Answering", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["MemexQA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Single-Source Domain Generalization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["PACS", "Digits-five"], "metrics": [], "task_id": "", "area": ""}, {"name": "Thoracic Disease Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Pulmonary Artery–Vein Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Lung Nodule Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["LIDC-IDRI"], "metrics": [], "task_id": "", "area": ""}, {"name": "Chinese Document Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Ranking", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["ogbg-molfreesolv", "ogbg-mollipo", "ZINC", "ogbg-molesol"], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentence Similarity", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "LWR Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Pill Classification (Both Sides)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fra-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Entity Cross-Document Coreference Resolution", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Mitigating Contextual Bias", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Semi-supervised Domain Adaptation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual Bitext Mining", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BUCC Russian-to-English", "BUCC French-to-English", "BUCC Chinese-to-English", "BUCC German-to-English"], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Question Answering", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["GQA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Pitch Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual ASR", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Speech-to-Text", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Open Knowledge Graph Embedding", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Legal Document Summarization", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Text Summarization", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Infilling", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Procedural Text Understanding", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Natural Language Inference (Zero-Shot)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Natural Language Inference (One-Shot)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation afr-nld", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation deu-nld", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ltz-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ltz-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ltz-nld", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nld-afr", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nld-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fry-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fry-nld", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hrx-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hrx-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nds-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nds-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nds-nld", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nld-fry", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nld-nds", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation gos-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation gos-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bel-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bel-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bos_Latn-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hbs-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lav-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation slv-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation oci-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bul-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation slv-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hbs-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hbs-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation slv-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hbs-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eus-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation deu-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eng-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation awa-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation epo-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation jpn-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kaz-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fas-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation msa-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation msa-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ara-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cmn_<PERSON>-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cmn_<PERSON>-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cmn_<PERSON>-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cmn_Hans-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cmn_Hant-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cmn_Hant-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cmn_Hant-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cmn_Hant-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation dsb-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hsb-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ido_Latn-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kaz_Cyrl-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lfn_Latn-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lfn_Latn-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lfn_Latn-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nor-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation por-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Micro-expression Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Wildly Unsupervised Domain Adaptation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Common Sense Reasoning (Few-Shot)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Common Sense Reasoning (One-Shot)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "multi-word expression embedding", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi Class Text Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Controllable Grasp Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Conversation Disentanglement", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Traffic Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Fashion Understanding", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["ModaNet Dev"], "metrics": [], "task_id": "", "area": ""}, {"name": "Complex Word Identification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Audio Question Answering", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Point cloud classification dataset", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "dialogue summary", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Chinese Landscape Painting Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Complexity Assessment (GermEval 2022)", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["TextComplexityDE"], "metrics": [], "task_id": "", "area": ""}, {"name": "Lip password classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Open Knowledge Graph Canonicalization", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Poem meters classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["PCD"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Domain Sentiment Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Word-level pronunciation scoring", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "EEG Signal Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Keyword Spotting CSS", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Scale Generalisation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Morphological Disambiguation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation oci-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eng-oci", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eng-ell", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bul-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation dan-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fas-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cym-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation zho-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "IFC Entity Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["IFCNetCore"], "metrics": [], "task_id": "", "area": ""}, {"name": "Handwritten Word Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "TFLM sequence generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Heartbeat Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Compositional Generalization (AVG)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Czech Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "Vietnamese Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "Romanian Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "Slovak Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "Latvian Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "Polish Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "Irish Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "Hungarian Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "French Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "Turkish Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "Spanish Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "Croatian Text Diacritization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems"], "metrics": [], "task_id": "", "area": ""}, {"name": "CodeSearchNet - Java", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Audio Multiple Target Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Syntax Representation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-shot Generalization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["CALVIN"], "metrics": [], "task_id": "", "area": ""}, {"name": "Analogical Similarity", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Understanding Fables", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentence Ambiguity", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "TriviaQA", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Analytic Entailment", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Epistemic Reasoning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Logical Args", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Clinical Knowledge", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Fantasy Reasoning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "GRE Reading Comprehension", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Nonsense Words Grammar", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multlingual Neural Machine Translation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Font Style Transfer", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-Shot Intent Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-Shot Intent Classification and Slot Filling", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Time-Series Few-Shot Learning with Heterogeneous Channels", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Question to Declarative Sentence", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Skill Generalization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["RGB-Stacking"], "metrics": [], "task_id": "", "area": ""}, {"name": "Semi-supervised time series classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Jet Tagging", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fin-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nob-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nno-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ita-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mkd-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ast-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ast-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation glg-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ita-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation oci-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ron-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hrv-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mkd-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mkd-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation slv-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation isl-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nob-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nob-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mkd-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lim-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation acm-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation acm-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation acm-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation acm-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation acm-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation amh-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation apc-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation apc-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation apc-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation apc-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation arz-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation arz-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation arz-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation arz-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation arz-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ast-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ast-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation awa-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation awa-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bho-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bho-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation bho-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cat-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ceb-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ces-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ckb-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ckb-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation crh-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation crh-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation crh-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation crh-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation crh-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation cym-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation est-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation est-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eus-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eus-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation eus-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fao-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fao-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fao-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fao-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fij-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fur-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fur-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fur-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation gla-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation gle-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation gle-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation gle-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation glg-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation glg-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation grn-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation grn-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation grn-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation guj-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation guj-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation guj-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation guj-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hat-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hat-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hat-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hat-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hat-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hin-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hne-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hne-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hne-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hne-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hne-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hye-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hye-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hye-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ibo-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ilo-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ilo-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ilo-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ilo-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation jav-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation jav-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation jav-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation jav-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation jav-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kat-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kat-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kat-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kaz-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kaz-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kaz-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kea-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kea-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kea-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kea-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kea-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation kon-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lij-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lij-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lij-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lij-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lim-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lim-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lim-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lin-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lin-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lin-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mag-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mal-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mar-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mlt-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mlt-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mlt-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation mlt-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nno-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation npi-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation npi-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nso-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation nya-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation oci-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pag-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pag-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pag-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pan-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pan-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pan-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pan-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pap-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pap-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pap-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pap-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pap-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pes-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pes-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pes-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pes-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation plt-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation plt-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation plt-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation plt-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation prs-deu", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation run-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation run-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "text political leaning classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-Shot Counting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Question-Answer-Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Transfer Reinforcement Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Document Level Machine Translation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Persona Dialogue in Story", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Enumerative Search", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Stroke Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Facial expression generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ArzEn Code-switched Translation to eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ArzEn Code-switched Translation to ara", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Speech Intent Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Label shift of blended-target domain adaptation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero-shot Sentiment Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["AfriSenti"], "metrics": [], "task_id": "", "area": ""}, {"name": "Steiner Tree Problem", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Early  Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["ECG200"], "metrics": [], "task_id": "", "area": ""}, {"name": "Vietnamese Natural Language Inference", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["ViNLI"], "metrics": [], "task_id": "", "area": ""}, {"name": "Profile Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Generalized Referring Expression Comprehension", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["gRefCOCO"], "metrics": [], "task_id": "", "area": ""}, {"name": "Superclass classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Pretrained Multilingual Language Models", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Transferability", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["classification benchmark"], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ara-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation fas-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation ind-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hau-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation hau-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation msa-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation lug-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation msa-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pus-eng", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pus-fra", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pus-por", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Translation pus-spa", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Summarization Consistency Evaluation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "open-set classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Tumour Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "intent-classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hint Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "OpenAPI code completion", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["OpenAPI completion refined"], "metrics": [], "task_id": "", "area": ""}, {"name": "Edge Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Person Identification (zero-shot)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Temporal Complex Logical Reasoning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Knowledge-Aware Recommendation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Artist classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text-to-GQL", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "text-classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Music Genre Transfer", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text2Sparql", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Raw vs Ripe (Generic)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Description-guided molecule generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["TOMG-Bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "FS-MEVQA", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Time Series Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text-Variation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multimodal Large Language Model", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Small-Footprint Keyword Spotting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cancer Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Multi-omics mRNA, miRNA, and DNA Methylation Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Asthmatic Lung Sound Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Chest wall lung sound dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Lung Disease Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Poker Hand Classification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Conversational Recommendation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Unconditional Crystal Generation", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["MP20"], "metrics": [], "task_id": "", "area": ""}, {"name": "Perceptual Distance", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "TinyQA Benchmark++", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["tinyqabenchmark_core-en"], "metrics": [], "task_id": "", "area": ""}, {"name": "Reranking", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "AMR Graph Similarity", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Benchmark for AMR Metrics based on Overt Objectives"], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph-To-Graph Translation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Acoustic Question Answering", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Classification Of Variable Stars", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Bird Classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Web Page Tagging", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Collaborative Ranking", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Age-Related Macular Degeneration Classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Joint NER and Classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Knowledge Graphs Data Curation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vowel Classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Generalized Zero-Shot Learning - Unseen", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "breast density classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Git Commit Message Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["CommitGen"], "metrics": [], "task_id": "", "area": ""}, {"name": "Oceanic Eddy Classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sperm Morphology Classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "API Sequence Recommendation", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["DeepAPI"], "metrics": [], "task_id": "", "area": ""}, {"name": "House Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Immune Repertoire Classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Leadership Inference", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Commonsense Reasoning for RL", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["commonsense-rl"], "metrics": [], "task_id": "", "area": ""}, {"name": "Next-basket recommendation", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Instacart", "TaFeng"], "metrics": [], "task_id": "", "area": ""}, {"name": "band gap classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Ticket Search", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Temporal Knowledge Graph Completion", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Log Parsing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Animated GIF Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Generalization Bounds", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Classification on Time Series with Missing Data", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sequential Bayesian Inference", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Chinese Spell Checking", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["SIGHAN 2015"], "metrics": [], "task_id": "", "area": ""}, {"name": "Complaint Comment Classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vietnamese Parsing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Learning Language specific models", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Approximating Betweenness-Centrality ranking", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Diachronic Word Embeddings", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Subdomain adaptation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Anonymization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Lingual Word Embeddings", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Geographic Question Answering", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Judgment Classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Pascal-50S"], "metrics": [], "task_id": "", "area": ""}, {"name": "Phenotype classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["MIMIC-CXR, MIMIC-IV"], "metrics": [], "task_id": "", "area": ""}, {"name": "Hindu Knowledge", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Edit script generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "energy management", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Wireframe Parsing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Phonocardiogram Classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Community Search", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Prompt-driven Zero-shot Domain Adaptation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "text-guided-generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Readability optimization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Comment Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Coding Problem Tagging", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Legal Reasoning", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["LegalBench (Issue-spotting)", "LegalBench (Rule-recall)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Potrait Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Voice Similarity", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Change Data Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Semi-Supervised Domain Generalization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Topological Risk Measure", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Quantum Circuit Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Reference Expression Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Ordinal Classification", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["OASIS+NACC+ICBM+ABIDE+IXI"], "metrics": [], "task_id": "", "area": ""}, {"name": "In-Context Learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sentence", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Generative Temporal Nursing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vietnamese Language Models", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vietnamese Natural Language Understanding", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Simultaneous Speech-to-Speech Translation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Adversarial Natural Language Inference", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Runtime ranking", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["TpuGraphs Layout mean"], "metrics": [], "task_id": "", "area": ""}, {"name": "GRAPH DOMAIN ADAPTATION", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["FRANKENSTEIN"], "metrics": [], "task_id": "", "area": ""}, {"name": "Conversational Information Access", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Script Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Urban Itinerary Planning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Semi-Supervised Text Regression", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multimodal Music Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Dataset Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "MUlTI-LABEL-ClASSIFICATION", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "NeRF", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Layout Generation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Small Language Model", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Appearance Transfer", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Language Model Evaluation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Text Normalization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "total_tasks": 1603}
# Papers with code datasets

You can download the full dataset behind [paperswithcode.com](https://paperswithcode.com) here:

Download links for the data dumps are:

- [All papers with abstracts](https://production-media.paperswithcode.com/about/papers-with-abstracts.json.gz)
- [Links between papers and code](https://production-media.paperswithcode.com/about/links-between-papers-and-code.json.gz)
- [Evaluation tables](https://production-media.paperswithcode.com/about/evaluation-tables.json.gz)
- [Methods](https://production-media.paperswithcode.com/about/methods.json.gz)
- [Datasets](https://production-media.paperswithcode.com/about/datasets.json.gz)

The last JSON is in the [sota-extractor](https://github.com/paperswithcode/sota-extractor) format and the code
from there can be used to load in the JSON into a set of Python classes. 

At the moment, data is regenerated daily.

Part of the data is coming from the sources listed in the [sota-extractor README](https://github.com/paperswithcode/sota-extractor).

## Licence

All data is licenced under [CC-BY-SA](https://creativecommons.org/licenses/by-sa/4.0/). 



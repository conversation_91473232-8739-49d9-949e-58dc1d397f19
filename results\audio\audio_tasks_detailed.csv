Task Name,Dataset Count,Common Datasets (Top 10),Benchmarks,SOTA Metrics,Subtasks,Categories,Description (First 200 chars)
Speech Separation,16,LRS2; LibriCSS; GRID Dataset; iKala; TIMIT; mDRT; MIR-1K; VoxCeleb2; WHAMR!; WHAM!,TCD-TIMIT corpus (mixed-speech); Libri20Mix; Libri10Mix; WSJ0-2mix; Libri5Mix; WSJ0-5mix; GRID corpus (mixed-speech); iKala; Libri15Mix; VoxCeleb2,30%; 10%; SI-SDRi; Number of parameters (M); MACs (G); SI-SNRi; 40%; STOI; 20%; NSDR,Speech Extraction,Speech,"The task of extracting all overlapping speech sources in a given mixed speech signal refers to the **Speech Separation**. Speech Separation is a special scenario of source separation problem, where th..."
Audio Source Separation,15,MUSDB18; FUSS; SMS-WSJ; Kinect-WSJ; DNS Challenge; jaCappella; MedleyVox; WHAMR!; OpenMIC-2018; WHAM!,MUSIC (multi-source); AudioSet,SAR; SDR; SIR,Directional Hearing; Target Sound Extraction; Single-Label Target Sound Extraction,Audio,"**Audio Source Separation** is the process of separating a mixture (e.g. a pop band recording) into isolated sounds from individual sources (e.g. just the lead vocals).      <span class=""description-s..."
Music Transcription,14,CocoChorales; Guitar-TECHS; JS Fake Chorales; ErhuPT; URMP; Music21; MusicNet; MAPS; YourMT3 Dataset; Cadenza Woodwind,URMP; MAPS; MusicNet; Slakh2100; SMD Piano; MAESTRO,Onset F1; APS; Number of params; note-level F-measure-no-offset (Fno),Multi-instrument Music Transcription,Music,"Music transcription is the task of converting an acoustic musical signal into some form of music notation.    <span style=""color:grey; opacity: 0.6"">( Image credit: [ISMIR 2015 Tutorial - Automatic Mu..."
Speaker Verification,12,VibraVox (throat microphone); CN-CELEB; CALLHOME American English Speech; VibraVox (temple vibration pickup); EVI; VibraVox (rigid in-ear microphone); VoxCeleb2; VibraVox (headset microphone); VibraVox (soft in-ear microphone); VibraVox (forehead accelerometer),VibraVox (throat microphone); CALLHOME; VibraVox (temple vibration pickup); VoxCeleb1; VoxCeleb2; VibraVox (headset microphone); VoxCeleb; VibraVox (soft in-ear microphone); VibraVox (forehead accelerometer); VibraVox (rigid in-ear microphone),Test EER; minDCF; Cosine EER; EER; Test min-DCF,Text-Independent Speaker Verification; Audio Deepfake Detection; Text-Dependent Speaker Verification,Speech,"Speaker verification is the verifying the identity of a person from characteristics of the voice.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Contrastive-Predictive-Coding-PyTorch  ](ht..."
Speaker Diarization,11,AVA-Speech; MagicData-RAMC; FSC-P2; CALLHOME American English Speech; RadioTalk; AVA; ASR-RAMC-BIGCCSC: A CHINESE CONVERSATIONAL SPEECH CORPUS; CHiME-5; AliMeeting; AVA-ActiveSpeaker,DIHARD; DIHARD3-eval; AMI; CALLHOME; call_home_american_english_speech; AMI MixHeadset; AMI Lapel; CH109; Hub5'00 CallHome; NIST-SRE 2000,FA; MI; CF; DER(%); V; Miss; DER - no overlap; DER(ig olp),N/A,Speech,"**Speaker Diarization** is the task of segmenting and co-indexing audio recordings by speaker. The way the task is commonly defined, the goal is not to identify known speakers, but to co-index segment..."
Music Source Separation,9,MUSDB18; CocoChorales; SynthSOD; MuseScore; MIR-1K; MUSDB18-HQ; Cadenza Woodwind; Slakh2100; MedleyDB,MUSDB18; MUSDB18-HQ; Slakh2100,Si-SDRi (Guitar); SDR (avg); SDR (others); SI-SDRi (Bass); SDR (drums); Si-SDRi (Piano); SDR (vocals); SDR (other); Si-SDRi (Drums); SDR (bass),N/A,Music,"Music source separation is the task of decomposing music into its constitutive components, e. g., yielding separated stems for the vocals, bass, and drums.    <span style=""color:grey; opacity: 0.6"">( ..."
Voice Conversion,8,ESD; ArVoice; GneutralSpeech Male; GneutralSpeech Female; VESUS; LibriSpeech; VIVOS; VCTK,LibriSpeech test-clean; ZeroSpeech 2019 English; VCTK,Phone Length Error (PLE); Equal Error Rate; Total Length Error (TLE); Character Error Rate (CER); Speaker Similarity; Word Error Rate (WER); Word Length Error (WLE),N/A,Speech; Audio,I remember all the summer days  Drinking wine in the sunshine  I hope it never leaves  And I remember all the summer nights  Staring at you in the moonlight  I hope you never leave 'cause baby  You're...
Music Modeling,6,JS Fake Chorales; Lakh Pianoroll Dataset; Lakh MIDI Dataset; Music21; Nottingham; JSB Chorales,JSB Chorales; Nottingham,Parameters; NLL,N/A,Music,"<span style=""color:grey; opacity: 0.6"">( Image credit: [R-Transformer](https://arxiv.org/pdf/1907.05572v1.pdf) )</span>"
Speech Dereverberation,6,EARS-Reverb; Reverb-WSJ0; WHAMR_ext; WHAM!; WHAMR!; DNS Challenge,N/A,N/A,N/A,N/A,N/A
Multi-instrument Music Transcription,6,URMP; ENST Drums; YourMT3 Dataset; MIR-ST500; Cadenza Woodwind; Slakh2100,N/A,N/A,N/A,N/A,N/A
Voice Anti-spoofing,6,PartialSpoof_v1; ASVspoof 2019; PartialSpoof; Laser Data; ASVspoof 5; ReMASC,ASVspoof2019; ASVspoof 2019 - LA; ASVspoof 2019 - PA,minDCF; min a-DCF; EER; min t-dcf,N/A,Audio,Discriminate genuine speech and spoofing attacks
Audio captioning,5,Clotho; WavCaps; AudioCaps; LongVALE; MACS,Clotho; AudioCaps,SPICE; FENSE; SPIDEr; #params (M); METEOR; SPIDEr-FL; Sentence-BERT; ROUGE; ROUGE-L; CIDEr,Retrieval-augmented Few-shot In-context Audio Captioning; Zero-shot Audio Captioning,Audio,"Audio Captioning is the task of describing audio using text. The general approach is to use an audio encoder to encode the audio (example: PANN, CAV-MAE), and to use a decoder (example: transformer) t..."
Music Captioning,5,Song Describer Dataset; JamendoMaxCaps; MusicCaps; MidiCaps; YouTube8M-MusicTextClips,N/A,N/A,N/A,Music,N/A
Speaker Identification,4,FSC-P2; CSI; EVI; VoxCeleb1,EVI en-GB; VoxCeleb1; EVI fr-FR; EVI pl-PL,Accuracy; Number of Params; Top-1 (%); Top-5 (%),N/A,Speech,N/A
Speaker Separation,3,FSDnoisy18k; LibriCSS; MC_GRID,N/A,N/A,Multi-Speaker Source Separation,Speech,N/A
Multi-task Audio Source Seperation,3,MTASS; CocoChorales; Acappella,N/A,N/A,N/A,N/A,N/A
Rhythm,2,PTB-XL; Fraxtil,N/A,N/A,N/A,N/A,N/A
Piano Music Modeling,2,Niko Chord Progression Dataset; PIAST,N/A,N/A,N/A,Music,N/A
Audio Signal Processing,2,mDRT; RemFX,N/A,N/A,blind source separation; Audio Effects Modeling; Audio Compression,Audio,"This is a general task that covers transforming audio inputs into audio outputs, not limited to existing PaperWithCode categories of Source Separation, Denoising, Classification, Recognition, etc."
Voice Cloning,2,GneutralSpeech Female; GneutralSpeech Male,N/A,N/A,N/A,N/A,N/A
Drum Transcription in Music (DTM),2,YourMT3 Dataset; ENST Drums,N/A,N/A,N/A,N/A,N/A
Salt-And-Pepper Noise Removal,1,BSD,N/A,N/A,N/A,N/A,N/A
Cross-environment ASR,1,Libri-Adapt,N/A,N/A,N/A,N/A,N/A
Cross-device ASR,1,Libri-Adapt,N/A,N/A,N/A,N/A,N/A
Noise Estimation,1,SIDD,SIDD,Average KL Divergence; PSNR Gap,N/A,Medical,N/A
Audio Fingerprint,1,Fingerprint Dataset,N/A,N/A,N/A,N/A,N/A
Soundscape evaluation,1,Subjective Perception of Active Noise Reduction (SPANR),N/A,N/A,N/A,Audio,Evaluation of soundscape in accordance to ISO/TS 12913-2
"Speaker Attribution in German Parliamentary Debates (GermEval 2023, subtask 1)",1,GePaDe,GePaDe,F1,N/A,Natural Language Processing,"Subtask 1 (full task) consists of predicting the cue words that trigger a speech event, together with the associated roles and their respective labels."
"Speaker Attribution in German Parliamentary Debates (GermEval 2023, subtask 2)",1,GePaDe,GePaDe,F1,N/A,N/A,"Subtask 2 (role labelling): Given the gold cue words,  the task consists in identifying the spans for all associated roles expressed in the text, together with their respective labels."
Acoustic Modelling,1,SALMon,N/A,N/A,N/A,Speech,N/A
Speaker Profiling,1,HeightCeleb,N/A,N/A,N/A,Speech,Estimation of Physical parameters from Speech data
Recognizing Seven Different Dastgahs Of Iranian Classical Music,0,N/A,N/A,N/A,N/A,Music,N/A
Audio declipping,0,N/A,N/A,N/A,N/A,Audio,Audio declipping is the task of estimating the original audio signal given its clipped measurements.
Audio Dequantization,0,N/A,N/A,N/A,N/A,Audio,Audio Dequantization is a process of estimating the original signal from its quantized counterpart.
Speaker Orientation,0,N/A,N/A,N/A,N/A,Audio,Direction of Voice or speaker orientation of the person with respect to the target device.
Hate Speech Normalization,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
blind source separation,0,N/A,N/A,N/A,N/A,N/A,"Blind source separation (BSS) is a signal processing technique that aims to separate multiple source signals from a set of mixed signals, without any prior knowledge about the sources or the mixing pr..."
Speech Representation Learning,0,N/A,N/A,N/A,N/A,Speech,N/A
Semi-Supervised Audio Regression,0,N/A,N/A,N/A,N/A,Audio,N/A
Speaker anonymization,0,N/A,N/A,N/A,N/A,Speech,N/A

[{"categories": [], "datasets": [{"dataset": "ogbn-products", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test Accuracy", "Validation Accuracy", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/skepsun/SAGN_with_SLE"}], "metrics": {"Number of params": "2179678", "Test Accuracy": "0.8428 ± 0.0014", "Validation Accuracy": "0.9287 ± 0.0003"}, "model_links": [], "model_name": "SAGN+SLE", "paper_date": "2021-04-19", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.09376", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/CorrectAndSmooth"}], "metrics": {"Number of params": "96247", "Test Accuracy": "0.8418 ± 0.0007", "Validation Accuracy": "0.9147 ± 0.0009"}, "model_links": [], "model_name": "MLP + C&S", "paper_date": "2020-10-27", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/CorrectAndSmooth"}], "metrics": {"Number of params": "10763", "Test Accuracy": "0.8301 ± 0.0001", "Validation Accuracy": "0.9134 ± 0.0001"}, "model_links": [], "model_name": "Linear + C&S", "paper_date": "2020-10-27", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/PaddlePaddle/PGL/tree/main/ogb_examples/nodeproppred/unimp"}], "metrics": {"Number of params": "1475605", "Test Accuracy": "0.8256 ± 0.0031", "Validation Accuracy": "0.9308 ± 0.0017"}, "model_links": [], "model_name": "UniMP", "paper_date": "2020-09-08", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2009.03509.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/CorrectAndSmooth"}], "metrics": {"Number of params": "4747", "Test Accuracy": "0.8254 ± 0.0003", "Validation Accuracy": "0.9103 ± 0.0001"}, "model_links": [], "model_name": "Plain Linear + C&S", "paper_date": "2020-10-27", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "253743", "Test Accuracy": "0.8193 ± 0.0031", "Validation Accuracy": "0.9221 ± 0.0037"}, "model_links": [], "model_name": "DeeperGCN+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "751574", "Test Accuracy": "0.8176 ± 0.0045", "Validation Accuracy": "0.9251 ± 0.0006"}, "model_links": [], "model_name": "GAT+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/PyG-ogbn-products/tree/main/sage%2Bnode2vec"}], "metrics": {"Number of params": "103983", "Test Accuracy": "0.8154 ± 0.0050", "Validation Accuracy": "0.9238 ± 0.0006"}, "model_links": [], "model_name": "GraphSAGE + C&S + node2vec", "paper_date": "2021-04-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/skepsun/SAGN_with_SLE"}], "metrics": {"Number of params": "2233391", "Test Accuracy": "0.8120 ± 0.0007", "Validation Accuracy": "0.9309 ± 0.0004"}, "model_links": [], "model_name": "SAGN", "paper_date": "2021-04-19", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.09376", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/tree/master/examples/ogb"}], "metrics": {"Number of params": "253743", "Test Accuracy": "0.8098 ± 0.0020", "Validation Accuracy": "0.9238 ± 0.0009"}, "model_links": [], "model_name": "DeeperGCN", "paper_date": "2020-06-28", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.07739", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/PyG-ogbn-products/tree/main/gat"}], "metrics": {"Number of params": "753622", "Test Accuracy": "0.8092 ± 0.0037", "Validation Accuracy": "0.9263 ± 0.0008"}, "model_links": [], "model_name": "GAT w/NS + C&S", "paper_date": "2021-04-04", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/sign"}], "metrics": {"Number of params": "3483703", "Test Accuracy": "0.8052 ± 0.0016", "Validation Accuracy": "0.9299 ± 0.0004"}, "model_links": [], "model_name": "SIGN", "paper_date": "2020-11-05", "paper_title": "", "paper_url": "https://arxiv.org/abs/2004.11198", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/PyG-ogbn-products/tree/main/sage"}], "metrics": {"Number of params": "207919", "Test Accuracy": "0.8041 ± 0.0022", "Validation Accuracy": "0.9238 ± 0.0007"}, "model_links": [], "model_name": "GraphSAGE w/NS + C&S", "paper_date": "2021-04-05", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/GraphSAINT/GraphSAINT/tree/master/graphsaint/open_graph_benchmark"}], "metrics": {"Number of params": "331661", "Test Accuracy": "0.8027 ± 0.0026", "Validation Accuracy": "Please tell us"}, "model_links": [], "model_name": "GraphSAINT-inductive", "paper_date": "2020-07-13", "paper_title": "", "paper_url": "https://arxiv.org/abs/1907.04931", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/ogb_baselines"}], "metrics": {"Number of params": "456034", "Test Accuracy": "0.7971 ± 0.0042", "Validation Accuracy": "0.9188 ± 0.0008"}, "model_links": [], "model_name": "ClusterGCN+residual+3 layers", "paper_date": "2020-10-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/1905.07953", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/rusty1s/pytorch_geometric/blob/master/examples/ogbn_products_gat.py"}], "metrics": {"Number of params": "751574", "Test Accuracy": "0.7945 ± 0.0059", "Validation Accuracy": "Please tell us"}, "model_links": [], "model_name": "GAT with NeighborSampling", "paper_date": "2020-05-24", "paper_title": "", "paper_url": "https://arxiv.org/abs/1710.10903", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "206895", "Test Accuracy": "0.7936 ± 0.0057", "Validation Accuracy": "0.9205 ± 0.0007"}, "model_links": [], "model_name": "GraphSAGE+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/cluster-gat"}], "metrics": {"Number of params": "1540848", "Test Accuracy": "0.7923 ± 0.0078", "Validation Accuracy": "0.8985 ± 0.0022"}, "model_links": [], "model_name": "Cluster-GAT", "paper_date": "2020-08-02", "paper_title": "", "paper_url": "https://arxiv.org/abs/1905.07953", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/products"}], "metrics": {"Number of params": "206895", "Test Accuracy": "0.7908 ± 0.0024", "Validation Accuracy": "0.9162 ± 0.0008"}, "model_links": [], "model_name": "GraphSAINT (SAGE aggr)", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/1907.04931", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/products"}], "metrics": {"Number of params": "206895", "Test Accuracy": "0.7897 ± 0.0033", "Validation Accuracy": "0.9212 ± 0.0009"}, "model_links": [], "model_name": "ClusterGCN (SAGE aggr)", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/1905.07953", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/products"}], "metrics": {"Number of params": "206895", "Test Accuracy": "0.7870 ± 0.0036", "Validation Accuracy": "0.9170 ± 0.0009"}, "model_links": [], "model_name": "NeighborSampling (SAGE aggr)", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/products"}], "metrics": {"Number of params": "206895", "Test Accuracy": "0.7850 ± 0.0014", "Validation Accuracy": "0.9224 ± 0.0007"}, "model_links": [], "model_name": "Full-batch GraphSAGE", "paper_date": "2020-06-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/ogbn-products/graphsage"}], "metrics": {"Number of params": "Please tell us", "Test Accuracy": "0.7829 ± 0.0016", "Validation Accuracy": "Please tell us"}, "model_links": [], "model_name": "GraphSAGE", "paper_date": "2020-05-12", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/products"}], "metrics": {"Number of params": "103727", "Test Accuracy": "0.7564 ± 0.0021", "Validation Accuracy": "0.9200 ± 0.0003"}, "model_links": [], "model_name": "Full-batch GCN", "paper_date": "2020-06-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/CorrectAndSmoothOGB"}], "metrics": {"Number of params": "0", "Test Accuracy": "0.7434 ± 0.0000", "Validation Accuracy": "0.9091 ± 0.0000"}, "model_links": [], "model_name": "Label Propagation", "paper_date": "2020-10-03", "paper_title": "", "paper_url": "http://pages.cs.wisc.edu/~jerryzhu/pub/thesis.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/cornell-zhang/GraphZoom/tree/master/ogb/ogbn-products"}], "metrics": {"Number of params": "120251183", "Test Accuracy": "0.7406 ± 0.0026", "Validation Accuracy": "0.9066 ± 0.0011"}, "model_links": [], "model_name": "GraphZoom (Node2vec)", "paper_date": "2020-10-06", "paper_title": "", "paper_url": "https://openreview.net/forum?id=r1lGO0EKDH", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/products"}], "metrics": {"Number of params": "313612207", "Test Accuracy": "0.7249 ± 0.0010", "Validation Accuracy": "0.9032 ± 0.0006"}, "model_links": [], "model_name": "Node2vec", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/1607.00653", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/cf020031308/LinkDist/blob/master/ogbn.py"}], "metrics": {"Number of params": "115806", "Test Accuracy": "0.6259 ± 0.0010", "Validation Accuracy": "0.7721 ± 0.0015"}, "model_links": [], "model_name": "CoLinkDistMLP", "paper_date": "2021-06-17", "paper_title": "", "paper_url": "https://arxiv.org/abs/2106.08541", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "103727", "Test Accuracy": "0.6241 ± 0.0016", "Validation Accuracy": "0.7688 ± 0.0014"}, "model_links": [], "model_name": "MLP+FLAG", "paper_date": "2020-11-17", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/products"}], "metrics": {"Number of params": "103727", "Test Accuracy": "0.6106 ± 0.0008", "Validation Accuracy": "0.7554 ± 0.0014"}, "model_links": [], "model_name": "MLP", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/2005.00687", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbn-proteins", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test ROC-AUC", "Validation ROC-AUC", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/tree/master/examples/ogb_eff/ogbn_proteins"}], "metrics": {"Number of params": "68471608", "Test ROC-AUC": "0.8824 ± 0.0015", "Validation ROC-AUC": "0.9450 ± 0.0008"}, "model_links": [], "model_name": "RevGNN-Wide", "paper_date": "2021-06-16", "paper_title": "", "paper_url": "https://arxiv.org/abs/2106.07476", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/tree/master/examples/ogb_eff/ogbn_proteins"}], "metrics": {"Number of params": "20031384", "Test ROC-AUC": "0.8774 ± 0.0013", "Validation ROC-AUC": "0.9326 ± 0.0006"}, "model_links": [], "model_name": "RevGNN-Deep", "paper_date": "2021-06-16", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2106.07476.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/ogbn-proteins"}], "metrics": {"Number of params": "2484192", "Test ROC-AUC": "0.8765 ± 0.0008", "Validation ROC-AUC": "0.9280 ± 0.0008"}, "model_links": [], "model_name": "GAT+BoT", "paper_date": "2021-06-16", "paper_title": "", "paper_url": "https://arxiv.org/abs/2103.13355", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/PyG-OGB-Tricks/tree/main/DGL-ogbn-proteins"}], "metrics": {"Number of params": "6360470", "Test ROC-AUC": "0.8711 ± 0.0007", "Validation ROC-AUC": "0.9217 ± 0.0011"}, "model_links": [], "model_name": "GAT + labels + node2vec", "paper_date": "2021-06-07", "paper_title": "", "paper_url": "https://arxiv.org/abs/1710.10903", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/yongchao-liu/gipa"}], "metrics": {"Number of params": "4831056", "Test ROC-AUC": "0.8700 ± 0.0010", "Validation ROC-AUC": "0.9187 ± 0.0003"}, "model_links": [], "model_name": "GIPA", "paper_date": "2021-05-13", "paper_title": "", "paper_url": "https://arxiv.org/abs/2105.06035", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/PaddlePaddle/PGL/tree/main/ogb_examples/nodeproppred/ogbn-proteins/unimp_cross_feat"}], "metrics": {"Number of params": "1959984", "Test ROC-AUC": "0.8691 ± 0.0018", "Validation ROC-AUC": "0.9258 ± 0.0009"}, "model_links": [], "model_name": "UniMP+CrossEdgeFeat", "paper_date": "2020-11-24", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2009.03509.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Espylapiza/dgl/tree/master/examples/pytorch/ogb/ogbn-proteins"}], "metrics": {"Number of params": "2475232", "Test ROC-AUC": "0.8682 ± 0.0021", "Validation ROC-AUC": "0.9194 ± 0.0003"}, "model_links": [], "model_name": "GAT+EdgeFeatureAtt", "paper_date": "2020-11-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/2103.13355", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/PaddlePaddle/PGL/tree/main/ogb_examples/nodeproppred/unimp"}], "metrics": {"Number of params": "1909104", "Test ROC-AUC": "0.8642 ± 0.0008", "Validation ROC-AUC": "0.9175 ± 0.0006"}, "model_links": [], "model_name": "UniMP", "paper_date": "2020-09-08", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2009.03509.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "2374568", "Test ROC-AUC": "0.8596 ± 0.0027", "Validation ROC-AUC": "0.9132 ± 0.0022"}, "model_links": [], "model_name": "DeeperGCN+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/tree/master/examples/ogb"}], "metrics": {"Number of params": "2374568", "Test ROC-AUC": "0.8580 ± 0.0017", "Validation ROC-AUC": "0.9106 ± 0.0016"}, "model_links": [], "model_name": "DeeperGCN", "paper_date": "2020-06-16", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.07739", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/blob/master/examples/ogb/ogbn_proteins"}], "metrics": {"Number of params": "2374456", "Test ROC-AUC": "0.8496 ± 0.0028", "Validation ROC-AUC": "0.8971 ± 0.0011"}, "model_links": [], "model_name": "DeepGCN", "paper_date": "2020-06-20", "paper_title": "", "paper_url": "http://openaccess.thecvf.com/content_ICCV_2019/papers/Li_DeepGCNs_Can_GCNs_Go_As_Deep_As_CNNs_ICCV_2019_paper.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/ogbn-proteins"}], "metrics": {"Number of params": "538544", "Test ROC-AUC": "0.8436 ± 0.0065", "Validation ROC-AUC": "0.8973 ± 0.0057"}, "model_links": [], "model_name": "MWE-DGCN", "paper_date": "2020-07-20", "paper_title": "", "paper_url": "https://cims.nyu.edu/~chenzh/files/GCN_with_edge_weights.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/PyG-ogbn-proteins/tree/main/gen%2Bflag"}], "metrics": {"Number of params": "487436", "Test ROC-AUC": "0.8251 ± 0.0043", "Validation ROC-AUC": "0.8656 ± 0.0037"}, "model_links": [], "model_name": "GEN + FLAG + node2vec", "paper_date": "2021-04-15", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.07739", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/xavierzw/ogb-geniepath-bs"}], "metrics": {"Number of params": "316754", "Test ROC-AUC": "0.7825 ± 0.0035", "Validation ROC-AUC": "Please tell us"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "http://arxiv.org/abs/2006.05806", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/PaddlePaddle/PGL/tree/master/examples/GaAN"}], "metrics": {"Number of params": "Please tell us", "Test ROC-AUC": "0.7803 ± 0.0073", "Validation ROC-AUC": "Please tell us"}, "model_links": [], "model_name": "GaAN", "paper_date": "2020-05-26", "paper_title": "", "paper_url": "https://arxiv.org/abs/1803.07294", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/proteins"}], "metrics": {"Number of params": "193136", "Test ROC-AUC": "0.7768 ± 0.0020", "Validation ROC-AUC": "0.8334 ± 0.0013"}, "model_links": [], "model_name": "GraphSAGE", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/proteins"}], "metrics": {"Number of params": "96880", "Test ROC-AUC": "0.7251 ± 0.0035", "Validation ROC-AUC": "0.7921 ± 0.0018"}, "model_links": [], "model_name": "GCN", "paper_date": "2020-07-17", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/proteins"}], "metrics": {"Number of params": "96880", "Test ROC-AUC": "0.7204 ± 0.0048", "Validation ROC-AUC": "0.7706 ± 0.0014"}, "model_links": [], "model_name": "MLP", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/2005.00687", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/proteins"}], "metrics": {"Number of params": "17094000", "Test ROC-AUC": "0.6881 ± 0.0065", "Validation ROC-AUC": "0.7007 ± 0.0053"}, "model_links": [], "model_name": "Node2vec", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1607.00653", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbn-arxiv", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test Accuracy", "Validation Accuracy", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/tree/master/examples/ogb_eff/ogbn_arxiv_dgl"}], "metrics": {"Number of params": "2098256", "Test Accuracy": "0.7426 ± 0.0017", "Validation Accuracy": "0.7497 ± 0.0008"}, "model_links": [], "model_name": "RevGAT+N.Adj+LabelReuse+SelfKD", "paper_date": "2021-06-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2106.07476", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/PyG-OGB-Tricks/tree/main/DGL-ogbn-arxiv"}], "metrics": {"Number of params": "1700432", "Test Accuracy": "0.7420 ± 0.0004", "Validation Accuracy": "0.7482 ± 0.0015"}, "model_links": [], "model_name": "GAT-node2vec + BoT + self-KD", "paper_date": "2021-06-28", "paper_title": "", "paper_url": "https://arxiv.org/abs/2105.08330", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ShunliRen/dgl/tree/master/examples/pytorch/ogb/ogbn-arxiv"}], "metrics": {"Number of params": "1441580", "Test Accuracy": "0.7416 ± 0.0008", "Validation Accuracy": "0.7514 ± 0.0004"}, "model_links": [], "model_name": "GAT+label reuse+self KD", "paper_date": "2020-12-15", "paper_title": "", "paper_url": "https://arxiv.org/abs/1710.10903", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/PyG-OGB-Tricks/tree/main/DGL-ogbn-arxiv"}], "metrics": {"Number of params": "1700432", "Test Accuracy": "0.7405 ± 0.0004", "Validation Accuracy": "0.7482 ± 0.0015"}, "model_links": [], "model_name": "GAT-node2vec + BoT", "paper_date": "2021-06-28", "paper_title": "", "paper_url": "https://arxiv.org/abs/2105.08330", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/tree/master/examples/ogb_eff/ogbn_arxiv_dgl"}], "metrics": {"Number of params": "2098256", "Test Accuracy": "0.7402 ± 0.0018", "Validation Accuracy": "0.7501 ± 0.0010"}, "model_links": [], "model_name": "RevGAT+NormAdj+LabelReuse", "paper_date": "2021-06-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2106.07476", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/mengyangniu/dgl/tree/master/examples/pytorch/ogb/ogbn-arxiv"}], "metrics": {"Number of params": "1441580", "Test Accuracy": "0.7399 ± 0.0012", "Validation Accuracy": "0.7513 ± 0.0009"}, "model_links": [], "model_name": "GAT+label+reuse+topo loss", "paper_date": "2020-12-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/1710.10903", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/skepsun/adaptive_graph_diffusion_networks_with_hop-wise_attention"}], "metrics": {"Number of params": "1508555", "Test Accuracy": "0.7398 ± 0.0009", "Validation Accuracy": "0.7519 ± 0.0009"}, "model_links": [], "model_name": "AGDN (GAT-HA+3_heads+labels)", "paper_date": "2021-01-03", "paper_title": "", "paper_url": "https://arxiv.org/abs/2012.15024", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/PaddlePaddle/PGL/tree/main/ogb_examples/nodeproppred/ogbn-arxiv/unimp_appnp_vnode_smooth"}], "metrics": {"Number of params": "687377", "Test Accuracy": "0.7397 ± 0.0015", "Validation Accuracy": "0.7506 ± 0.0009"}, "model_links": [], "model_name": "UniMP_v2", "paper_date": "2020-11-24", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2009.03509.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Espylapiza/dgl/tree/master/examples/pytorch/ogb/ogbn-arxiv"}], "metrics": {"Number of params": "1441580", "Test Accuracy": "0.7395 ± 0.0012", "Validation Accuracy": "0.7519 ± 0.0008"}, "model_links": [], "model_name": "GAT(norm.adj.)+label reuse+C&S", "paper_date": "2020-11-24", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Espylapiza/dgl/tree/master/examples/pytorch/ogb/ogbn-arxiv"}], "metrics": {"Number of params": "1441580", "Test Accuracy": "0.7391 ± 0.0012", "Validation Accuracy": "0.7516 ± 0.0008"}, "model_links": [], "model_name": "GAT+norm. adj.+label reuse", "paper_date": "2020-11-11", "paper_title": "", "paper_url": "https://arxiv.org/abs/2103.13355", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/CorrectAndSmooth"}], "metrics": {"Number of params": "1567000", "Test Accuracy": "0.7386 ± 0.0014", "Validation Accuracy": "0.7484 ± 0.0007"}, "model_links": [], "model_name": "GAT + C&S", "paper_date": "2020-10-27", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/PaddlePaddle/PGL/tree/main/ogb_examples/nodeproppred/unimp"}], "metrics": {"Number of params": "1162515", "Test Accuracy": "0.7379 ± 0.0014", "Validation Accuracy": "0.7475 ± 0.0008"}, "model_links": [], "model_name": "UniMP_large", "paper_date": "2020-09-25", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2009.03509.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/skepsun/adaptive_graph_diffusion_networks_with_hop-wise_attention"}], "metrics": {"Number of params": "1447115", "Test Accuracy": "0.7375 ± 0.0021", "Validation Accuracy": "0.7483 ± 0.0009"}, "model_links": [], "model_name": "AGDN (GAT-HA+3_heads)", "paper_date": "2021-01-03", "paper_title": "", "paper_url": "https://arxiv.org/abs/2012.15024", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "1628440", "Test Accuracy": "0.7371 ± 0.0013", "Validation Accuracy": "0.7496 ± 0.0010"}, "model_links": [], "model_name": "GAT+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Espylapiza/dgl/tree/master/examples/pytorch/ogb/ogbn-arxiv"}], "metrics": {"Number of params": "1441580", "Test Accuracy": "0.7366 ± 0.0011", "Validation Accuracy": "0.7508 ± 0.0009"}, "model_links": [], "model_name": "GAT+norm. adj.+labels", "paper_date": "2020-10-29", "paper_title": "", "paper_url": "https://arxiv.org/abs/2103.13355", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/ogbn-arxiv"}], "metrics": {"Number of params": "1628440", "Test Accuracy": "0.7365 ± 0.0011", "Validation Accuracy": "0.7504 ± 0.0006"}, "model_links": [], "model_name": "GAT+norm.adj.+labels", "paper_date": "2020-09-17", "paper_title": "", "paper_url": "https://arxiv.org/abs/2103.13355", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/GCN_res-CS-v2"}], "metrics": {"Number of params": "155824", "Test Accuracy": "0.7313 ± 0.0017", "Validation Accuracy": "0.7445 ± 0.0011"}, "model_links": [], "model_name": "GCN_res + C&S_v2", "paper_date": "2021-03-26", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/CorrectAndSmooth"}], "metrics": {"Number of params": "175656", "Test Accuracy": "0.7312 ± 0.0012", "Validation Accuracy": "0.7391 ± 0.0015"}, "model_links": [], "model_name": "MLP + C&S", "paper_date": "2020-10-27", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/PaddlePaddle/PGL/tree/main/ogb_examples/nodeproppred/unimp"}], "metrics": {"Number of params": "473489", "Test Accuracy": "0.7311 ± 0.0020", "Validation Accuracy": "0.7450 ± 0.0005"}, "model_links": [], "model_name": "UniMP", "paper_date": "2020-09-08", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2009.03509.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/ogbn-arxiv"}], "metrics": {"Number of params": "238632", "Test Accuracy": "0.7306 ± 0.0024", "Validation Accuracy": "0.7442 ± 0.0012"}, "model_links": [], "model_name": "GCN+linear+labels", "paper_date": "2020-09-05", "paper_title": "", "paper_url": "https://arxiv.org/abs/2103.13355", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/PyG-GCN_res-CS"}], "metrics": {"Number of params": "155824", "Test Accuracy": "0.7297 ± 0.0022", "Validation Accuracy": "0.7423 ± 0.0014"}, "model_links": [], "model_name": "GCN_res + C&S", "paper_date": "2021-03-24", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/ogb_baselines"}], "metrics": {"Number of params": "122542", "Test Accuracy": "0.7286 ± 0.0016", "Validation Accuracy": "0.7382 ± 0.0007"}, "model_links": [], "model_name": "GCN+residual+6 layers", "paper_date": "2020-10-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/ogb_baselines"}], "metrics": {"Number of params": "21885098", "Test Accuracy": "0.7278 ± 0.0013", "Validation Accuracy": "0.7414 ± 0.0008"}, "model_links": [], "model_name": "GCN+residual+node2vec", "paper_date": "2020-10-03", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/GCN_res-FLAG"}], "metrics": {"Number of params": "155824", "Test Accuracy": "0.7276 ± 0.0024", "Validation Accuracy": "0.7389 ± 0.0012"}, "model_links": [], "model_name": "GCN_res + 8 layers + FLAG", "paper_date": "2021-02-23", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/chennnM/GCNII/tree/master/PyG/ogbn-arxiv"}], "metrics": {"Number of params": "2148648", "Test Accuracy": "0.7274 ± 0.0016", "Validation Accuracy": "Please tell us"}, "model_links": [], "model_name": "GCNII", "paper_date": "2020-07-07", "paper_title": "", "paper_url": "https://arxiv.org/abs/2007.02133", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/ogbn_arxiv_GCN_res"}], "metrics": {"Number of params": "155824", "Test Accuracy": "0.7262 ± 0.0037", "Validation Accuracy": "0.7369 ± 0.0021"}, "model_links": [], "model_name": "GCN_res + 8 layers", "paper_date": "2021-02-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/CorrectAndSmooth"}], "metrics": {"Number of params": "15400", "Test Accuracy": "0.7222 ± 0.0002", "Validation Accuracy": "0.7368 ± 0.0004"}, "model_links": [], "model_name": "Linear + C&S", "paper_date": "2020-10-27", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "218664", "Test Accuracy": "0.7219 ± 0.0021", "Validation Accuracy": "0.7349 ± 0.0009"}, "model_links": [], "model_name": "GraphSAGE+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/EtoDemerzel0427/ogbn-arxiv-baselines/blob/master/JKNet.py"}], "metrics": {"Number of params": "89000", "Test Accuracy": "0.7219 ± 0.0021", "Validation Accuracy": "0.7335 ± 0.0007"}, "model_links": [], "model_name": "JKNet (GCN-based)", "paper_date": "2020-08-26", "paper_title": "", "paper_url": "https://arxiv.org/pdf/1806.03536.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/shyam196/egc"}], "metrics": {"Number of params": "100648", "Test Accuracy": "0.7219 ± 0.0016", "Validation Accuracy": "0.7338 ± 0.0022"}, "model_links": [], "model_name": "EGC-S (100k)", "paper_date": "2021-04-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.01481", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "491176", "Test Accuracy": "0.7214 ± 0.0019", "Validation Accuracy": "0.7311 ± 0.0009"}, "model_links": [], "model_name": "DeeperGCN+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/mengliu1998/DeeperGNN"}], "metrics": {"Number of params": "43857", "Test Accuracy": "0.7209 ± 0.0025", "Validation Accuracy": "0.7290 ± 0.0011"}, "model_links": [], "model_name": "DAGNN", "paper_date": "2020-08-19", "paper_title": "", "paper_url": "https://arxiv.org/abs/2007.09296", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "142888", "Test Accuracy": "0.7204 ± 0.0020", "Validation Accuracy": "0.7330 ± 0.0010"}, "model_links": [], "model_name": "GCN+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/PaddlePaddle/PGL/tree/master/ogb_examples/nodeproppred/ogbn-arxiv"}], "metrics": {"Number of params": "1471506", "Test Accuracy": "0.7197 ± 0.0024", "Validation Accuracy": "Please tell us"}, "model_links": [], "model_name": "GaAN", "paper_date": "2020-06-16", "paper_title": "", "paper_url": "https://arxiv.org/abs/1803.07294", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/shyam196/egc"}], "metrics": {"Number of params": "99464", "Test Accuracy": "0.7196 ± 0.0023", "Validation Accuracy": "0.7334 ± 0.0013"}, "model_links": [], "model_name": "EGC-M (100k)", "paper_date": "2021-04-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.01481", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/sign"}], "metrics": {"Number of params": "3566128", "Test Accuracy": "0.7195 ± 0.0011", "Validation Accuracy": "0.7323 ± 0.0006"}, "model_links": [], "model_name": "SIGN", "paper_date": "2020-11-05", "paper_title": "", "paper_url": "https://arxiv.org/abs/2004.11198", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/tree/master/examples/ogb"}], "metrics": {"Number of params": "491176", "Test Accuracy": "0.7192 ± 0.0016", "Validation Accuracy": "0.7262 ± 0.0014"}, "model_links": [], "model_name": "DeeperGCN", "paper_date": "2020-06-16", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.07739", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/arxiv"}], "metrics": {"Number of params": "142888", "Test Accuracy": "0.7174 ± 0.0029", "Validation Accuracy": "0.7300 ± 0.0017"}, "model_links": [], "model_name": "GCN", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/arxiv"}], "metrics": {"Number of params": "218664", "Test Accuracy": "0.7149 ± 0.0027", "Validation Accuracy": "0.7277 ± 0.0016"}, "model_links": [], "model_name": "GraphSAGE", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/CorrectAndSmooth"}], "metrics": {"Number of params": "5160", "Test Accuracy": "0.7126 ± 0.0001", "Validation Accuracy": "0.7300 ± 0.0001"}, "model_links": [], "model_name": "Plain Linear + C&S", "paper_date": "2020-10-27", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.13993", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/cornell-zhang/GraphZoom/tree/master/ogb/ogbn-arxiv"}], "metrics": {"Number of params": "8963624", "Test Accuracy": "0.7118 ± 0.0018", "Validation Accuracy": "0.7220 ± 0.0007"}, "model_links": [], "model_name": "GraphZoom (Node2vec)", "paper_date": "2020-07-02", "paper_title": "", "paper_url": "https://openreview.net/forum?id=r1lGO0EKDH", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/arxiv"}], "metrics": {"Number of params": "21818792", "Test Accuracy": "0.7007 ± 0.0013", "Validation Accuracy": "0.7129 ± 0.0013"}, "model_links": [], "model_name": "Node2vec", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1607.00653", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/CorrectAndSmoothOGB"}], "metrics": {"Number of params": "0", "Test Accuracy": "0.6832 ± 0.0000", "Validation Accuracy": "0.7014 ± 0.0000"}, "model_links": [], "model_name": "Label Propagation", "paper_date": "2020-10-02", "paper_title": "", "paper_url": "http://pages.cs.wisc.edu/~jerryzhu/pub/ssl_survey.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/cf020031308/LinkDist/blob/master/ogbn.py"}], "metrics": {"Number of params": "120912", "Test Accuracy": "0.5638 ± 0.0016", "Validation Accuracy": "0.5807 ± 0.0011"}, "model_links": [], "model_name": "CoLinkDistMLP", "paper_date": "2021-06-17", "paper_title": "", "paper_url": "https://arxiv.org/abs/2106.08541", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "110120", "Test Accuracy": "0.5602 ± 0.0019", "Validation Accuracy": "0.5817 ± 0.0011"}, "model_links": [], "model_name": "MLP+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/arxiv"}], "metrics": {"Number of params": "110120", "Test Accuracy": "0.5550 ± 0.0023", "Validation Accuracy": "0.5765 ± 0.0012"}, "model_links": [], "model_name": "MLP", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/2005.00687", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbn-papers100M", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test Accuracy", "Validation Accuracy", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/skepsun/SAGN_with_SLE"}], "metrics": {"Number of params": "8556888", "Test Accuracy": "0.6800 ± 0.0015", "Validation Accuracy": "0.7131 ± 0.0010"}, "model_links": [], "model_name": "SAGN+SLE", "paper_date": "2021-04-19", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.09376", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/nvidia-china-sae/WholeGraph"}], "metrics": {"Number of params": "883378", "Test Accuracy": "0.6736 ± 0.0010", "Validation Accuracy": "0.7172 ± 0.0005"}, "model_links": [], "model_name": "TransformerConv", "paper_date": "2021-03-04", "paper_title": "", "paper_url": "https://arxiv.org/abs/2009.03509", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/mengyangniu/ogbn-papers100m-sage"}], "metrics": {"Number of params": "5755172", "Test Accuracy": "0.6706 ± 0.0017", "Validation Accuracy": "0.7032 ± 0.0011"}, "model_links": [], "model_name": "GraphSAGE_res_incep", "paper_date": "2021-02-28", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/skepsun/SAGN_with_SLE"}], "metrics": {"Number of params": "6098092", "Test Accuracy": "0.6675 ± 0.0084", "Validation Accuracy": "0.7034 ± 0.0099"}, "model_links": [], "model_name": "SAGN", "paper_date": "2021-04-19", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.09376", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/twitter-research/sign"}], "metrics": {"Number of params": "7180460", "Test Accuracy": "0.6606 ± 0.0019", "Validation Accuracy": "0.6984 ± 0.0006"}, "model_links": [], "model_name": "SIGN-XL", "paper_date": "2020-11-04", "paper_title": "", "paper_url": "https://arxiv.org/abs/2004.11198", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/twitter-research/sign"}], "metrics": {"Number of params": "1008812", "Test Accuracy": "0.6568 ± 0.0006", "Validation Accuracy": "0.6932 ± 0.0006"}, "model_links": [], "model_name": "SIGN", "paper_date": "2020-11-04", "paper_title": "", "paper_url": "https://arxiv.org/abs/2004.11198", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/papers100M"}], "metrics": {"Number of params": "144044", "Test Accuracy": "0.6329 ± 0.0019", "Validation Accuracy": "0.6648 ± 0.0020"}, "model_links": [], "model_name": "SGC", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/1902.07153", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/papers100M"}], "metrics": {"Number of params": "14215818412", "Test Accuracy": "0.5560 ± 0.0023", "Validation Accuracy": "0.5807 ± 0.0028"}, "model_links": [], "model_name": "Node2vec", "paper_date": "2020-06-26", "paper_title": "", "paper_url": "https://arxiv.org/abs/1607.00653", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/papers100M"}], "metrics": {"Number of params": "144044", "Test Accuracy": "0.4724 ± 0.0031", "Validation Accuracy": "0.4960 ± 0.0029"}, "model_links": [], "model_name": "MLP", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/2005.00687", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbn-mag", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test Accuracy", "Validation Accuracy", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/facebookresearch/NARS"}], "metrics": {"Number of params": "4130149", "Test Accuracy": "0.5240 ± 0.0016", "Validation Accuracy": "0.5372 ± 0.0009"}, "model_links": [], "model_name": "NARS", "paper_date": "2021-02-18", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2011.09679.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/yule-BUAA/R-HGNN.git"}], "metrics": {"Number of params": "5638053", "Test Accuracy": "0.5204 ± 0.0026", "Validation Accuracy": "0.5361 ± 0.0022"}, "model_links": [], "model_name": "R-HGNN", "paper_date": "2021-05-24", "paper_title": "", "paper_url": "https://arxiv.org/abs/2105.11122", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/PyG-ogbn-mag/tree/main/rgsn%2Bmetapath2vec"}], "metrics": {"Number of params": "309777252", "Test Accuracy": "0.5109 ± 0.0038", "Validation Accuracy": "0.5295 ± 0.0042"}, "model_links": [], "model_name": "R-GSN + metapath2vec", "paper_date": "2021-05-23", "paper_title": "", "paper_url": "https://arxiv.org/abs/2103.07877", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/yule-BUAA/HGConv.git"}], "metrics": {"Number of params": "2850405", "Test Accuracy": "0.5045 ± 0.0017", "Validation Accuracy": "0.5300 ± 0.0018"}, "model_links": [], "model_name": "HGConv", "paper_date": "2021-02-14", "paper_title": "", "paper_url": "https://arxiv.org/abs/2012.14722", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/xjtuwxliang/R-GSN/tree/main"}], "metrics": {"Number of params": "154373028", "Test Accuracy": "0.5032 ± 0.0037", "Validation Accuracy": "0.5182 ± 0.0041"}, "model_links": [], "model_name": "R-GSN", "paper_date": "2021-01-29", "paper_title": "", "paper_url": "https://arxiv.org/pdf/1703.06103.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lingfanyu/pyHGT/tree/master/ogbn-mag"}], "metrics": {"Number of params": "26877657", "Test Accuracy": "0.4982 ± 0.0013", "Validation Accuracy": "0.5124 ± 0.0046"}, "model_links": [], "model_name": "HGT (TransE embs)", "paper_date": "2021-02-17", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2003.01332.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/ytchx1999/PyG-ogbn-mag/tree/main/saint%2Bmetapath2vec"}], "metrics": {"Number of params": "309764724", "Test Accuracy": "0.4966 ± 0.0022", "Validation Accuracy": "0.5066 ± 0.0017"}, "model_links": [], "model_name": "GraphSAINT + metapath2vec", "paper_date": "2021-04-09", "paper_title": "", "paper_url": "https://arxiv.org/abs/1907.04931", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/acbull/pyHGT/tree/master/ogbn-mag"}], "metrics": {"Number of params": "21173389", "Test Accuracy": "0.4927 ± 0.0061", "Validation Accuracy": "0.4989 ± 0.0047"}, "model_links": [], "model_name": "HGT (LADIES Sample)", "paper_date": "2021-01-26", "paper_title": "", "paper_url": "https://arxiv.org/abs/2003.01332", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/mag"}], "metrics": {"Number of params": "154366772", "Test Accuracy": "0.4751 ± 0.0022", "Validation Accuracy": "0.4837 ± 0.0026"}, "model_links": [], "model_name": "GraphSAINT (R-GCN aggr)", "paper_date": "2020-06-26", "paper_title": "", "paper_url": "https://arxiv.org/abs/1907.04931", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "154366772", "Test Accuracy": "0.4737 ± 0.0048", "Validation Accuracy": "0.4835 ± 0.0036"}, "model_links": [], "model_name": "R-GCN+FLAG", "paper_date": "2020-10-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/mag"}], "metrics": {"Number of params": "154366772", "Test Accuracy": "0.4678 ± 0.0067", "Validation Accuracy": "0.4761 ± 0.0068"}, "model_links": [], "model_name": "Neighbor<PERSON><PERSON>ling (R-GCN aggr)", "paper_date": "2020-06-26", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/sign"}], "metrics": {"Number of params": "3724645", "Test Accuracy": "0.4046 ± 0.0012", "Validation Accuracy": "0.4068 ± 0.0010"}, "model_links": [], "model_name": "SIGN", "paper_date": "2020-11-05", "paper_title": "", "paper_url": "https://arxiv.org/abs/2004.11198", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/mag"}], "metrics": {"Number of params": "154366772", "Test Accuracy": "0.3977 ± 0.0046", "Validation Accuracy": "0.4084 ± 0.0041"}, "model_links": [], "model_name": "Full-batch R-GCN", "paper_date": "2020-06-26", "paper_title": "", "paper_url": "https://arxiv.org/abs/1703.06103", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/mag"}], "metrics": {"Number of params": "154366772", "Test Accuracy": "0.3732 ± 0.0037", "Validation Accuracy": "0.3840 ± 0.0031"}, "model_links": [], "model_name": "ClusterGCN (R-GCN aggr)", "paper_date": "2020-06-26", "paper_title": "", "paper_url": "https://arxiv.org/abs/1905.07953", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/mag"}], "metrics": {"Number of params": "94479069", "Test Accuracy": "0.3544 ± 0.0036", "Validation Accuracy": "0.3506 ± 0.0017"}, "model_links": [], "model_name": "MetaPath2vec", "paper_date": "2020-06-26", "paper_title": "", "paper_url": "https://ericdongyx.github.io/papers/KDD17-dong-chawla-swami-metapath2vec.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/cf020031308/LinkDist/blob/master/ogbn.py"}], "metrics": {"Number of params": "278202", "Test Accuracy": "0.2761 ± 0.0018", "Validation Accuracy": "0.2646 ± 0.0013"}, "model_links": [], "model_name": "CoLinkDistMLP", "paper_date": "2021-06-17", "paper_title": "", "paper_url": "https://arxiv.org/abs/2106.08541", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/nodeproppred/mag"}], "metrics": {"Number of params": "188509", "Test Accuracy": "0.2692 ± 0.0026", "Validation Accuracy": "0.2626 ± 0.0016"}, "model_links": [], "model_name": "MLP", "paper_date": "2020-06-26", "paper_title": "", "paper_url": "https://arxiv.org/abs/2005.00687", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": {"title": "Node Property Prediction LeaderBoard", "url": "https://ogb.stanford.edu/docs/leader_nodeprop/"}, "subtasks": [], "synonyms": [], "task": "Node Property Prediction"}, {"categories": [], "datasets": [{"dataset": "ogbl-ppa", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test Hits@100", "Validation Hits@100", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "709122", "Test Hits@100": "0.4880 ± 0.0316", "Validation Hits@100": "0.5125 ± 0.0252"}, "model_links": [], "model_name": "SEAL", "paper_date": "2020-10-14", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2010.16103.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "0", "Test Hits@100": "0.3245 ± 0.0000", "Validation Hits@100": "0.3268 ± 0.0000"}, "model_links": [], "model_name": "<PERSON><PERSON>", "paper_date": "2021-02-12", "paper_title": "", "paper_url": "https://citeseerx.ist.psu.edu/viewdoc/download?doi=**********.1370&rep=rep1&type=pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/ppa"}], "metrics": {"Number of params": "147662849", "Test Hits@100": "0.3229 ± 0.0094", "Validation Hits@100": "0.3228 ± 0.0428"}, "model_links": [], "model_name": "Matrix Factorization", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/2005.00687", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/deepwalk"}], "metrics": {"Number of params": "150138741", "Test Hits@100": "0.2888 ± 0.0153", "Validation Hits@100": "Please tell us"}, "model_links": [], "model_name": "DeepWalk", "paper_date": "2020-07-23", "paper_title": "", "paper_url": "https://arxiv.org/pdf/1403.6652.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "0", "Test Hits@100": "0.2765 ± 0.0000", "Validation Hits@100": "0.2823 ± 0.0000"}, "model_links": [], "model_name": "Common Neighbor", "paper_date": "2021-02-12", "paper_title": "", "paper_url": "http://www.eecs.harvard.edu/~michaelm/CS222/linkpred.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/deepwalk"}], "metrics": {"Number of params": "150138741", "Test Hits@100": "0.2302 ± 0.0163", "Validation Hits@100": "Please tell us"}, "model_links": [], "model_name": "DeepWalk", "paper_date": "2020-06-30", "paper_title": "", "paper_url": "https://arxiv.org/pdf/1403.6652.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/ppa"}], "metrics": {"Number of params": "73878913", "Test Hits@100": "0.2226 ± 0.0083", "Validation Hits@100": "0.2253 ± 0.0088"}, "model_links": [], "model_name": "Node2vec", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1607.00653", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/ppa"}], "metrics": {"Number of params": "278529", "Test Hits@100": "0.1867 ± 0.0132", "Validation Hits@100": "0.1845 ± 0.0140"}, "model_links": [], "model_name": "GCN", "paper_date": "2020-06-25", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/ppa"}], "metrics": {"Number of params": "424449", "Test Hits@100": "0.1655 ± 0.0240", "Validation Hits@100": "0.1724 ± 0.0264"}, "model_links": [], "model_name": "GraphSAGE", "paper_date": "2020-06-25", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbl-collab", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test Hits@50", "Validation Hits@50", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "501570", "Test Hits@50": "0.6474 ± 0.0043", "Validation Hits@50": "0.6495 ± 0.0043"}, "model_links": [], "model_name": "SEAL-nofeat (val as input)", "paper_date": "2021-06-16", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2010.16103.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "0", "Test Hits@50": "0.6417 ± 0.0000", "Validation Hits@50": "0.6349 ± 0.0000"}, "model_links": [], "model_name": "<PERSON><PERSON>", "paper_date": "2021-02-12", "paper_title": "", "paper_url": "https://citeseerx.ist.psu.edu/viewdoc/download?doi=**********.1370&rep=rep1&type=pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "0", "Test Hits@50": "0.6137 ± 0.0000", "Validation Hits@50": "0.6036 ± 0.0000"}, "model_links": [], "model_name": "Common Neighbor", "paper_date": "2021-02-12", "paper_title": "", "paper_url": "http://www.eecs.harvard.edu/~michaelm/CS222/linkpred.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "501570", "Test Hits@50": "0.5471 ± 0.0049", "Validation Hits@50": "0.6495 ± 0.0043"}, "model_links": [], "model_name": "SEAL-nofeat", "paper_date": "2021-06-16", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2010.16103.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/collab"}], "metrics": {"Number of params": "460289", "Test Hits@50": "0.5463 ± 0.0112", "Validation Hits@50": "0.5688 ± 0.0077"}, "model_links": [], "model_name": "GraphSAGE  (val as input)", "paper_date": "2020-10-19", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/tree/master/examples/ogb"}], "metrics": {"Number of params": "117383", "Test Hits@50": "0.5273 ± 0.0047", "Validation Hits@50": "0.6187 ± 0.0045"}, "model_links": [], "model_name": "DeeperGCN", "paper_date": "2020-10-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.07739", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/omri1348/LRGA/tree/master/ogb/examples/linkproppred"}], "metrics": {"Number of params": "1069489", "Test Hits@50": "0.5221 ± 0.0072", "Validation Hits@50": "0.6088 ± 0.0059"}, "model_links": [], "model_name": "LRGA + GCN", "paper_date": "2020-08-05", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.07846", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/deepwalk"}], "metrics": {"Number of params": "61390187", "Test Hits@50": "0.5037 ± 0.0034", "Validation Hits@50": "Please tell us"}, "model_links": [], "model_name": "DeepWalk", "paper_date": "2020-06-30", "paper_title": "", "paper_url": "https://arxiv.org/pdf/1403.6652.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/collab"}], "metrics": {"Number of params": "30322945", "Test Hits@50": "0.4888 ± 0.0054", "Validation Hits@50": "0.5703 ± 0.0052"}, "model_links": [], "model_name": "Node2vec", "paper_date": "2020-06-22", "paper_title": "", "paper_url": "https://arxiv.org/abs/1607.00653", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/collab"}], "metrics": {"Number of params": "460289", "Test Hits@50": "0.4810 ± 0.0081", "Validation Hits@50": "0.5688 ± 0.0077"}, "model_links": [], "model_name": "GraphSAGE", "paper_date": "2020-06-24", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/collab"}], "metrics": {"Number of params": "296449", "Test Hits@50": "0.4714 ± 0.0145", "Validation Hits@50": "0.5263 ± 0.0115"}, "model_links": [], "model_name": "GCN (val as input)", "paper_date": "2020-10-19", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/collab"}], "metrics": {"Number of params": "296449", "Test Hits@50": "0.4475 ± 0.0107", "Validation Hits@50": "0.5263 ± 0.0115"}, "model_links": [], "model_name": "GCN", "paper_date": "2020-06-24", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/collab"}], "metrics": {"Number of params": "60514049", "Test Hits@50": "0.3886 ± 0.0029", "Validation Hits@50": "0.4896 ± 0.0029"}, "model_links": [], "model_name": "Matrix Factorization", "paper_date": "2020-06-22", "paper_title": "", "paper_url": "https://arxiv.org/abs/2005.00687", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbl-ddi", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test Hits@20", "Validation Hits@20", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/lbn187/DLGNN"}], "metrics": {"Number of params": "3760134", "Test Hits@20": "0.8239 ± 0.0437", "Validation Hits@20": "0.8206 ± 0.0298"}, "model_links": [], "model_name": "GraphSAGE+anchor distance", "paper_date": "2021-05-20", "paper_title": "", "paper_url": "https://www.dropbox.com/s/is3f4dfvtvnis7w/DEGNN_linkPrediction.pdf?dl=0", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/JeffJeffy/CS224W-OGB-DEA-JK"}], "metrics": {"Number of params": "1763329", "Test Hits@20": "0.7672 ± 0.0265", "Validation Hits@20": "0.6713 ± 0.0071"}, "model_links": [], "model_name": "DEA + JKNet", "paper_date": "2021-03-21", "paper_title": "", "paper_url": "https://github.com/JeffJeffy/CS224W-OGB-DEA-JK/blob/main/CS224w_final_report.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/chuanqichen/cs224w"}], "metrics": {"Number of params": "10235281", "Test Hits@20": "0.7385 ± 0.0871", "Validation Hits@20": "0.7225 ± 0.0047"}, "model_links": [], "model_name": "LRGA+GCN(Node2Vec+Augment)", "paper_date": "2021-03-21", "paper_title": "", "paper_url": "https://github.com/chuanqichen/cs224w/blob/main/the_study_of_drug_drug_interaction_learning_through_various_graph_learning_methods.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/cf020031308/mad-learning/blob/master/ogbl-ddi.py"}], "metrics": {"Number of params": "1228897", "Test Hits@20": "0.6781 ± 0.0294", "Validation Hits@20": "0.7010 ± 0.0082"}, "model_links": [], "model_name": "MAD Learning", "paper_date": "2021-02-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/2102.05246", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/omri1348/LRGA/tree/master/ogb/examples/linkproppred"}], "metrics": {"Number of params": "1576081", "Test Hits@20": "0.6230 ± 0.0912", "Validation Hits@20": "0.6675 ± 0.0058"}, "model_links": [], "model_name": "LRGA + GCN", "paper_date": "2020-08-05", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.07846", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Chillee/ogb_ddi"}], "metrics": {"Number of params": "1421571", "Test Hits@20": "0.6056 ± 0.0869", "Validation Hits@20": "0.6776 ± 0.0095"}, "model_links": [], "model_name": "GCN+JKNet", "paper_date": "2020-09-15", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/ddi"}], "metrics": {"Number of params": "1421057", "Test Hits@20": "0.5390 ± 0.0474", "Validation Hits@20": "0.6262 ± 0.0037"}, "model_links": [], "model_name": "GraphSAGE", "paper_date": "2020-06-25", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/ddi"}], "metrics": {"Number of params": "1289985", "Test Hits@20": "0.3707 ± 0.0507", "Validation Hits@20": "0.5550 ± 0.0208"}, "model_links": [], "model_name": "GCN", "paper_date": "2020-06-25", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "531138", "Test Hits@20": "0.3056 ± 0.0386", "Validation Hits@20": "0.2849 ± 0.0269"}, "model_links": [], "model_name": "SEAL", "paper_date": "2020-10-14", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2010.16103.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/deepwalk"}], "metrics": {"Number of params": "11543913", "Test Hits@20": "0.2642 ± 0.0610", "Validation Hits@20": "Please tell us"}, "model_links": [], "model_name": "DeepWalk", "paper_date": "2020-07-23", "paper_title": "", "paper_url": "https://arxiv.org/pdf/1403.6652.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/ddi"}], "metrics": {"Number of params": "645249", "Test Hits@20": "0.2326 ± 0.0209", "Validation Hits@20": "0.3292 ± 0.0121"}, "model_links": [], "model_name": "Node2vec", "paper_date": "2020-06-22", "paper_title": "", "paper_url": "https://arxiv.org/abs/1607.00653", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmlc/dgl/tree/master/examples/pytorch/ogb/deepwalk"}], "metrics": {"Number of params": "1543913", "Test Hits@20": "0.2246 ± 0.0290", "Validation Hits@20": "Please tell us"}, "model_links": [], "model_name": "DeepWalk", "paper_date": "2020-06-30", "paper_title": "", "paper_url": "https://arxiv.org/pdf/1403.6652.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "0", "Test Hits@20": "0.1861 ± 0.0000", "Validation Hits@20": "0.0966 ± 0.0000"}, "model_links": [], "model_name": "<PERSON><PERSON>", "paper_date": "2021-02-12", "paper_title": "", "paper_url": "https://citeseerx.ist.psu.edu/viewdoc/download?doi=**********.1370&rep=rep1&type=pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "0", "Test Hits@20": "0.1773 ± 0.0000", "Validation Hits@20": "0.0947 ± 0.0000"}, "model_links": [], "model_name": "Common Neighbor", "paper_date": "2021-02-12", "paper_title": "", "paper_url": "http://www.eecs.harvard.edu/~michaelm/CS222/linkpred.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/ddi"}], "metrics": {"Number of params": "1224193", "Test Hits@20": "0.1368 ± 0.0475", "Validation Hits@20": "0.3370 ± 0.0264"}, "model_links": [], "model_name": "Matrix Factorization", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/2005.00687", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbl-citation2", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test MRR", "Validation MRR", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "260802", "Test MRR": "0.8767 ± 0.0032", "Validation MRR": "0.8757 ± 0.0031"}, "model_links": [], "model_name": "SEAL", "paper_date": "2021-02-12", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2010.16103.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/citation2"}], "metrics": {"Number of params": "296449", "Test MRR": "0.8474 ± 0.0021", "Validation MRR": "0.8479 ± 0.0023"}, "model_links": [], "model_name": "Full-batch GCN", "paper_date": "2021-01-04", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/citation2"}], "metrics": {"Number of params": "460289", "Test MRR": "0.8260 ± 0.0036", "Validation MRR": "0.8263 ± 0.0033"}, "model_links": [], "model_name": "Full-batch GraphSAGE", "paper_date": "2021-01-04", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/citation2"}], "metrics": {"Number of params": "460289", "Test MRR": "0.8044 ± 0.0010", "Validation MRR": "0.8054 ± 0.0009"}, "model_links": [], "model_name": "NeighborSampling (SAGE aggr)", "paper_date": "2021-01-04", "paper_title": "", "paper_url": "https://arxiv.org/abs/1706.02216", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/citation2"}], "metrics": {"Number of params": "296449", "Test MRR": "0.8004 ± 0.0025", "Validation MRR": "0.7994 ± 0.0025"}, "model_links": [], "model_name": "ClusterGCN (GCN aggr)", "paper_date": "2021-01-04", "paper_title": "", "paper_url": "https://arxiv.org/abs/1905.07953", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/citation2"}], "metrics": {"Number of params": "296449", "Test MRR": "0.7985 ± 0.0040", "Validation MRR": "0.7975 ± 0.0039"}, "model_links": [], "model_name": "GraphSAINT (GCN aggr)", "paper_date": "2021-01-04", "paper_title": "", "paper_url": "https://arxiv.org/abs/1907.04931", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/citation2"}], "metrics": {"Number of params": "374911105", "Test MRR": "0.6141 ± 0.0011", "Validation MRR": "0.6124 ± 0.0011"}, "model_links": [], "model_name": "Node2vec", "paper_date": "2021-01-04", "paper_title": "", "paper_url": "https://arxiv.org/abs/1607.00653", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "0", "Test MRR": "0.5189 ± 0.0000", "Validation MRR": "0.5167 ± 0.0000"}, "model_links": [], "model_name": "<PERSON><PERSON>", "paper_date": "2021-02-12", "paper_title": "", "paper_url": "https://citeseerx.ist.psu.edu/viewdoc/download?doi=**********.1370&rep=rep1&type=pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/citation2"}], "metrics": {"Number of params": "281113505", "Test MRR": "0.5186 ± 0.0443", "Validation MRR": "0.5181 ± 0.0436"}, "model_links": [], "model_name": "Matrix Factorization", "paper_date": "2021-01-04", "paper_title": "", "paper_url": "https://arxiv.org/abs/2005.00687", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/facebookresearch/SEAL_OGB"}], "metrics": {"Number of params": "0", "Test MRR": "0.5147 ± 0.0000", "Validation MRR": "0.5119 ± 0.0000"}, "model_links": [], "model_name": "Common Neighbor", "paper_date": "2021-02-12", "paper_title": "", "paper_url": "http://www.eecs.harvard.edu/~michaelm/CS222/linkpred.pdf", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbl-wikikg2", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test MRR", "Validation MRR", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/AutoML-4Paradigm/AutoSF/tree/AutoSF-OGB"}], "metrics": {"Number of params": "500227800", "Test MRR": "0.5458 ± 0.0052", "Validation MRR": "0.5510 ± 0.0063"}, "model_links": [], "model_name": "AutoSF", "paper_date": "2021-04-02", "paper_title": "", "paper_url": "https://arxiv.org/pdf/1904.11682.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/alipay/KnowledgeGraphEmbeddingsViaPairedRelationVectors_PairRE"}], "metrics": {"Number of params": "500334800", "Test MRR": "0.5208 ± 0.0027", "Validation MRR": "0.5423 ± 0.0020"}, "model_links": [], "model_name": "PairRE (200dim)", "paper_date": "2021-01-28", "paper_title": "", "paper_url": "https://arxiv.org/abs/2011.03798", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/wikikg2"}], "metrics": {"Number of params": "1250435750", "Test MRR": "0.4332 ± 0.0025", "Validation MRR": "0.4353 ± 0.0028"}, "model_links": [], "model_name": "RotatE (250dim)", "paper_date": "2021-01-23", "paper_title": "", "paper_url": "https://arxiv.org/abs/1902.10197", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/wikikg2"}], "metrics": {"Number of params": "1250569500", "Test MRR": "0.4256 ± 0.0030", "Validation MRR": "0.4272 ± 0.0030"}, "model_links": [], "model_name": "TransE (500dim)", "paper_date": "2021-01-23", "paper_title": "", "paper_url": "https://papers.nips.cc/paper/5071-translating-embeddings-for-modeling-multi-relational-data.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/wikikg2"}], "metrics": {"Number of params": "1250569500", "Test MRR": "0.4027 ± 0.0027", "Validation MRR": "0.3759 ± 0.0016"}, "model_links": [], "model_name": "ComplEx (250dim)", "paper_date": "2021-01-23", "paper_title": "", "paper_url": "https://arxiv.org/abs/1606.06357", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/wikikg2"}], "metrics": {"Number of params": "250113900", "Test MRR": "0.3804 ± 0.0022", "Validation MRR": "0.3534 ± 0.0052"}, "model_links": [], "model_name": "ComplEx (50dim)", "paper_date": "2021-01-23", "paper_title": "", "paper_url": "https://arxiv.org/abs/1606.06357", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/wikikg2"}], "metrics": {"Number of params": "1250569500", "Test MRR": "0.3729 ± 0.0045", "Validation MRR": "0.3506 ± 0.0042"}, "model_links": [], "model_name": "DistMult (500dim)", "paper_date": "2021-01-23", "paper_title": "", "paper_url": "https://arxiv.org/abs/1412.6575", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/wikikg2"}], "metrics": {"Number of params": "250113900", "Test MRR": "0.3447 ± 0.0082", "Validation MRR": "0.3150 ± 0.0088"}, "model_links": [], "model_name": "DistMult (100dim)", "paper_date": "2021-01-23", "paper_title": "", "paper_url": "https://arxiv.org/abs/1412.6575", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/wikikg2"}], "metrics": {"Number of params": "250113900", "Test MRR": "0.2622 ± 0.0045", "Validation MRR": "0.2465 ± 0.0020"}, "model_links": [], "model_name": "TransE (100dim)", "paper_date": "2021-01-23", "paper_title": "", "paper_url": "https://papers.nips.cc/paper/5071-translating-embeddings-for-modeling-multi-relational-data.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/wikikg2"}], "metrics": {"Number of params": "250087150", "Test MRR": "0.2530 ± 0.0034", "Validation MRR": "0.2250 ± 0.0035"}, "model_links": [], "model_name": "RotatE (50dim)", "paper_date": "2021-01-23", "paper_title": "", "paper_url": "https://arxiv.org/abs/1902.10197", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbl-biokg", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test MRR", "Validation MRR", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/AutoML-4Paradigm/AutoSF/tree/AutoSF-OGB"}], "metrics": {"Number of params": "93824000", "Test MRR": "0.8309 ± 0.0008", "Validation MRR": "0.8317 ± 0.0007"}, "model_links": [], "model_name": "AutoSF", "paper_date": "2021-04-02", "paper_title": "", "paper_url": "https://arxiv.org/pdf/1904.11682.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/alipay/KnowledgeGraphEmbeddingsViaPairedRelationVectors_PairRE"}], "metrics": {"Number of params": "187750000", "Test MRR": "0.8164 ± 0.0005", "Validation MRR": "0.8172 ± 0.0005"}, "model_links": [], "model_name": "PairRE", "paper_date": "2020-11-09", "paper_title": "", "paper_url": "https://arxiv.org/abs/2011.03798", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/biokg"}], "metrics": {"Number of params": "187648000", "Test MRR": "0.8095 ± 0.0007", "Validation MRR": "0.8105 ± 0.0001"}, "model_links": [], "model_name": "ComplEx", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/1606.06357", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/biokg"}], "metrics": {"Number of params": "187648000", "Test MRR": "0.8043 ± 0.0003", "Validation MRR": "0.8055 ± 0.0003"}, "model_links": [], "model_name": "DistMult", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/1412.6575", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/biokg"}], "metrics": {"Number of params": "187597000", "Test MRR": "0.7989 ± 0.0004", "Validation MRR": "0.7997 ± 0.0002"}, "model_links": [], "model_name": "RotatE", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://arxiv.org/abs/1902.10197", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/linkproppred/biokg"}], "metrics": {"Number of params": "187648000", "Test MRR": "0.7452 ± 0.0004", "Validation MRR": "0.7456 ± 0.0003"}, "model_links": [], "model_name": "TransE", "paper_date": "2020-06-10", "paper_title": "", "paper_url": "https://papers.nips.cc/paper/5071-translating-embeddings-for-modeling-multi-relational-data.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": {"title": "Link Property Prediction LeaderBoard", "url": "https://ogb.stanford.edu/docs/leader_linkprop/"}, "subtasks": [], "synonyms": [], "task": "Link Property Prediction"}, {"categories": [], "datasets": [{"dataset": "ogbg-molhiv", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test ROC-AUC", "Validation ROC-AUC", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/PaddlePaddle/PaddleHelix/tree/dev/competition/ogbg_molhiv"}], "metrics": {"Number of params": "2425102", "Test ROC-AUC": "0.8232 ± 0.0047", "Validation ROC-AUC": "0.8331 ± 0.0054"}, "model_links": [], "model_name": "Neural FingerPrints", "paper_date": "2021-03-15", "paper_title": "", "paper_url": "https://github.com/PaddlePaddle/PaddleHelix/blob/dev/competition/ogbg_molhiv/Molecule_Representation_Learning_by_Leveraging_Chemical_Information.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/cyrusmaher/ogb-molecule-comp"}], "metrics": {"Number of params": "230000", "Test ROC-AUC": "0.8060 ± 0.0010", "Validation ROC-AUC": "0.8420 ± 0.0030"}, "model_links": [], "model_name": "MorganFP+Rand. Forest", "paper_date": "2020-09-21", "paper_title": "", "paper_url": "https://pubs.acs.org/doi/10.1021/ci100050t", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/dmmendo/OGBMolhiv_PWL"}], "metrics": {"Number of params": "4600000", "Test ROC-AUC": "0.8039 ± 0.0040", "Validation ROC-AUC": "0.8279 ± 0.0059"}, "model_links": [], "model_name": "P-WL", "paper_date": "2021-03-29", "paper_title": "", "paper_url": "http://proceedings.mlr.press/v97/rieck19a/rieck19a.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Saro00/DGN"}], "metrics": {"Number of params": "114065", "Test ROC-AUC": "0.7970 ± 0.0097", "Validation ROC-AUC": "0.8470 ± 0.0047"}, "model_links": [], "model_name": "DGN", "paper_date": "2020-11-20", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2010.02863.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "531976", "Test ROC-AUC": "0.7942 ± 0.0120", "Validation ROC-AUC": "0.8425 ± 0.0061"}, "model_links": [], "model_name": "DeeperGCN+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/bayer-science-for-a-better-life/phc-gnn"}], "metrics": {"Number of params": "110909", "Test ROC-AUC": "0.7934 ± 0.0116", "Validation ROC-AUC": "0.8217 ± 0.0089"}, "model_links": [], "model_name": "PHC-GNN", "paper_date": "2021-04-14", "paper_title": "", "paper_url": "https://arxiv.org/abs/2103.16584", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lukecavabarrett/pna"}], "metrics": {"Number of params": "326081", "Test ROC-AUC": "0.7905 ± 0.0132", "Validation ROC-AUC": "0.8519 ± 0.0099"}, "model_links": [], "model_name": "PNA", "paper_date": "2020-11-25", "paper_title": "", "paper_url": "https://arxiv.org/abs/2004.05718", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lsj2408/GraphNorm"}], "metrics": {"Number of params": "526201", "Test ROC-AUC": "0.7883 ± 0.0100", "Validation ROC-AUC": "0.7904 ± 0.0115"}, "model_links": [], "model_name": "GCN+GraphNorm", "paper_date": "2020-09-16", "paper_title": "", "paper_url": "https://arxiv.org/abs/2009.03294", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/rusty1s/himp-gnn"}], "metrics": {"Number of params": "153029", "Test ROC-AUC": "0.7880 ± 0.0082", "Validation ROC-AUC": "Please tell us"}, "model_links": [], "model_name": "HIMP", "paper_date": "2020-06-22", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.12179", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/tree/master/examples/ogb"}], "metrics": {"Number of params": "531976", "Test ROC-AUC": "0.7858 ± 0.0117", "Validation ROC-AUC": "0.8427 ± 0.0063"}, "model_links": [], "model_name": "DeeperGCN", "paper_date": "2020-06-16", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.07739", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/shyam196/egc"}], "metrics": {"Number of params": "317265", "Test ROC-AUC": "0.7818 ± 0.0153", "Validation ROC-AUC": "0.8396 ± 0.0097"}, "model_links": [], "model_name": "EGC-M (No Edge Features)", "paper_date": "2021-04-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.01481", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/gbouritsas/graph-substructure-networks"}], "metrics": {"Number of params": "3338701", "Test ROC-AUC": "0.7799 ± 0.0100", "Validation ROC-AUC": "0.8658 ± 0.0084"}, "model_links": [], "model_name": "GSN", "paper_date": "2021-01-05", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.09252", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/navid-naderi/WEGL"}], "metrics": {"Number of params": "361064", "Test ROC-AUC": "0.7757 ± 0.0111", "Validation ROC-AUC": "0.8101 ± 0.0097"}, "model_links": [], "model_name": "WEGL", "paper_date": "2020-06-26", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.09430", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "3336306", "Test ROC-AUC": "0.7748 ± 0.0096", "Validation ROC-AUC": "0.8438 ± 0.0128"}, "model_links": [], "model_name": "GIN+virtual node+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/shyam196/egc"}], "metrics": {"Number of params": "317013", "Test ROC-AUC": "0.7721 ± 0.0110", "Validation ROC-AUC": "0.8366 ± 0.0074"}, "model_links": [], "model_name": "EGC-S (No Edge Features)", "paper_date": "2021-04-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.01481", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/mol"}], "metrics": {"Number of params": "3336306", "Test ROC-AUC": "0.7707 ± 0.0149", "Validation ROC-AUC": "0.8479 ± 0.0068"}, "model_links": [], "model_name": "GIN+virtual node", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1810.00826", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "527701", "Test ROC-AUC": "0.7683 ± 0.0102", "Validation ROC-AUC": "0.8176 ± 0.0087"}, "model_links": [], "model_name": "GCN+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "1885206", "Test ROC-AUC": "0.7654 ± 0.0114", "Validation ROC-AUC": "0.8225 ± 0.0155"}, "model_links": [], "model_name": "GIN+FLAG", "paper_date": "2020-10-20", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/mol"}], "metrics": {"Number of params": "527701", "Test ROC-AUC": "0.7606 ± 0.0097", "Validation ROC-AUC": "0.8204 ± 0.0141"}, "model_links": [], "model_name": "GCN", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/mol"}], "metrics": {"Number of params": "1978801", "Test ROC-AUC": "0.7599 ± 0.0119", "Validation ROC-AUC": "0.8384 ± 0.0091"}, "model_links": [], "model_name": "GCN+virtual node", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/mol"}], "metrics": {"Number of params": "1885206", "Test ROC-AUC": "0.7558 ± 0.0140", "Validation ROC-AUC": "0.8232 ± 0.0090"}, "model_links": [], "model_name": "GIN", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1810.00826", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/irhum/GraphFlux.jl/blob/main/examples/ogbghiv.ipynb"}], "metrics": {"Number of params": "527701", "Test ROC-AUC": "0.7549 ± 0.0163", "Validation ROC-AUC": "0.8042 ± 0.0107"}, "model_links": [], "model_name": "GCN (in Julia)", "paper_date": "2021-06-28", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbg-molpcba", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test AP", "Validation AP", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/PaddlePaddle/PGL/tree/main/ogb_examples/graphproppred/ogbg_molpcba"}], "metrics": {"Number of params": "6147029", "Test AP": "0.2979 ± 0.0030", "Validation AP": "0.3126 ± 0.0023"}, "model_links": [], "model_name": "GINE+ w/ APPNP", "paper_date": "2021-03-15", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2011.15069.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/bayer-science-for-a-better-life/phc-gnn"}], "metrics": {"Number of params": "1690328", "Test AP": "0.2947 ± 0.0026", "Validation AP": "0.3068 ± 0.0025"}, "model_links": [], "model_name": "PHC-GNN", "paper_date": "2021-04-14", "paper_title": "", "paper_url": "https://arxiv.org/abs/2103.16584", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/RBrossard/GINEPLUS"}], "metrics": {"Number of params": "6147029", "Test AP": "0.2917 ± 0.0015", "Validation AP": "0.3065 ± 0.0030"}, "model_links": [], "model_name": "GINE+ w/ virtual nodes", "paper_date": "2020-12-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/2011.15069", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Saro00/DGN"}], "metrics": {"Number of params": "6732696", "Test AP": "0.2885 ± 0.0030", "Validation AP": "0.2970 ± 0.0021"}, "model_links": [], "model_name": "DGN", "paper_date": "2021-03-04", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2010.02863.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "5550208", "Test AP": "0.2842 ± 0.0043", "Validation AP": "0.2952 ± 0.0029"}, "model_links": [], "model_name": "DeeperGCN+virtual node+FLAG", "paper_date": "2020-10-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Saro00/DGN"}], "metrics": {"Number of params": "6550839", "Test AP": "0.2838 ± 0.0035", "Validation AP": "0.2926 ± 0.0026"}, "model_links": [], "model_name": "PNA", "paper_date": "2021-03-04", "paper_title": "", "paper_url": "https://arxiv.org/pdf/2004.05718.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "3374533", "Test AP": "0.2834 ± 0.0038", "Validation AP": "0.2912 ± 0.0026"}, "model_links": [], "model_name": "GIN+virtual node+FLAG", "paper_date": "2020-10-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/tree/master/examples/ogb"}], "metrics": {"Number of params": "5550208", "Test AP": "0.2781 ± 0.0038", "Validation AP": "0.2920 ± 0.0025"}, "model_links": [], "model_name": "DeeperGCN+virtual node", "paper_date": "2020-08-11", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.07739", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/mol"}], "metrics": {"Number of params": "3374533", "Test AP": "0.2703 ± 0.0023", "Validation AP": "0.2798 ± 0.0025"}, "model_links": [], "model_name": "GIN+virtual node", "paper_date": "2020-08-11", "paper_title": "", "paper_url": "https://arxiv.org/abs/1810.00826", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "2017028", "Test AP": "0.2483 ± 0.0037", "Validation AP": "0.2556 ± 0.0040"}, "model_links": [], "model_name": "GCN+virtual node+FLAG", "paper_date": "2020-10-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/mol"}], "metrics": {"Number of params": "2017028", "Test AP": "0.2424 ± 0.0034", "Validation AP": "0.2495 ± 0.0042"}, "model_links": [], "model_name": "GCN+virtual node", "paper_date": "2020-08-11", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "1923433", "Test AP": "0.2395 ± 0.0040", "Validation AP": "0.2451 ± 0.0042"}, "model_links": [], "model_name": "GIN+FLAG", "paper_date": "2020-10-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/Axeln78/Transferability-of-spectral-gnns"}], "metrics": {"Number of params": "1475003", "Test AP": "0.2306 ± 0.0016", "Validation AP": "0.2372 ± 0.0018"}, "model_links": [], "model_name": "ChebNet", "paper_date": "2020-12-28", "paper_title": "", "paper_url": "https://arxiv.org/abs/1606.09375", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/mol"}], "metrics": {"Number of params": "1923433", "Test AP": "0.2266 ± 0.0028", "Validation AP": "0.2305 ± 0.0027"}, "model_links": [], "model_name": "GIN", "paper_date": "2020-08-11", "paper_title": "", "paper_url": "https://arxiv.org/abs/1810.00826", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "565928", "Test AP": "0.2116 ± 0.0017", "Validation AP": "0.2150 ± 0.0022"}, "model_links": [], "model_name": "GCN+FLAG", "paper_date": "2020-10-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/cyrusmaher/ogb-molecule-comp"}], "metrics": {"Number of params": "29440000", "Test AP": "0.2054 ± 0.0004", "Validation AP": "0.2226 ± 0.0002"}, "model_links": [], "model_name": "MorganFP+Rand. Forest", "paper_date": "2020-09-21", "paper_title": "", "paper_url": "https://pubs.acs.org/doi/10.1021/ci100050t", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/mol"}], "metrics": {"Number of params": "565928", "Test AP": "0.2020 ± 0.0024", "Validation AP": "0.2059 ± 0.0033"}, "model_links": [], "model_name": "GCN", "paper_date": "2020-08-11", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbg-ppa", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test Accuracy", "Validation Accuracy", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/PierreHao/YouGraph"}], "metrics": {"Number of params": "3758642", "Test Accuracy": "0.8140 ± 0.0028", "Validation Accuracy": "0.7811 ± 0.0012"}, "model_links": [], "model_name": "ExpC*+bag of tricks", "paper_date": "2021-06-21", "paper_title": "", "paper_url": "https://github.com/PierreHao/YouGraph/blob/main/report/GMAN%20and%20bag%20of%20tricks%20for%20graph%20classification.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/qslim/epcb-gnns"}], "metrics": {"Number of params": "1369397", "Test Accuracy": "0.7976 ± 0.0072", "Validation Accuracy": "0.7518 ± 0.0080"}, "model_links": [], "model_name": "ExpC", "paper_date": "2020-12-14", "paper_title": "", "paper_url": "https://arxiv.org/abs/2012.07219", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "2336421", "Test Accuracy": "0.7752 ± 0.0069", "Validation Accuracy": "0.7484 ± 0.0052"}, "model_links": [], "model_name": "DeeperGCN+FLAG", "paper_date": "2020-10-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/lightaime/deep_gcns_torch/tree/master/examples/ogb"}], "metrics": {"Number of params": "2336421", "Test Accuracy": "0.7712 ± 0.0071", "Validation Accuracy": "0.7313 ± 0.0078"}, "model_links": [], "model_name": "DeeperGCN", "paper_date": "2020-06-16", "paper_title": "", "paper_url": "https://arxiv.org/abs/2006.07739", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "3288042", "Test Accuracy": "0.7245 ± 0.0114", "Validation Accuracy": "0.6789 ± 0.0079"}, "model_links": [], "model_name": "GIN+virtual node+FLAG", "paper_date": "2020-10-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/ppa"}], "metrics": {"Number of params": "3288042", "Test Accuracy": "0.7037 ± 0.0107", "Validation Accuracy": "0.6678 ± 0.0105"}, "model_links": [], "model_name": "GIN+virtual node", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1810.00826", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "1930537", "Test Accuracy": "0.6944 ± 0.0052", "Validation Accuracy": "0.6638 ± 0.0055"}, "model_links": [], "model_name": "GCN+virtual node+FLAG", "paper_date": "2020-10-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/devnkong/FLAG"}], "metrics": {"Number of params": "1836942", "Test Accuracy": "0.6905 ± 0.0092", "Validation Accuracy": "0.6465 ± 0.0070"}, "model_links": [], "model_name": "GIN+FLAG", "paper_date": "2020-10-21", "paper_title": "", "paper_url": "https://arxiv.org/abs/2010.09891", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/ppa"}], "metrics": {"Number of params": "1836942", "Test Accuracy": "0.6892 ± 0.0100", "Validation Accuracy": "0.6562 ± 0.0107"}, "model_links": [], "model_name": "GIN", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1810.00826", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/ppa"}], "metrics": {"Number of params": "1930537", "Test Accuracy": "0.6857 ± 0.0061", "Validation Accuracy": "0.6511 ± 0.0048"}, "model_links": [], "model_name": "GCN+virtual node", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/ppa"}], "metrics": {"Number of params": "479437", "Test Accuracy": "0.6839 ± 0.0084", "Validation Accuracy": "0.6497 ± 0.0034"}, "model_links": [], "model_name": "GCN", "paper_date": "2020-05-01", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ogbg-code2", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Test F1 score", "Validation F1 score", "Number of params"], "rows": [{"code_links": [{"title": "", "url": "https://github.com/PierreHao/YouGraph"}], "metrics": {"Number of params": "63684290", "Test F1 score": "0.1770 ± 0.0012", "Validation F1 score": "0.1631 ± 0.0090"}, "model_links": [], "model_name": "GMAN+bag of tricks", "paper_date": "2021-06-21", "paper_title": "", "paper_url": "https://github.com/PierreHao/YouGraph/blob/main/report/GMAN%20and%20bag%20of%20tricks%20for%20graph%20classification.pdf", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/vthost/DAGNN"}], "metrics": {"Number of params": "35246814", "Test F1 score": "0.1751 ± 0.0049", "Validation F1 score": "0.1607 ± 0.0040"}, "model_links": [], "model_name": "DAGNN", "paper_date": "2021-04-08", "paper_title": "", "paper_url": "https://openreview.net/pdf?id=JbuYF437WB6", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/code2"}], "metrics": {"Number of params": "12484310", "Test F1 score": "0.1595 ± 0.0018", "Validation F1 score": "0.1461 ± 0.0013"}, "model_links": [], "model_name": "GCN+virtual node", "paper_date": "2021-02-24", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/shyam196/egc"}], "metrics": {"Number of params": "10986002", "Test F1 score": "0.1595 ± 0.0019", "Validation F1 score": "0.1464 ± 0.0021"}, "model_links": [], "model_name": "EGC-M (No Edge Features)", "paper_date": "2021-04-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.01481", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/code2"}], "metrics": {"Number of params": "13841815", "Test F1 score": "0.1581 ± 0.0026", "Validation F1 score": "0.1439 ± 0.0020"}, "model_links": [], "model_name": "GIN+virtual node", "paper_date": "2021-02-24", "paper_title": "", "paper_url": "https://arxiv.org/abs/1810.00826", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/shyam196/egc"}], "metrics": {"Number of params": "10992050", "Test F1 score": "0.1570 ± 0.0032", "Validation F1 score": "0.1453 ± 0.0025"}, "model_links": [], "model_name": "PNA (No Edge Features)", "paper_date": "2021-04-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.01481", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/shyam196/egc"}], "metrics": {"Number of params": "10971506", "Test F1 score": "0.1552 ± 0.0022", "Validation F1 score": "0.1441 ± 0.0016"}, "model_links": [], "model_name": "MPNN-Max (No Edge Features)", "paper_date": "2021-04-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.01481", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/shyam196/egc"}], "metrics": {"Number of params": "11156530", "Test F1 score": "0.1528 ± 0.0025", "Validation F1 score": "0.1427 ± 0.0020"}, "model_links": [], "model_name": "EGC-S (No Edge Features)", "paper_date": "2021-04-06", "paper_title": "", "paper_url": "https://arxiv.org/abs/2104.01481", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/code2"}], "metrics": {"Number of params": "11033210", "Test F1 score": "0.1507 ± 0.0018", "Validation F1 score": "0.1399 ± 0.0017"}, "model_links": [], "model_name": "GCN", "paper_date": "2021-02-24", "paper_title": "", "paper_url": "https://arxiv.org/abs/1609.02907", "uses_additional_data": false}, {"code_links": [{"title": "", "url": "https://github.com/snap-stanford/ogb/tree/master/examples/graphproppred/code2"}], "metrics": {"Number of params": "12390715", "Test F1 score": "0.1495 ± 0.0023", "Validation F1 score": "0.1376 ± 0.0016"}, "model_links": [], "model_name": "GIN", "paper_date": "2021-02-24", "paper_title": "", "paper_url": "https://arxiv.org/abs/1810.00826", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": {"title": "Graph Property Prediction LeaderBoard", "url": "https://ogb.stanford.edu/docs/leader_graphprop/"}, "subtasks": [], "synonyms": [], "task": "Graph Property Prediction"}]
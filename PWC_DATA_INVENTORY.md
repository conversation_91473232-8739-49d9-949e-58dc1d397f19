# Papers with Code Data Inventory

## 🗄️ Preserved Data

### Core Data Files (located in `data/` directory)

1. **datasets.json.gz** (8.0 MB)
   - Contains complete information for all datasets
   - Dataset descriptions, associated tasks, paper links, etc.
   - This is one of PWC's most important datasets

2. **evaluation-tables.json.gz** (21 MB)
   - All task evaluation tables and benchmarks
   - Task hierarchical relationships (parent-child tasks)
   - SOTA (State of the Art) results
   - Various evaluation metrics

3. **methods.json.gz** / **methods.json** (5.2 MB / 28 MB)
   - Detailed information for all methods and algorithms
   - Method descriptions, related papers, etc.

4. **papers-with-abstracts.json.gz** (537.7 MB)
   - Contains 576,261 papers with abstracts
   - Full paper metadata

5. **links-between-papers-and-code.json.gz** (25.0 MB)
   - Paper-to-code repository associations
   - 300,161 links between papers and implementations

### Processed Data (located in `results/` directory)

11 CSV files containing classifications and detailed information:
- Task lists classified by domain (CV, NLP, Audio, Other)
- Detailed task information (datasets, benchmarks, metrics, etc.)
- Task hierarchical relationships
- Research area classifications

## 🔍 Data Integrity Assessment

### Data You Have:
- ✅ **Task System**: Complete classification of 4,451 tasks
- ✅ **Dataset Information**: Metadata for all datasets
- ✅ **Evaluation Benchmarks**: All benchmarks and SOTA results
- ✅ **Methods & Algorithms**: Information for all methods
- ✅ **Papers & Abstracts**: 576,261 papers with full abstracts
- ✅ **Paper-Code Links**: 300,161 implementation links

## 💡 Data Value

The preserved data contains Papers with Code's most essential content:

1. **Complete AI/ML Task Classification System**
   - A valuable resource for understanding AI research domains
   - Includes 2,275 CV tasks, 1,093 NLP tasks, etc.

2. **Benchmarks and Evaluation Standards**
   - 3,468 different evaluation metrics
   - Historical SOTA results for each task

3. **Dataset Catalog**
   - Information for all major datasets used in AI research
   - Dataset-to-task mappings

## 📊 Statistical Summary

- **Total Data Volume**: ~625 MB (compressed)
- **Total Tasks**: 4,451
- **Papers**: 576,261
- **Code Links**: 300,161
- **Datasets**: 15,008
- **Evaluation Metrics**: 3,468
- **Research Areas**: 17 major categories

### Research Area Distribution (from PWC client analysis):
1. Computer Vision: 865 tasks
2. Natural Language Processing: 436 tasks
3. Miscellaneous: 219 tasks
4. Medical: 190 tasks
5. Methodology: 157 tasks
6. Time Series: 98 tasks
7. Graphs: 87 tasks
8. Audio: 69 tasks
9. Computer Code: 61 tasks
10. Robots: 56 tasks
11. Knowledge Base: 50 tasks
12. Reasoning: 50 tasks
13. Speech: 48 tasks
14. Playing Games: 40 tasks
15. Music: 32 tasks
16. Adversarial: 31 tasks

## 🔐 Backup Recommendations

1. **Immediate Backup** to multiple locations:
   - Cloud storage (Google Drive, Dropbox, etc.)
   - External hard drives
   - Version control systems (Git LFS)

2. **Regular Verification** of data integrity:
   - Check file sizes
   - Verify successful decompression
   - Ensure CSV files are readable

3. **Documentation**:
   - Save this README and all documentation
   - Keep data processing scripts
   - Preserve original link information

## 🚨 Important Reminder

Papers with Code has ceased operations, and this data represents:
- The final snapshot before website closure
- An important record of AI/ML research history
- A precious resource that cannot be obtained again

**Please carefully preserve this data!**

---
*Last updated: July 25, 2025*
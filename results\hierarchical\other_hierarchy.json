{"domain": "other", "hierarchical_tasks": [], "standalone_tasks": [{"name": "Person Re-Identification", "children": [], "description": "", "dataset_count": 65, "benchmarks": ["eSports Sensors Dataset", "DukeMTMC-reID", "Market-1501->DukeMTMC-reID", "P-DukeMTMC-reID", "Market-1501", "AG-ReID", "SYSU-MM01", "Market-1501-C", "MSMT17", "PRID2011"], "metrics": [], "task_id": "", "area": ""}, {"name": "Data Augmentation", "children": [], "description": "", "dataset_count": 63, "benchmarks": ["CIFAR-10", "ImageNet", "GA1457"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Task Learning", "children": [], "description": "", "dataset_count": 59, "benchmarks": ["NYUv2", "CelebA", "ChestX-ray14", "Cityscapes test", "QM9", "UTKFace", "OMNIGLOT", "wireframe dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Self-Supervised Learning", "children": [], "description": "", "dataset_count": 47, "benchmarks": ["TinyImageNet", "STL-10", "DABS", "CREMA-D", "cifar100", "CIFAR-100", "CIFAR-10", "ImageNet-100 (TEMI Split)", "cifar10", "Tiny ImageNet"], "metrics": [], "task_id": "", "area": ""}, {"name": "Time Series Forecasting", "children": [], "description": "", "dataset_count": 46, "benchmarks": ["ETTh2 (168) Multivariate", "ETTh1 (720) Multivariate", "Weather2K1786 (336)", "ETTh1 (48) Univariate", "Weather (96)", "ETTh1 (336) Univariate", "Solar (96)", "ETTh1 (336) Multivariate", "Electricity (336)", "ETTh2 (24) Univariate"], "metrics": [], "task_id": "", "area": ""}, {"name": "Entity Linking", "children": [], "description": "", "dataset_count": 44, "benchmarks": ["BC7 NLM-Chem", "WebQSP-WD", "N3-Reuters-128", "OKE-2015", "WiC-TSV", "TAC-KBP 2010", "MSNBC", "CoNLL-Aida", "Rare Diseases Mentions in MIMIC-III (Text-to-UMLS)", "FUNSD"], "metrics": [], "task_id": "", "area": ""}, {"name": "Misinformation", "children": [], "description": "", "dataset_count": 44, "benchmarks": ["NLP4IF-2021--Fighting the COVID-19 Infodemic "], "metrics": [], "task_id": "", "area": ""}, {"name": "Decision Making", "children": [], "description": "", "dataset_count": 40, "benchmarks": ["./01/01/1967", "NASA C-MAPSS"], "metrics": [], "task_id": "", "area": ""}, {"name": "Continual Learning", "children": [], "description": "", "dataset_count": 35, "benchmarks": ["DSC (10 tasks)", "Split MNIST (5 tasks)", "CUB-200-2011 (20 tasks) - 1 epoch", "Tiny-ImageNet (10tasks)", "visual domain decathlon (10 tasks)", "Flowers (Fine-grained 6 Tasks)", "AIDS", "MLT17", "split CIFAR-100", "5-Datasets"], "metrics": [], "task_id": "", "area": ""}, {"name": "Metric Learning", "children": [], "description": "", "dataset_count": 33, "benchmarks": ["DyML-Animal", "DyML-Vehicle", "CUB-200-2011", "In-Shop", "Stanford Online Products", "CARS196", "DyML-Product", " CUB-200-2011"], "metrics": [], "task_id": "", "area": ""}, {"name": "regression", "children": [], "description": "", "dataset_count": 31, "benchmarks": ["Synthetic: y = x * sin x", "ADORE", "Medical Cost Personal Dataset", "California Housing Prices", "Car_Price_Prediction", "Concrete Compressive Strength"], "metrics": [], "task_id": "", "area": ""}, {"name": "Drug Discovery", "children": [], "description": "", "dataset_count": 26, "benchmarks": ["SIDER", "BindingDB IC50", "ToxCast", "DRD2", "KIBA", "DAVIS-DTA", "BACE", "PDBbind", "Tox21", "QED"], "metrics": [], "task_id": "", "area": ""}, {"name": "Time Series Analysis", "children": [], "description": "", "dataset_count": 25, "benchmarks": ["PhysioNet Challenge 2012", "Speech Commands", "Ventilator Pressure Prediction"], "metrics": [], "task_id": "", "area": ""}, {"name": "Fairness", "children": [], "description": "", "dataset_count": 23, "benchmarks": ["DiveFace", "BAF – Variant I", "BAF – Base", "BAF – Variant III", "MORPH", "BAF – Variant V", "UTKFace", "BAF – Variant II", "BAF – Variant IV"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multimodal Deep Learning", "children": [], "description": "", "dataset_count": 22, "benchmarks": ["CUB-200-2011"], "metrics": [], "task_id": "", "area": ""}, {"name": "Robotic Grasping", "children": [], "description": "", "dataset_count": 22, "benchmarks": ["NBMOD", "GraspNet-1Billion", " Jacquard dataset", "Cornell Grasp Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Regression", "children": [], "description": "", "dataset_count": 21, "benchmarks": ["ZINC 10k", "PGR ", "PCQM4M-LSC", "ZINC", "F2", "PARP1", "ZINC-500k", "ESOL", "ZINC 100k", "ZINC-full"], "metrics": [], "task_id": "", "area": ""}, {"name": "Imitation Learning", "children": [], "description": "", "dataset_count": 21, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Age Estimation", "children": [], "description": "", "dataset_count": 20, "benchmarks": ["ChaLearn 2015", "AFAD", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AgeDB", "LAGENDA", "PhysioNet Challenge 2021", "UTKFace", "KANFace", "FGNET", "IMDB-Clean"], "metrics": [], "task_id": "", "area": ""}, {"name": "Reinforcement Learning (RL)", "children": [], "description": "", "dataset_count": 20, "benchmarks": [".", "ProcGen"], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Clustering", "children": [], "description": "", "dataset_count": 19, "benchmarks": ["Citeseer", "<PERSON> et al", "Cora", "<PERSON><PERSON><PERSON><PERSON> et al", "<PERSON><PERSON> et al", "<PERSON><PERSON><PERSON> et al", "Biase et al", "<PERSON><PERSON><PERSON> et al", "<PERSON> et al", "Pubmed"], "metrics": [], "task_id": "", "area": ""}, {"name": "Quantization", "children": [], "description": "", "dataset_count": 18, "benchmarks": ["IJB-C", "AgeDB-30", "LFW", "Wiki-40B", "CFP-FP", "CIFAR-10", "ImageNet", "Knowledge-based:", "COCO (Common Objects in Context)", "IJB-B"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multivariate Time Series Forecasting", "children": [], "description": "", "dataset_count": 18, "benchmarks": ["ETTh1 (48) Multivariate", "ETTh1 (720) Multivariate", "MIMIC-III", "USHCN-Daily", "ETTh2 (720) Multivariate", "PhysioNet Challenge 2012", "Weather", "ETTh1 (96) Multivariate", "Helpdesk", "Traffic"], "metrics": [], "task_id": "", "area": ""}, {"name": "Time Series", "children": [], "description": "", "dataset_count": 18, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Binarization", "children": [], "description": "", "dataset_count": 17, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Topic Models", "children": [], "description": "", "dataset_count": 17, "benchmarks": ["AG News", "AgNews", "20NewsGroups", "Arxiv HEP-TH citation graph", "20 Newsgroups", "NYT"], "metrics": [], "task_id": "", "area": ""}, {"name": "Meta-Learning", "children": [], "description": "", "dataset_count": 17, "benchmarks": ["ML45", "MT50", "OMNIGLOT - 1-Shot, 20-way", "ML10"], "metrics": [], "task_id": "", "area": ""}, {"name": "SMAC+", "children": [], "description": "", "dataset_count": 17, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Long-tail Learning", "children": [], "description": "", "dataset_count": 16, "benchmarks": ["CIFAR-10-LT (ρ=200)", "ImageNet-LT", "COCO-MLT", "MIMIC-CXR-LT", "Places-LT", "VOC-MLT", "iNaturalist 2018", "CIFAR-100-LT (ρ=50)", "Lot-insts", "CIFAR-10-LT (ρ=50)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Prompt Engineering", "children": [], "description": "", "dataset_count": 16, "benchmarks": ["Food-101", "FGVC-Aircraft", "ImageNet-R", "ImageNet V2", "ImageNet-A", "Oxford 102 Flower", "Stanford Cars", "Oxford-IIIT Pet Dataset", "SUN397", "ImageNet"], "metrics": [], "task_id": "", "area": ""}, {"name": "Contrastive Learning", "children": [], "description": "", "dataset_count": 16, "benchmarks": ["imagenet-1k", "STL-10", "10,000 People - Human Pose Recognition Data", "CIFAR-10"], "metrics": [], "task_id": "", "area": ""}, {"name": "Learning with noisy labels", "children": [], "description": "", "dataset_count": 16, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Self-Driving Cars", "children": [], "description": "", "dataset_count": 16, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Weather Forecasting", "children": [], "description": "", "dataset_count": 16, "benchmarks": ["SEVIR", "NOAA Atmospheric Temperature Dataset", "Shifts", "LA", "SD"], "metrics": [], "task_id": "", "area": ""}, {"name": "Node Clustering", "children": [], "description": "", "dataset_count": 15, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Physical Simulations", "children": [], "description": "", "dataset_count": 15, "benchmarks": ["Impact Plate", "4D-DRESS", "Deformable Plate", "Deforming Plate", "Sphere Simple"], "metrics": [], "task_id": "", "area": ""}, {"name": "Within-Session ERP", "children": [], "description": "", "dataset_count": 15, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Density Estimation", "children": [], "description": "", "dataset_count": 13, "benchmarks": ["BSDS300", "Frey<PERSON><PERSON>", "UCI POWER", "UCI MINIBOONE", "CIFAR-10", "ImageNet 64x64", "CIFAR-10 (Conditional)", "UCI HEPMASS", "OMNIGLOT", "Caltech-101"], "metrics": [], "task_id": "", "area": ""}, {"name": "Clustering Algorithms Evaluation", "children": [], "description": "", "dataset_count": 13, "benchmarks": ["pixraw10P", "iris", "Fashion-MNIST", "JAFFE", "ionosphere", "<PERSON><PERSON> face", "seeds", "pathbased", "MNIST", "97 synthetic datasets"], "metrics": [], "task_id": "", "area": ""}, {"name": "Adversarial Robustness", "children": [], "description": "", "dataset_count": 13, "benchmarks": ["Stylized ImageNet", "ImageNet-A", "AdvGLUE", "ImageNet-C", "CIFAR-100", "CIFAR-10", "ImageNet"], "metrics": [], "task_id": "", "area": ""}, {"name": "UIE", "children": [], "description": "", "dataset_count": 13, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Entity Disambiguation", "children": [], "description": "", "dataset_count": 13, "benchmarks": ["Mewsli-9", "MSNBC", "AIDA-CoNLL", "ShadowLink-Top", "WNED-WIKI", "ACE2004", "DocRED-IE", "ShadowLink-Shadow", "WNED-CWEB", "AQUAINT"], "metrics": [], "task_id": "", "area": ""}, {"name": "Open-Domain Dialog", "children": [], "description": "", "dataset_count": 13, "benchmarks": ["KILT: Wizard of Wikipedia"], "metrics": [], "task_id": "", "area": ""}, {"name": "Disentanglement", "children": [], "description": "", "dataset_count": 13, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Embedding", "children": [], "description": "", "dataset_count": 12, "benchmarks": ["Barabasi<PERSON><PERSON>"], "metrics": [], "task_id": "", "area": ""}, {"name": "Imputation", "children": [], "description": "", "dataset_count": 12, "benchmarks": ["Adult", "HMNIST", "PhysioNet Challenge 2012", "Sprites"], "metrics": [], "task_id": "", "area": ""}, {"name": "En<PERSON>ty Ty<PERSON>", "children": [], "description": "", "dataset_count": 12, "benchmarks": ["FIGER", "Ontonotes v5 (English)", "Freebase FIGER", "AIDA-CoNLL", "DocRED-IE", " Open Entity", "Open Entity", "OntoNotes"], "metrics": [], "task_id": "", "area": ""}, {"name": "COVID-19 Diagnosis", "children": [], "description": "", "dataset_count": 12, "benchmarks": ["COVID-19 CXR Dataset", "", "COVIDx", "COVIDx CXR-3", "Large COVID-19 CT scan slice dataset", "Novel COVID-19 Chestxray Repository", "Covid-19 <PERSON>ugh Cambridge", "COVIDGR"], "metrics": [], "task_id": "", "area": ""}, {"name": "Referring Expression Comprehension", "children": [], "description": "", "dataset_count": 12, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Entity Resolution", "children": [], "description": "", "dataset_count": 12, "benchmarks": ["Amazon-Google", "MusicBrainz20K", "Abt-Buy", "WDC Products", "WDC Watches-xlarge", "WDC Products-80%cc-seen-medium", "WDC Computers-small", "WDC Products-50%cc-unseen-medium", "WDC Computers-xlarge", "WDC Products-80%cc-seen-medium-multi"], "metrics": [], "task_id": "", "area": ""}, {"name": "Robot Manipulation", "children": [], "description": "", "dataset_count": 12, "benchmarks": ["SimplerEnv-Google Robot", "CALVIN", "RLBench", "SimplerEnv-Widow X", "MimicGen"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multiple-choice", "children": [], "description": "", "dataset_count": 12, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Stochastic Optimization", "children": [], "description": "", "dataset_count": 11, "benchmarks": ["AG News", "CIFAR-10 WRN-28-10 - 200 Epochs", "CIFAR-10 ResNet-18 - 200 Epochs", "ImageNet ResNet-50 - 60 Epochs", "Penn Treebank (Character Level) 3x1000 LSTM - 500 Epochs", "CIFAR-100", "CoLA", "CIFAR-10", "CIFAR-100 WRN-28-10 - 200 Epochs", "MNIST"], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Person Re-Identification", "children": [], "description": "", "dataset_count": 11, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Adversarial Attack", "children": [], "description": "", "dataset_count": 11, "benchmarks": ["CIFAR-10", "WSJ0-2mix", "CIFAR-100"], "metrics": [], "task_id": "", "area": ""}, {"name": "Virtual Try-on", "children": [], "description": "", "dataset_count": 11, "benchmarks": ["MPV", "Microsoft COCO dataset", "StreetTryOn", "Deep-Fashion", "Dress Code", "UBC Fashion Videos", "VITON-HD", "VITON", "FashionIQ"], "metrics": [], "task_id": "", "area": ""}, {"name": "Federated Learning", "children": [], "description": "", "dataset_count": 11, "benchmarks": ["CIFAR-100 (alpha=0, 10 clients per round)", "Landmarks-User-160k", "CIFAR-100 (alpha=1000, 10 clients per round)", "Cityscapes heterogeneous", "CIFAR-100 (alpha=1000, 20 clients per round)", "CIFAR100 (alpha=0.3, 10 clients per round)", "CIFAR-100 (alpha=1000, 5 clients per round)", "CIFAR-100 (alpha=0, 20 clients per round)", "CIFAR-100 (alpha=0.5, 10 clients per round)", "CIFAR-100 (alpha=0.5, 5 clients per round)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Continuous Control", "children": [], "description": "", "dataset_count": 10, "benchmarks": ["Simple Humanoid", "Swimmer + Gathering", "hopper.hop", "walker.stand", "<PERSON><PERSON> (OpenAI Gym)", "Ball in cup, catch (DMControl100k)", "quadruped.walk", "Full Humanoid", "Ant + Gathering", "Chee<PERSON>, run (DMControl500k)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Learning", "children": [], "description": "", "dataset_count": 10, "benchmarks": ["CAMELS"], "metrics": [], "task_id": "", "area": ""}, {"name": "Automatic Post-Editing", "children": [], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Person Identification", "children": [], "description": "", "dataset_count": 10, "benchmarks": ["WiGesture", "BioEye", "EEG Motor Movement/Imagery Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Benchmarking", "children": [], "description": "", "dataset_count": 10, "benchmarks": ["Wiki-40B", "CloudEval-YAML"], "metrics": [], "task_id": "", "area": ""}, {"name": "Table annotation", "children": [], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Column Type Annotation", "children": [], "description": "", "dataset_count": 10, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Time Series Regression", "children": [], "description": "", "dataset_count": 10, "benchmarks": ["USNA-Cn2 (long-term)", "MLO-Cn2", "FinSen", "USNA-Cn2 (short-duration)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Homography Estimation", "children": [], "description": "", "dataset_count": 9, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Reinforcement Learning", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["Time Pilot", "Venture", "Boss Level", "Frostbite", "Ice Hockey", "<PERSON>", "Unblock Pickup", "ATLANTIS", "Demon Attack", "Tutankham"], "metrics": [], "task_id": "", "area": ""}, {"name": "AutoML", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["Wine", "Breast Cancer Coimbra Data Set", "OrdinalDataset", "Chalearn-AutoML-1"], "metrics": [], "task_id": "", "area": ""}, {"name": "Shadow Removal", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["INS Dataset", "ISTD+", "SRD", "WSRD+", "ISTD", "^(#$!@#$)(()))******"], "metrics": [], "task_id": "", "area": ""}, {"name": "Program Repair", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["GitHub-Python", "TFix's Code Patches Data", "HumanEvalPack", "DeepFix"], "metrics": [], "task_id": "", "area": ""}, {"name": "Learning-To-Rank", "children": [], "description": "", "dataset_count": 9, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-agent Reinforcement Learning", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["SMAC-Exp", "UAV Logistics", "ParticleEnvs Cooperative Communication"], "metrics": [], "task_id": "", "area": ""}, {"name": "Representation Learning", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["CIFAR10", "Sports10", "Animals-10", "SciDocs", "Circle Data"], "metrics": [], "task_id": "", "area": ""}, {"name": "Code Repair", "children": [], "description": "", "dataset_count": 9, "benchmarks": ["CodeXGLUE - Bugs2Fix"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Label Learning", "children": [], "description": "", "dataset_count": 8, "benchmarks": ["COCO 2014"], "metrics": [], "task_id": "", "area": ""}, {"name": "Lipreading", "children": [], "description": "", "dataset_count": 8, "benchmarks": ["LRS2", "GRID corpus (mixed-speech)", "CMLR", "CAS-VSR-S101", "Lip Reading in the Wild", "LRW-1000", "CAS-VSR-W1k (LRW-1000)", "LRS3-TED"], "metrics": [], "task_id": "", "area": ""}, {"name": "Incremental Learning", "children": [], "description": "", "dataset_count": 8, "benchmarks": ["ImageNet-100 - 50 classes + 50 steps of 1 class", "CIFAR100-B0(10steps of 10 classes)", "CIFAR-100 - 50 classes + 5 steps of 10 classes", "ImageNet-100 - 50 classes + 25 steps of 2 classes", "ImageNet - 500 classes + 25 steps of 20 classes", "MLT17", "CIFAR-100-B0(5steps of 20 classes)", "ImageNet - 10 steps", "ImageNet - 500 classes + 5 steps of 100 classes", "ImageNet-100 - 50 classes + 5 steps of 10 classes"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multivariate Time Series Imputation", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Correlated Time Series Forecasting", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Disaster Response", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Automated Theorem Proving", "children": [], "description": "", "dataset_count": 8, "benchmarks": ["HolStep (Unconditional)", "miniF2F-valid", "miniF2F-test", "Metamath set.mm", "CoqGym", "HOList benchmark", "miniF2F-curriculum", "HolStep (Conditional)", "<PERSON><PERSON><PERSON><PERSON>"], "metrics": [], "task_id": "", "area": ""}, {"name": "Starcraft", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Heart rate estimation", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Explainable artificial intelligence", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "AI Agent", "children": [], "description": "", "dataset_count": 8, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "GLinear", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Argument Mining", "children": [], "description": "", "dataset_count": 7, "benchmarks": ["TACO -- Twitter Arguments from COnversations"], "metrics": [], "task_id": "", "area": ""}, {"name": "Driver Attention Monitoring", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Point Processes", "children": [], "description": "", "dataset_count": 7, "benchmarks": ["MIMIC-II", "MemeTracker", "Amazon MTPP", "Stackoverflow", "Retweet MTPP", "RETWEET", "StackOverflow MTPP", "AgeGroup Transactions MTPP"], "metrics": [], "task_id": "", "area": ""}, {"name": "Offline RL", "children": [], "description": "", "dataset_count": 7, "benchmarks": ["D4RL", "Walker2d"], "metrics": [], "task_id": "", "area": ""}, {"name": "Rain Removal", "children": [], "description": "", "dataset_count": 7, "benchmarks": ["DID-MDN", "Nightrain"], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Pre-training", "children": [], "description": "", "dataset_count": 7, "benchmarks": ["UCI measles", "<PERSON><PERSON><PERSON>"], "metrics": [], "task_id": "", "area": ""}, {"name": "Robot Task Planning", "children": [], "description": "", "dataset_count": 7, "benchmarks": ["SheetCopilot", "PackIt"], "metrics": [], "task_id": "", "area": ""}, {"name": "Data Integration", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Within-Session SSVEP", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "RAG", "children": [], "description": "", "dataset_count": 7, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Personalized Federated Learning", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Phrase Grounding", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["ReferIt", "Flickr30k", "Flickr30k Entities Dev", "Flickr30k Entities Test", "Visual Genome"], "metrics": [], "task_id": "", "area": ""}, {"name": "Lip Reading", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["GRID corpus (mixed-speech)", "TCD-TIMIT corpus (mixed-speech)", "LRW"], "metrics": [], "task_id": "", "area": ""}, {"name": "Explanation Fidelity Evaluation", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Collaborative Filtering", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["Yelp2018", "<PERSON><PERSON><PERSON>", "MovieLens 1M", "Amazon-Book"], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Mining", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Band Gap", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Feature Importance", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["Breastcancer", "Digits", "iris", "boston", "Diabetes", "Wine"], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Representation Learning", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["COMA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Univariate Time Series Forecasting", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Bandwidth Extension", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Atari Games", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["Atari 2600 Gopher", "Atari 2600 Berzerk", "Atari 2600 Pooyan", "Atari 2600 Elevator Action", "Atari 2600 Fishing Derby", "Atari 2600 Defender", "Atari 2600 Up and Down", "Atari 2600 Seaquest", "Atari 2600 Pitfall!", "Atari 2600 Tennis"], "metrics": [], "task_id": "", "area": ""}, {"name": "Starcraft II", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Goal-Oriented Dialog", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["<PERSON><PERSON><PERSON>"], "metrics": [], "task_id": "", "area": ""}, {"name": "Probabilistic Deep Learning", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "16k", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["ConceptNet"], "metrics": [], "task_id": "", "area": ""}, {"name": "Open Set Learning", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Explainable Artificial Intelligence (XAI)", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["ADNI"], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Modal Person Re-Identification", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Protein Design", "children": [], "description": "", "dataset_count": 6, "benchmarks": ["CATH 4.3", "CATH 4.2"], "metrics": [], "task_id": "", "area": ""}, {"name": "Math", "children": [], "description": "", "dataset_count": 6, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Core set discovery", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["Kr-vs-kp", "Credit-g", "Mozilla4", "Amazon-employee-access", "Abalone", "Soybean", "ISOLET", "Letter", "JM1", "Electricity"], "metrics": [], "task_id": "", "area": ""}, {"name": "Adversarial Defense", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["ImageNet (non-targeted PGD, max perturbation=4)", "CAAD 2018", "CIFAR-100", "CIFAR-10", "ImageNet", "ImageNet (targeted PGD, max perturbation=16)", "miniImageNet", "MNIST", "TrojAI Round 1", "TrojAI Round 0"], "metrics": [], "task_id": "", "area": ""}, {"name": "Network Pruning", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["CIFAR-100", "ImageNet - ResNet 50 - 90% sparsity", "CIFAR-10", "ImageNet", "MNIST"], "metrics": [], "task_id": "", "area": ""}, {"name": "Linguistic Acceptability", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["DaLAJ", "CoLA", "CoLA Dev", "ItaCoLA", "RuCoLA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Chunking", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["Penn Treebank", "CoNLL 2000", "CoNLL 2003 (English)", "CoNLL 2003 (German)", "CoNLL 2003"], "metrics": [], "task_id": "", "area": ""}, {"name": "Point Cloud Completion", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["Completion3D", "ShapeNet", "ShapeNet-ViPC"], "metrics": [], "task_id": "", "area": ""}, {"name": "Region Proposal", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Clustering", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Complex Query Answering", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Arousal Estimation", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Valence Estimation", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Handwriting Verification", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["AND Dataset", "CEDAR Signature"], "metrics": [], "task_id": "", "area": ""}, {"name": "Gaussian Processes", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["UCI POWER"], "metrics": [], "task_id": "", "area": ""}, {"name": "Network Embedding", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Real-Time Strategy Games", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Color Constancy", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["INTEL-TUT2"], "metrics": [], "task_id": "", "area": ""}, {"name": "Feature Engineering", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["2019_test set"], "metrics": [], "task_id": "", "area": ""}, {"name": "Time Series Clustering", "children": [], "description": "", "dataset_count": 5, "benchmarks": ["eICU Collaborative Research Database"], "metrics": [], "task_id": "", "area": ""}, {"name": "Causal Discovery", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Physics-informed machine learning", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SSTOD", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Natural Questions", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "College Mathematics", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Elementary Mathematics", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Uncertainty Quantification", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Intelligent Communication", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Occlusion Handling", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "PDE Surrogate Modeling", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "AI and Safety", "children": [], "description": "", "dataset_count": 5, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "POS", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "One-Shot Learning", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["MNIST"], "metrics": [], "task_id": "", "area": ""}, {"name": "Fill Mask", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Dense Pixel Correspondence Estimation", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["TSS", "HPatches", "KITTI 2015", "ETH3D", "KITTI 2012"], "metrics": [], "task_id": "", "area": ""}, {"name": "Interpretable Machine Learning", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["CUB-200-2011"], "metrics": [], "task_id": "", "area": ""}, {"name": "Long-tail learning with class descriptors", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "FG-1-PG-1", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Partial Label Learning", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["CIFAR-100 (partial ratio 0.1)", "M-VAD Names", "CIFAR-100 (partial ratio 0.01)", "CIFAR-10 (partial ratio 0.3)", "Caltech-UCSD Birds 200 (partial ratio 0.05)", "CIFAR-10 (partial ratio 0.5)", "ISIC 2019", "CIFAR-10 (partial ratio 0.1)", "Autoimmune Dataset", "CIFAR-100 (partial ratio 0.05)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Open-World Semi-Supervised Learning", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Learning with coarse labels", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph-to-Sequence", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["LDC2015E86:", "WebNLG"], "metrics": [], "task_id": "", "area": ""}, {"name": "SSIM", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["DocUNet"], "metrics": [], "task_id": "", "area": ""}, {"name": "Compressive Sensing", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["BSD68 CS=50%", "Urban100 - 2x upscaling", "BSDS100 - 2x upscaling", "Set5", "Set11 cs=50%"], "metrics": [], "task_id": "", "area": ""}, {"name": "Matrix Completion", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Room Layout Estimation", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["SUN RGB-D"], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON> (multi-choices) (One-Shot)", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Disparity Estimation", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["Sintel 4D LFV - ambushfight5", "Sintel 4D LFV - bamboo3", "Sintel 4D LFV - shaman2", "Sintel 4D LFV - thebigfight2"], "metrics": [], "task_id": "", "area": ""}, {"name": "STS", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Meter Reading", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["UFPR-AMR Dataset", "UFPR-AMR", "Copel-AMR", "UFPR-ADMR-v1"], "metrics": [], "task_id": "", "area": ""}, {"name": "Decision Making Under Uncertainty", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Survival Analysis", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Remaining Useful Lifetime Estimation", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["NASA C-MAPSS-2", "NASA C-MAPSS"], "metrics": [], "task_id": "", "area": ""}, {"name": "Long-range modeling", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Astronomy", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Memorization", "children": [], "description": "", "dataset_count": 4, "benchmarks": ["BIG-bench (Hindu Knowledge)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Columns Property Annotation", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Persuasion Strategies", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Behavior Forecasting", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Temporal Sequences", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Pansharpening", "children": [], "description": "", "dataset_count": 4, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Model Poisoning", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sparse Learning and binarization", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sparse Learning", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["ImageNet", "CINIC-10", "ImageNet32"], "metrics": [], "task_id": "", "area": ""}, {"name": "OpenAI Gym", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["HalfCheetah-v2", "CartPole-v1", "MountainCarContinuous-v0", "Walker2d-v2", "Ant-v4", "Humanoid-v2", "InvertedDoublePendulum-v2", "InvertedPendulum-v2", "Walker2d-v4", "Hopper-v2"], "metrics": [], "task_id": "", "area": ""}, {"name": "Unconstrained Lip-synchronization", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Blood pressure estimation", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["Multi-day Continuous BP Prediction", "MIMIC-III", "VitalDB"], "metrics": [], "task_id": "", "area": ""}, {"name": "Dataset Distillation - 1IPC", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Timex normalization", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Continual Pretraining", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["AG News", "ACL-ARC", "SciERC"], "metrics": [], "task_id": "", "area": ""}, {"name": "Novel Class Discovery", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["cifar100", "SVHN", "cifar10"], "metrics": [], "task_id": "", "area": ""}, {"name": "ECG Patient Identification", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ECG Patient Identification (gallery-probe)", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "parameter-efficient fine-tuning", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["HellaSwag", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BoolQ"], "metrics": [], "task_id": "", "area": ""}, {"name": "Skills Assessment", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["Multimodal PISA"], "metrics": [], "task_id": "", "area": ""}, {"name": "Skills Evaluation", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["eSports Sensors Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Cloud Removal", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Modal  Person Re-Identification", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Combinatorial Optimization", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Grap<PERSON>", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "One-class classifier", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Art Analysis", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Mathematical Proofs", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Efficient Exploration", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Relation Linking", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Large-Scale Person Re-Identification", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Mesh Recovery", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["BEDLAM"], "metrics": [], "task_id": "", "area": ""}, {"name": "Hyperparameter Optimization", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Radar odometry", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["Oxford Radar RobotCar Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Privacy Preserving Deep Learning", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-target regression", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["Google 5 qubit random Hamiltonian"], "metrics": [], "task_id": "", "area": ""}, {"name": "Ensemble Learning", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["SMS Spam Collection Data Set"], "metrics": [], "task_id": "", "area": ""}, {"name": "Heart Rate Variability", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Drum Transcription", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Diabetic Retinopathy Grading", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["<PERSON><PERSON> EyePACS"], "metrics": [], "task_id": "", "area": ""}, {"name": "News Annotation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Network Community Partition", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Parking Space Occupancy", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["ACMPS", "Action-Camera Parking", "CNRPark+EXT", "SPKL", "PKLot"], "metrics": [], "task_id": "", "area": ""}, {"name": "Blocking", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Riddle Sense", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "High School Mathematics", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Professional Psychology", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON>", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["CWL EEG/fMRI Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Malware Clustering", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Malware Analysis", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Referring Expression", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["SQA3D"], "metrics": [], "task_id": "", "area": ""}, {"name": "Symbolic Regression", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Explainable Models", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Domain Recommender Systems", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Brain Decoding", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["Stanford ECoG library: ECoG to Finger Movements", "BCI Competition IV: ECoG to Finger Movements"], "metrics": [], "task_id": "", "area": ""}, {"name": "Website Fingerprinting Attacks", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["Website Traffic Data on Tor"], "metrics": [], "task_id": "", "area": ""}, {"name": "Plant Phenotyping", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Feedback Vertex Set (FVS)", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "MuJoCo Games", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["InvertedPendulum", "Walker2d-v3", "HalfCheetah", "Point Maze", "Humanoid-v3", "Humanoid-v2", "Swimmer", "<PERSON>", "HalfCHeetah-v3", "Ant"], "metrics": [], "task_id": "", "area": ""}, {"name": "Snow Removal", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Game of Sudoku", "children": [], "description": "", "dataset_count": 3, "benchmarks": ["Sudoku 9x9"], "metrics": [], "task_id": "", "area": ""}, {"name": "Hate Span Identification", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hallucination Evaluation", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Theory of Mind Modeling", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Anomaly Forecasting", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "LLM Jailbreak", "children": [], "description": "", "dataset_count": 3, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Deep Clustering", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["USPS", "Coil-20", "Stackoverflow", "MNIST", "Searchsnippets"], "metrics": [], "task_id": "", "area": ""}, {"name": "Hard-label Attack", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Nature-Inspired Optimization Algorithm", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["CIFAR-10", "MNIST"], "metrics": [], "task_id": "", "area": ""}, {"name": "TAG", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Interpretability Techniques for Deep Learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["CelebA", "CausalGym"], "metrics": [], "task_id": "", "area": ""}, {"name": "QQP", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Feature Upsampling", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Missing Elements", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Horizon Line Estimation", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["Horizon Lines in the Wild", "York Urban Dataset", "Eurasian Cities Dataset", "KITTI Horizon"], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON>se <PERSON>ing", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["Visual Genome"], "metrics": [], "task_id": "", "area": ""}, {"name": "Defocus Estimation", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["CUHK - Blur Detection Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "FLUE", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Steering Control", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Boundary Captioning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Boundary Grounding", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Domain-IL Continual Learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Provable Adversarial Defense", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Clean-label Backdoor Attack (0.05%)", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Test Agnostic Long-Tailed Learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "STS Benchmark", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Total Magnetization", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Continual Domain Shift Learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "MuJoCo", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Incremental Constrained Clustering", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Q-Learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Test-time Adaptation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cross-Modality Person Re-identification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Point Cloud Super Resolution", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["PU-GAN", "SHREC15"], "metrics": [], "task_id": "", "area": ""}, {"name": "Information Threading", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Deep Attention", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Authorship Verification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Accident Anticipation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Entity Embeddings", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Dominance Estimation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hierarchical Reinforcement Learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["Ant + Maze"], "metrics": [], "task_id": "", "area": ""}, {"name": "graph partitioning", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["custom"], "metrics": [], "task_id": "", "area": ""}, {"name": "Policy Gradient Methods", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Meta Reinforcement Learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Board Games", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Self-Supervised Person Re-Identification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Goal Reinforcement Learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["no extra data"], "metrics": [], "task_id": "", "area": ""}, {"name": "Clustering Ensemble", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Point Clouds", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["DTU", "Tanks and Temples"], "metrics": [], "task_id": "", "area": ""}, {"name": "Dial Meter Reading", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "No real Data Binarization", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Behavioural cloning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Relational Pattern Learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Dynamics", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Author Attribution", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ICU Mortality", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Popularity Forecasting", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON>ze Test", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["CodeXGLUE - CT-all", "CodeXGLUE - CT-maxmin"], "metrics": [], "task_id": "", "area": ""}, {"name": "Moment Queries", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Reinforcement Learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Linear evaluation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Story Completion", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Armed Bandits", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["Mushroom"], "metrics": [], "task_id": "", "area": ""}, {"name": "Molecule Captioning", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["ChEBI-20", "L+M-24"], "metrics": [], "task_id": "", "area": ""}, {"name": "Aggression Identification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Irony Identification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Moral Scenarios", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Computer Security", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Security Studies", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Agent Path Finding", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Record linking", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-agent Integration", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["BBAI Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "De-identification", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Domain Expansion", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Automated Essay Scoring", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["ASAP-AES"], "metrics": [], "task_id": "", "area": ""}, {"name": "Traffic Data Imputation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Overall - Test", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Variable Disambiguation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Embeddings Evaluation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multimodal Association", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Seeing Beyond the Visible", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["KITTI360-EX", "HYPERVIEW"], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON>", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Website Fingerprinting Defense", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["Website Traffic Data on Tor"], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON>", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["ValNov Subtask A", "ValNov Subtask B"], "metrics": [], "task_id": "", "area": ""}, {"name": "Load Forecasting", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Spatio-Temporal Forecasting", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Protein Annotation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "slot-filling", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Ethics", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["Ethics", "Ethics (per ethics)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Dynamic Point Removal", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["semi-indoor", "Argoverse 2"], "metrics": [], "task_id": "", "area": ""}, {"name": "PAIR TRADING", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Irregular Time Series", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Age and Gender Estimation", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["LAGENDA gender", "LAGENDA age"], "metrics": [], "task_id": "", "area": ""}, {"name": "Mathematical Problem-Solving", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sensor Fusion", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Negation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Judgment Correlation", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["Flickr8k-Expert", "Flickr8k-CF"], "metrics": [], "task_id": "", "area": ""}, {"name": "Kinship Verification", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["KinFaceW-I", "KinFaceW-II"], "metrics": [], "task_id": "", "area": ""}, {"name": "Position regression", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SAXS regression", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "XRD regression", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SANS regression", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ND regression", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Neutron PDF regression", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "NMT", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sports Analytics", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "RTE", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Lung Cancer Diagnosis", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Collision Avoidance", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["A Ball Collision Dataset (ABCD)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Modality completion", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hybrid Machine Learning", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Raindrop Removal", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "backdoor defense", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "User Simulation", "children": [], "description": "", "dataset_count": 2, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Fault Diagnosis", "children": [], "description": "", "dataset_count": 2, "benchmarks": ["Digital twin-supported deep learning for fault diagnosis"], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised MNIST", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Rotated MNIST", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Rotated MNIST"], "metrics": [], "task_id": "", "area": ""}, {"name": "Adversarial Defense against FGSM Attack", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Türkçe Görüntü Altyazılama", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "HairColor/Unbiased", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "HeavyMakeup/Unbiased", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Lexical Simplification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Gait Identification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Online Clustering", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Long-tail Learning on CIFAR-10-LT (ρ=100)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ROLSSL-Consistent", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ROLSSL-<PERSON><PERSON><PERSON>", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ROLSSL-Uniform", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Class Incremental Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Cifar100-B0(10 tasks)-no-exemplars", "CIFAR100-B0(50 tasks)-no-exemplars", "cifar100", "CIFAR-100 - 50 classes + 10 steps of 5 classes", "Cifar100-B0(20 tasks)-no-exemplars", "CIFAR-100 - 50 classes + 5 steps of 10 classes"], "metrics": [], "task_id": "", "area": ""}, {"name": "Non-exemplar-based Class Incremental Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Data Free Quantization", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Classifier calibration", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "class-incremental learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["cifar100"], "metrics": [], "task_id": "", "area": ""}, {"name": "Overlapped 100-50", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Overlapped 50-50", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Overlapped 100-10", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Overlapped 100-5", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Overlapped 25-25", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "NMR J-coupling", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Race/Unbiased", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Age/Unbiased", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Surgical Skills Evaluation", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["JIGSAWS", "MISTIC-SIL"], "metrics": [], "task_id": "", "area": ""}, {"name": "Outdoor Light Source Estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Materials Screening", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cadenza 1 - Task 1 - Headphone", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Parallel Corpus Mining", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Story Continuation", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["PororoSV", "VIST", "FlintstonesSV"], "metrics": [], "task_id": "", "area": ""}, {"name": "NetHack Score", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "MS-SSIM", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Local Distortion", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["DocUNet"], "metrics": [], "task_id": "", "area": ""}, {"name": "SENTS", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "MORPH", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "UNLABELED_DEPENDENCIES", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "LABELED_DEPENDENCIES", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "LEMMA", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Informativeness", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "COVID-19 Modelling", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Question Rewriting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "HTR", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "GPR", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON>'s Revenge", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cadenza 1 - Task 2 - In Car", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Overlapped 10-1", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Domain 11-5", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Domain 11-1", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Domain 1-1", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Overlapped 14-1", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "CARLA MAP Leaderboard", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "CARLA longest6", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["CARLA"], "metrics": [], "task_id": "", "area": ""}, {"name": "CARLA Leaderboard 2.0", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Uncropping", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Places2 val"], "metrics": [], "task_id": "", "area": ""}, {"name": "Distributed Computing", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Game of Doom", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Continuous Control (100k environment steps)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Continuous Control (500k environment steps)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SNES Games", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "D4RL", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON>-random", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON>-medium", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON>-expert", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON>-medium-expert", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Gym half<PERSON><PERSON>h-medium-replay", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>-full-replay", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Adroid pen-human", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Adroid hammer-human", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Adroid door-human", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Adroid relocate-human", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Adroid pen-cloned", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Ad<PERSON> hammer-cloned", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Adroid door-cloned", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON> relocate-cloned", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Decipherment", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "TGIF-Transition", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "TGIF-Frame", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ListOps", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Diffusion Personalization Tuning Free", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Epidemiology", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Demosaicking", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Probabilistic Time Series Forecasting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Metaheuristic Optimization", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Polyphone disambiguation", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["CPP"], "metrics": [], "task_id": "", "area": ""}, {"name": "Sensor Modeling", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multivariate Time Series Forecastingm", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Game of Hanabi", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Two-sample testing", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["MNIST vs Fake MNIST", "HDGM (d=10, N=4000)", "HIGGS Data Set", "Blob (9 modes, 40 for each)", "CIFAR-10 vs CIFAR-10.1 (1000 samples)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Lexical Normalization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["LexNorm"], "metrics": [], "task_id": "", "area": ""}, {"name": "Period Estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["OmniArt"], "metrics": [], "task_id": "", "area": ""}, {"name": "OrangeSum", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Lake Ice Monitoring", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Clean-label Backdoor Attack (0.024%)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Micro-Expression Spotting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Game of Football", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Replay Grounding", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Caricature", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Split and Rephrase", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "metrics": [], "task_id": "", "area": ""}, {"name": "Geophysics", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Precipitation Forecasting", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["SEVIR"], "metrics": [], "task_id": "", "area": ""}, {"name": "Noun Phrase Canonicalization", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hypernym Discovery", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Music domain", "Medical domain", "General"], "metrics": [], "task_id": "", "area": ""}, {"name": "Col BERTTrip<PERSON>", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "point of interests", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Populist attitude", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Phone-level pronunciation scoring", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Utterance-level pronounciation scoring", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Non-Intrusive Load Monitoring", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Time Series Averaging", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Quantum Machine Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["https://www.kaggle.com/datasets/saurab<PERSON>hahane/classification-of-malwares", "iris"], "metrics": [], "task_id": "", "area": ""}, {"name": "N-Queens Problem - All Possible Solutions", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Traveling Salesman Problem", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Choice MRC", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Graphon Estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Clinical Note Phenotyping", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Computational Phenotyping", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Propaganda technique identification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Synthetic Data Evaluation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Mobile Security", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Stable MCI vs Progressive MCI", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Body Volume Estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Causal Identification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "HumanEval", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "MTEB Benchmark", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "AllNLI Triplet", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Question-Answer categorization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["QC-Science"], "metrics": [], "task_id": "", "area": ""}, {"name": "EditCompletion", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["C# EditCompletion"], "metrics": [], "task_id": "", "area": ""}, {"name": "Node Regression", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Algorithmic Trading", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Patient Phenotyping", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["HiRID"], "metrics": [], "task_id": "", "area": ""}, {"name": "Circulatory Failure", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["HiRID"], "metrics": [], "task_id": "", "area": ""}, {"name": "Respiratory Failure", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["HiRID"], "metrics": [], "task_id": "", "area": ""}, {"name": "Remaining Length of Stay", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Task 2", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Problem-Solving Deliberation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Classify murmurs", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "reinforcement-learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Capacity Estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SMAC", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["SMAC MMM2", "SMAC 6h_vs_8z", "SMAC MMM2_7m2M1M_vs_9m3M1M", "SMAC 3s5z_vs_4s6z", "SMAC 6h_vs_9z", "SMAC MMM2_7m2M1M_vs_8m4M1M", "SMAC 27m_vs_30m", "SMAC 3s5z_vs_3s6z", "SMAC corridor_2z_vs_24zg", "SMAC 26m_vs_30m"], "metrics": [], "task_id": "", "area": ""}, {"name": "Moving Point Cloud Processing", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Fine-Grained Facial Editing", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "VGSI", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Safe Reinforcement Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "GSM8K", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["gsm8k (5-shots)", "GSM8K"], "metrics": [], "task_id": "", "area": ""}, {"name": "DrugProt", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Galaxy emergent property recreation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "BBBC021 NSC Accuracy", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "CYCLoPs Accuracy", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multimodal GIF Dialog", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Success Rate (5 task-horizon)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Avg. sequence length", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "BIG-bench Machine Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["38-<PERSON>", "BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Identify Odd Metapor", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Odd One Out", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Crash Blossom", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Auto Debugging", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Big-bench <PERSON><PERSON>"], "metrics": [], "task_id": "", "area": ""}, {"name": "Crass AI", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Empirical Judgments", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Timedial", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Business Ethics", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Moral Disputes", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Moral Permissibility", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "FEVER (2-way)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "FEVER (3-way)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Misconceptions", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "High School European History", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "High School US History", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "High School World History", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "International Law", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Jurisprudence", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Management", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Marketing", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Philosophy", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Prehistory", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Professional Law", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "World Religions", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Entailed Polarity", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Evaluating Information Essentiality", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON>", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Physical Intuition", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Presuppositions As NLI", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Professional Accounting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Anatomy", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "College Medicine", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Aging", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Organs Senses Multiple Choice", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Nutrition", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Professional Medicine", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Virology", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "English Proverbs", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Implicatures", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Implicit Relations", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "LAMBADA", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Movie Dialog Same Or Different", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Phrase Relatedness", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "RACE-h", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "RACE-m", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "College Biology", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "College Chemistry", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "College Computer Science", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "College Physics", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Conceptual Physics", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "High School Biology", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "High School Chemistry", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "High School Computer Science", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "High School Physics", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "High School Statistics", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Physics MC", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Econometrics", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "High School Geography", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "High School Government and Politics", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "High School Macroeconomics", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "High School Microeconomics", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "High School Psychology", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Sexuality", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Public Relations", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Sociology", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "US Foreign Policy", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BIG-bench"], "metrics": [], "task_id": "", "area": ""}, {"name": "Sleep Staging", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "W-R-L-D Sleep Staging", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "W-R-N Sleep Staging", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Atari Games 100k", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "fr-en", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "es-en", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "de-en", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "EEG 4 classes", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "EEG Left/Right hand", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "EM showers clusterization", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Topic coverage", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Single Choice Question", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Toponym Resolution", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "RoomEnv-v0", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Surveillance-to-Single", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Surveillance-to-Booking", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Surveillance-to-Surveillance", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ECG Wave Delineation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "TDC ADMET Benchmarking Group", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SpO2 estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Time Offset Calibration", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Mental Workload Estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Industrial Robots", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Singer Identification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Skill Mastery", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["RGB-Stacking"], "metrics": [], "task_id": "", "area": ""}, {"name": "Binary Quantification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Spatial Clustering", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Privacy Preserving", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Log Solubility", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["ESOL"], "metrics": [], "task_id": "", "area": ""}, {"name": "Row Annotation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Morpheme Segmentaiton", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["UniMorph 4.0"], "metrics": [], "task_id": "", "area": ""}, {"name": "Chemical Indexing", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["BC7 NLM-Chem"], "metrics": [], "task_id": "", "area": ""}, {"name": "Virtual Try-Off", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Playing the Game of 2048", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["The Game of 2048"], "metrics": [], "task_id": "", "area": ""}, {"name": "Lexical Analysis", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Negation Scope Resolution", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Speculation Scope Resolution", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Negation and Speculation Scope resolution", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Exemplar-Free Counting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "A-VB Two", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "A-VB High", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "A-VB Culture", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Open Relation Modeling", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Game of Go", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["ELO Ratings"], "metrics": [], "task_id": "", "area": ""}, {"name": "Holdout Set", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["xView3-SAR"], "metrics": [], "task_id": "", "area": ""}, {"name": "Humanitarian", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Descriptive", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Remove - PQ", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Remove - PO", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Replace - PQ", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Replace - PO", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Add - PQ", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Add - PO", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Specificity", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Partially Observable Reinforcement Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Model-based Reinforcement Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Definition Modelling", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vocal ensemble separation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Navigate", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "RoomEnv-v1", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Card Games", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "FocusNews (test)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SRF (test)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Protein Folding", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Driver Identification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Attention", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Film Simulation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Acne Severity Grading", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["ACNE04"], "metrics": [], "task_id": "", "area": ""}, {"name": "Subgraph Counting - K4", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Subgraph Counting - Triangle", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Subgraph Counting - 3 Star", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Subgraph Counting - Chordal C4", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Subgraph Counting - 2 star", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Subgraph Counting - C4", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Subgraph Counting - Tailed Triangle", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Subgraph Counting - C5", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Subgraph Counting - C6", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Subgraph Counting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hurricane Forecasting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "All", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Diagnostic", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sub-diagnostic", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Super-diagnostic", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Form", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "VCGBench-Diverse", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["VideoInstruct"], "metrics": [], "task_id": "", "area": ""}, {"name": "Operator learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Solar Irradiance Forecasting", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["ASOS Data"], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON>", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "XLM-R", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Bayesian Optimization", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Bug fixing", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Travel Time Estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Automatic Lyrics Transcription", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Virtual Try-on (Shop2Street)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Virtual Try-on (Model2Street)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Virtual Try-on (Street2Street)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vignetting Removal", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Model Editing", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SVBRDF Estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "TAR", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "tabular-regression", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Mixed Reality", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Self-Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Joint Radar-Communication", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "EEG", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multiclass Quantification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Neural Network Security", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Long Term Anticipation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "point cloud upsampling", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Film Removal", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Citation worthiness", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Omniverse <PERSON>", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Low-latency processing", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hypergraph Contrastive Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Semi-Supervised Person Re-Identification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Domain Adaptive Person Re-Identification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "MMLU", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["mmlu (5-shots)", "MMLU-Pro", "mmlu (chat CoT)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Hallucination", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["MMNeedle"], "metrics": [], "task_id": "", "area": ""}, {"name": "Person Identification (1-shot)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Fashion Compatibility Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "4k", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Dataset Size Recovery", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sequential Pattern Mining", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Layout Design", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "RoomEnv-v2", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vietnamese Datasets", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Game of Poker", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "MMR total", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["MRR-Benchmark"], "metrics": [], "task_id": "", "area": ""}, {"name": "GermEval2024 Shared Task 1 Subtask 1", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["GerMS-AT"], "metrics": [], "task_id": "", "area": ""}, {"name": "GermEval2024 Shared Task 1 Subtask 2", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["GerMS-AT"], "metrics": [], "task_id": "", "area": ""}, {"name": "Occlusion Estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "TiROD", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Flood extent forecasting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "News Authenticity Identification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Li-ion State of Health Estimation", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["NASA Li-ion Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Chemical Process", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "software testing", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "erson Re-Identification", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Demand Forecasting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Neural Network simulation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Liquid Simulation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Equilibrium traffic assignment", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Authorship Attribution", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Author Profiling", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "MMSQL performance", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Task Planning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Quant Trading", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Raspberry Pi 3", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Raspberry Pi 4", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Raspberry Pi 5", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Crashworthiness Design", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ECG Digitization", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["ECG-Image-Database"], "metrics": [], "task_id": "", "area": ""}, {"name": "Sand", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sequential Decision Making", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Credit score", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Load Virtual Sensing", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Load Virtual Sensing (Fx)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Load Virtual Sensing (Fy)", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Red Teaming", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["SUDO Dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Real-World Adversarial Attack", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Atomic Forces", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Field Boundary Delineation", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "LLM real-life tasks", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Fixed Few Shot Prompting Danger Assessment", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Zero Shot Prompting Danger Assessment", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Fixed Few Shot Prompting", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Building Damage Assessment", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "automatic short answer grading", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Causal Judgment", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Deep Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Procedure Learning", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Scheduling", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Game of Mafia", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Asynchronous Group Communication", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Building Flood Damage Assessment", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "2k", "children": [], "description": "", "dataset_count": 1, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Reinforcement Learning (Atari Games)", "children": [], "description": "", "dataset_count": 1, "benchmarks": ["Seaquest - OpenAI Gym"], "metrics": [], "task_id": "", "area": ""}, {"name": "Foveation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cancer", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Data Mining", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "De<PERSON>zing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Forgery", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Write Computer Programs From Specifications", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Clustering Multivariate Time Series", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Chinese", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Ecommerce", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sequential Diagnosis", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Patient Outcomes", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Mammogram", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Ecg Risk Stratification", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["ngm"], "metrics": [], "task_id": "", "area": ""}, {"name": "Atrial Fibrillation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Advertising", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hand", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Remote Sensing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Molecule Interpretation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Tomography", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Magnetic Resonance Fingerprinting", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Animation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Bilevel Optimization", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Equilibrium-Traffic-Networks/Anaheim", " Equilibrium-Traffic-Networks/Eastern Massachusetts", "\tEquilibrium-Traffic-Networks/Sioux Falls"], "metrics": [], "task_id": "", "area": ""}, {"name": "Dynamic graph embedding", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Tensor Networks", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Portfolio Optimization", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Yahoo "], "metrics": [], "task_id": "", "area": ""}, {"name": "hypergraph embedding", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Intelligent Surveillance", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Pain Intensity Regression", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["UNBC-McMaster ShoulderPain dataset"], "metrics": [], "task_id": "", "area": ""}, {"name": "Safe Exploration", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Game of Chess", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Game of Shogi", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["ELO Ratings"], "metrics": [], "task_id": "", "area": ""}, {"name": "FPS Games", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Network Congestion Control", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Artificial Life", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Skull Stripping", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Distributional Reinforcement Learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cryptanalysis", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Learning to Execute", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Outdoor Positioning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Materials Imaging", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Query Wellformedness", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Query Wellformedness"], "metrics": [], "task_id": "", "area": ""}, {"name": "Automatic Writing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Bayesian Optimisation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Radio Interferometry", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Seismic Interpretation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Steganographics", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Problem Decomposition", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Non-Linear Elasticity", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Metamerism", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Web Credibility", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Observation Completion", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Neural Network", "children": [], "description": "", "dataset_count": 0, "benchmarks": [".."], "metrics": [], "task_id": "", "area": ""}, {"name": "hypergraph partitioning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Game of Cricket", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Transparency Separation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Molecular Dynamics", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Data Poisoning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "RDF Dataset Discovery", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Lightfield", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "L2 Regularization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Automated Writing Evaluation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Landmine", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Meme Captioning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Amodal Layout Estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cover song identification", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Da-TACOS", "YouTube350", "Covers80", "SHS100K-TEST"], "metrics": [], "task_id": "", "area": ""}, {"name": "One-shot model fusion", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Information Plane", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Mutual Information Estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Misogynistic Aggression Identification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "NetHack", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Blink estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Researcher's Night", "Eyeblink8", "RT-BENE"], "metrics": [], "task_id": "", "area": ""}, {"name": "Spatial Interpolation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hypergraph representations", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "quantum gate design", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sequential Quantile Estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Re-basin", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Quantum Circuit Equivalence Checking", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "JSONiq Query Execution", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cyber Attack Investigation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Population Assignment", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Survey Sampling", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Influence Approximation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Robust Design", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Ensemble Pruning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "statistical independence testing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "band gap regression", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sequential Correlation Estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Making Hiring Decisions", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["SIOP 2020/2021"], "metrics": [], "task_id": "", "area": ""}, {"name": "Survey", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Domain Labelling", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["BabelDomains"], "metrics": [], "task_id": "", "area": ""}, {"name": "Cervical cancer biopsy identification", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Cervical Cancer (Risk Factors) Data Set"], "metrics": [], "task_id": "", "area": ""}, {"name": "Breast Tissue Identification", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Breast Tissue Data Set"], "metrics": [], "task_id": "", "area": ""}, {"name": "Sparse subspace-based clustering", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Turning Point Identification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Edge-computing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Neural Stylization", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Meshes"], "metrics": [], "task_id": "", "area": ""}, {"name": "Temporal Processing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Taxonomy Learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Pulse wave simulation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Formalize foundations of universal algebra in dependent type theory", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Data Ablation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Numerical Integration", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Tensor Decomposition", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Pronunciation Assessment", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Dynamic Time Warping", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Exponential degradation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Im2Spec", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "BRDF estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Stochastic Block Model", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Emergent communications on relations", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Reliable Intelligence Identification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Unity", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Service Composition", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Learning Theory", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Riemannian optimization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Model Discovery", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Spike Sorting", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Additive models", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Density Ratio Estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Experimental Design", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "FAD", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hard Attention", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Literature Mining", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Superpixels", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Anachronisms", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "DNN Testing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Side Channel Analysis", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Variational Monte Carlo", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Graph Nonvolutional Network", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Majority Voting Classifier", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Neural <PERSON>ng", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Facial Editing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Tropical Cyclone Track Forecasting", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Tropical Cyclone Intensity Forecasting", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Procgen Hard (100M)", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SMC会议", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ARQMath2", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Nonparametric Clustering", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Seismic Inversion", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Seismic Imaging", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Suggestion mining", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Geometry-aware processing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Second-order methods", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "PSO-ConvNets Dynamics 1", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "PSO-ConvNets Dynamics 2", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "PAC learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Maximum Separation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Radar waveform design", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Dataset Condensation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Weight Space Learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Network Interpretation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "File difference", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Respiratory Rate Estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Molecular Docking", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Penn Machine Learning Benchmark (Real-World)", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Deep Feature Inversion", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Computational fabrication", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Stability-aware design", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Brain Morphometry", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON>'s <PERSON><PERSON>", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON>", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Federated Unsupervised Learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Humanoid Control", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Spatial Token Mixer", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Steganaly<PERSON>", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "highlight removal", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Job Shop Scheduling", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Li-ion battery degradation modes diagnosis", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Battery diagnosis", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Penn Machine Learning Benchmark", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Real-world Datasets"], "metrics": [], "task_id": "", "area": ""}, {"name": "Compiler Optimization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Data Valuation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Efficient Neural Network", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Eikonal Tomography", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Tree Decomposition", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Weakly-supervised Learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Result aggregation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "quantum gate calibration", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Ontology Subsumption Inferece", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Ontology Embedding", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Model free quantum gate design", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Multi-Modal Learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Joint Deblur and Frame Interpolation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Joint Deblur and Unrolling", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Sensitivity", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Unsupervised Long Term Person Re-Identification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Collaborative Plan Acquisition", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "DNA analysis", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Relation Network", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "MonkeyPox Diagnosis", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Cloud Computing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "molecular representation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Self Adaptive System", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Constrained Clustering", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Linguistic steganography", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Hearing Aid and device processing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "NWP Post-processing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Persuasiveness", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Diffusion Personalization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Geometry Problem Solving", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Artificial Global Workspace", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Atomistic Description", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "de novo peptide sequencing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "WNLI", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "GPS Embeddings", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Geo-Tagged NUS-WIDE (GPS + Visual)", "Geo-Tagged NUS-WIDE (GPS Only)"], "metrics": [], "task_id": "", "area": ""}, {"name": "Partially Labeled Datasets", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Pseudo Label Filtering", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Non-Adversarial Robustness", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "C++ code", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Discrete Choice Models", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Machine Unlearning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Pre-Fine-Tuning Weight Recovery", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "nlg evaluation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Computational Efficiency", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Plant village"], "metrics": [], "task_id": "", "area": ""}, {"name": "6D Vision", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Satellite Orbit Determination", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Signal Processing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Feature Correlation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Single Particle Analysis", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Earth Observation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ERP", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "SSVEP", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Financial Analysis", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Portrait Animation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Diversity", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Therapeutics Data Commons", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Dataset Distillation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Network Identification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Wrong PDF attached", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vietnamese Multimodal Learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Ingenuity", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Deep imbalanced regression", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Local intrinsic dimension estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Surrogate Hydrodynamic Modeling", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Part-based Representation Learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Assortment Optimization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Test Case Creation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Disjoint 19-1", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["PASCAL VOC 2012"], "metrics": [], "task_id": "", "area": ""}, {"name": "Pupil Diameter Estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Mammographic Breast Positioning Assessment", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ARC", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "HellaSwag", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Student dropout", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Crash injury severity", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "<PERSON><PERSON><PERSON>rov-Arnold Networks", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "test driven development", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Molecular geometry optimization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Computational chemistry", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "rllib", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "scientific discovery", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Missing Values", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Vietnamese Lexical Normalization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Traffic Signal Control", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "(deleted task 2)", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "DFT Z isomer pi-pi* wavelength", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Mamba", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "State Space Models", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Political evalutation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "User Identification", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "scoring rule", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Minecraft", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "4K 60Fps", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Model Optimization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "LMM real-life tasks", "children": [], "description": "", "dataset_count": 0, "benchmarks": ["Leaderboard"], "metrics": [], "task_id": "", "area": ""}, {"name": "Drug Design", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "dambreak", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ArabicMMLU", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Autonomous Racing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Geometry-based operator learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Dynamic neural networks", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "model", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "iFun", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "global-optimization", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Data Driven Optimal Control", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "spatio-temporal extrapolation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "陕西、四川、重庆地区2017-2022年末人口数（如图1-7所示）中，陕西省人口数较稳 定，维持在3940万人，四川和重庆均呈上升趋势。", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ICU Admission", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "input filtering", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Fitting", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Self-Evolving AI", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Integrated sensing and communication", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "ISAC", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "parameter estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "tensor algebra", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "compressed sensing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "subspace methods", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Lifelong learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Exemplar-Free", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Prompt Learning", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Mixture-of-Experts", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "OpenAI Vision", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Lightweight Deployment", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "BraTS2021", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Weather Editing", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "State Estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "PHMbench", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Terrain Estimation", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Human Agent Collaboration", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}, {"name": "Diverse Top-k Subgroup List Discovery", "children": [], "description": "", "dataset_count": 0, "benchmarks": [], "metrics": [], "task_id": "", "area": ""}], "total_tasks": 1043}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Hierarchy Viewer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .stats {
            display: flex;
            gap: 30px;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .search-box {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .button-primary {
            background-color: #007bff;
            color: white;
        }
        .button-primary:hover {
            background-color: #0056b3;
        }
        .button-secondary {
            background-color: #e0e0e0;
            color: #333;
        }
        .button-secondary:hover {
            background-color: #d0d0d0;
        }
        .task-tree {
            font-size: 14px;
        }
        .tree-node {
            margin: 2px 0;
        }
        .tree-node-content {
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background-color 0.2s;
        }
        .tree-node-content:hover {
            background-color: #f0f0f0;
        }
        .tree-node-content.selected {
            background-color: #e3f2fd;
        }
        .tree-node-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .tree-toggle {
            display: inline-block;
            width: 20px;
            text-align: center;
            color: #666;
            user-select: none;
        }
        .tree-children {
            margin-left: 30px;
            display: none;
        }
        .tree-children.expanded {
            display: block;
        }
        .dataset-count {
            background-color: #f0f0f0;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: #666;
        }
        .task-details {
            position: fixed;
            right: 20px;
            top: 20px;
            width: 400px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            padding: 20px;
            display: none;
            max-height: 80vh;
            overflow-y: auto;
        }
        .task-details.visible {
            display: block;
        }
        .task-details h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
        }
        .task-details .close-btn {
            position: absolute;
            right: 10px;
            top: 10px;
            cursor: pointer;
            font-size: 20px;
            color: #666;
        }
        .task-details .detail-item {
            margin-bottom: 15px;
        }
        .task-details .detail-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }
        .task-details .detail-value {
            color: #333;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 2px;
        }
        .tabs {
            display: flex;
            border-bottom: 2px solid #e0e0e0;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        .tab:hover {
            background-color: #f0f0f0;
        }
        .tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
            font-weight: bold;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        @media (max-width: 768px) {
            .task-details {
                position: static;
                width: 100%;
                margin-top: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 id="page-title">Task Hierarchy Viewer</h1>
            <select id="domain-selector" class="button button-secondary">
                <option value="">Select Domain</option>
                <option value="cv">Computer Vision</option>
                <option value="nlp">Natural Language Processing</option>
                <option value="audio">Audio Processing</option>
                <option value="other">Other Domains</option>
            </select>
        </div>
        
        <div class="stats" id="stats">
            <div class="stat-item">
                <div class="stat-value" id="total-tasks">0</div>
                <div class="stat-label">Total Tasks</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="hierarchical-tasks">0</div>
                <div class="stat-label">Hierarchical Tasks</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="standalone-tasks">0</div>
                <div class="stat-label">Standalone Tasks</div>
            </div>
        </div>
        
        <div class="controls">
            <input type="text" class="search-box" id="search-box" placeholder="Search tasks...">
            <button class="button button-primary" id="expand-all-btn">Expand All</button>
            <button class="button button-secondary" id="collapse-all-btn">Collapse All</button>
        </div>
        
        <div class="tabs">
            <div class="tab active">Hierarchical Tasks</div>
            <div class="tab">Standalone Tasks</div>
        </div>
        
        <div class="tab-content active" id="hierarchical-content">
            <div class="task-tree" id="hierarchical-tree"></div>
        </div>
        
        <div class="tab-content" id="standalone-content">
            <div class="task-tree" id="standalone-tree"></div>
        </div>
    </div>
    
    <div class="task-details" id="task-details">
        <span class="close-btn">&times;</span>
        <h3 id="task-name"></h3>
        <div class="detail-item">
            <div class="detail-label">Dataset Count</div>
            <div class="detail-value" id="task-dataset-count"></div>
        </div>
        <div class="detail-item">
            <div class="detail-label">Description</div>
            <div class="detail-value" id="task-description"></div>
        </div>
        <div class="detail-item">
            <div class="detail-label">Benchmarks</div>
            <div class="detail-value" id="task-benchmarks"></div>
        </div>
        <div class="detail-item">
            <div class="detail-label">Metrics</div>
            <div class="detail-value" id="task-metrics"></div>
        </div>
    </div>
    
    <!-- Load modular JavaScript files -->
    <script src="js/task-data-loader.js"></script>
    <script src="js/task-tree-renderer.js"></script>
    <script src="js/task-details-viewer.js"></script>
    <script src="js/task-viewer-app.js"></script>
</body>
</html>
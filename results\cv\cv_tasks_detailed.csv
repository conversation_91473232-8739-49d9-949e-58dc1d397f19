Task Name,Dataset Count,Common Datasets (Top 10),Benchmarks,SOTA Metrics,Subtasks,Categories,Description (First 200 chars)
Semantic Segmentation,348,Matterport3D; Lost and Found; Photi-LakeIce; FoodSeg103; Fashionpedia; ATLANTIS; OmniCity; RobotPush; PETRAW; KvasirCapsule-SEG,Matterport3D; UrbanLF; SYN-UDTIRI; dacl10k v1 testfinal; UAVid; FoodSeg103; CEMS-W; ATLANTIS; DeLiVER test; BDD100K val,FPS; Mean IoU (test); Dice; Params (M); Average F1; A-acc; μ; Category iIoU; IoU; Mean Accuracy,Text-Line Extraction; Real-Time Semantic Segmentation; UNET Segmentation; Bird's-Eye View Semantic Segmentation; Semi-Supervised Semantic Segmentation,Robots; Medical; Computer Vision; Computer Code,N/A
Object Detection,333,Boreal Forest Fire; AppleBBCH81; Visual 3D shape matching dataset; FOD-A; SPair-71k; SSL; A Dataset of Multispectral Potato Plants Images; CornHub; Hyper-Kvasir Dataset; cranfield-synthetic-drone-detection,; GRAZPEDWRI-DX; VisDrone-DET2019; COCO test-dev; SIXray; BDD100K val; PASCAL Part 2010 - Animals; EventPed; VisDrone- 1% labeled data; A Dataset of Multispectral Potato Plants Images,; mAP w/o OOD; mMR; Operations per network pass; APM; AP@0.5; Average IOU; mAP@0.5:0.95; APt; mAP@0.75,Malaria Ovale Detection; Open Vocabulary Object Detection; License Plate Detection; Medical Object Detection; Moving Object Detection,Computer Vision,N/A
Image Classification,283,Galaxy Zoo DECaLS; Weapon Detection Dataset; Kuzushiji-MNIST; BAM!; OFDIW; AppleScabFDs; ImageNet-Hard; ColonINST-v1 (Unseen); ColonINST-v1; MuMiN-medium,"Kuzushiji-MNIST; CIFAR-100, 40% Symmetric Noise; ImageNet-Hard; KITTI-Dist; Fracture/Normal Shoulder Bone X-ray Images on MURA; ColonINST-v1 (Unseen); ObjectNet (ImageNet classes); finetuned-websites; Flowers (Tensorflow); CUB-200-2011",Operations per network pass; Overall; Test f1; Test F1 score; PARAMS (M); ImageNet Top-5 Accuracy; Total Accuracy; Percentage Average accuracy - 5 tasks; Epochs; Eval F1,Satellite Image Classification; Artistic style classification; Misclassification Rate - Natural Adversarial Samples; Multi-Label Image Recognition; Small Data Image Classification,Adversarial; Computer Vision,**Image Classification** is a fundamental task in vision recognition that aims to understand and categorize an image as a whole under a specific label. Unlike [object detection](/task/object-detection...
Visual Question Answering (VQA),145,VQA-CE; VisCon-1M; TDIUC; TextKVQA; MM-Vet; FigureQA; CLEVR-Humans; Visual Question Answering v2.0; ZS-F-VQA; Conceptual Captions,TextVQA test-standard; VQA-CE; GQA test-std; A-OKVQA; GRIT; VQA-CP; QLEVR; WebSRC; TDIUC; VQA v2 val,Reasoning (Mea.); PC-cpr; Reasoning (Spa.); Binary; Reasoning (Geo.); PC-grp; Exact Match (EM); Abductive; GC-mat; Open,Chart Question Answering; Embodied Question Answering; Generative Visual Question Answering; 3D Question Answering (3D-QA); Factual Visual Question Answering,Computer Vision; Natural Language Processing,**Visual Question Answering (VQA)** is a task in computer vision that involves answering questions about an image. The goal of VQA is to teach machines to understand the content of an image and answer...
Named Entity Recognition (NER),130,i2b2 De-identification Dataset; BB; COVID-Q; BC7 NLM-Chem; BC5CDR; FindVehicle; PhoNER COVID19; Europeana Newspapers; Dataset of Legal Documents; Polyglot-NER,LINNAEUS; IECSIL FIRE-2018 Shared Task; WetLab; i2b2 De-identification Dataset; SLUE; NCBI-disease; CoNLL 2000; OntoNotes 5.0; BC7 NLM-Chem; UNER v1 - PUD (Swedish),Average F1; label-F1 (%); Micro F1; Multi-Task Supervision; F1-score (strict); F1-score (Weighted); Micro F1 (Exact Span); Avg F1; Entity F1; F1 (surface form),Medical Named Entity Recognition; Multi-Grained Named Entity Recognition; Multi-modal Named Entity Recognition; Multilingual Named Entity Recognition; Cross-Domain Named Entity Recognition,Natural Language Processing,"**Named Entity Recognition (NER)** is a task of Natural Language Processing (NLP) that involves identifying and classifying named entities in a text into predefined categories such as person names, or..."
Pose Estimation,124,GazeFollow; H3WB; MPII; HUMAN4D; MoVi; NCLT; IKEA ASM; RealArt-6; VBR; KeypointNet, ITOP front-view; MERL-RAV; COCO test-dev; MPII; 300W (Full); 3DPW; ITOP top-view; MPII Single Person; MS-COCO; DensePose-COCO,"0..5sec; Percentage correct; DUC2-Acc@0.5m,10°; AP Easy; Average End-Point Error; APM; AR50; Acceleration Error; MAE pitch (º); Mean PCK@0.1",Hand Pose Estimation; 6D Pose Estimation; 6D Pose Estimation using RGB; Semi-supervised 2D and 3D landmark labeling; 3D Pose Estimation,Computer Vision,"**Pose Estimation** is a computer vision task where the goal is to detect the position and orientation of a person or an object. Usually, this is done by predicting the location of specific keypoints ..."
Anomaly Detection,120,ISP-AD; UBI-Fights; Lost and Found; DARPA; SensumSODF; SWAT A7; SPOT-10; UCSD Ped2; Fashion-MNIST; Forest CoverType,KSDD2; Numenta Anomaly Benchmark; Anomaly Detection on Unlabeled ImageNet-30 vs CUB-200; Lost and Found; Unlabeled CIFAR-10 vs CIFAR-100; PAD Dataset; One-class CIFAR-10; Fishyscapes L&F; NB15-Backdoor; UBnormal,Segmentation AU-sPRO (until FPR 5%); FPS; Object AUROC; Average F1; Avg. ROC-AUC; ROC AUC; FPR; Network; AUC; AUC-PRO,Damaged Tissue Detection; One-Class Classification; Image Manipulation Detection; Depth + RGB Anomaly Segmentation; 3D + RGB Anomaly Detection,Miscellaneous; Methodology; Computer Vision; Graphs,"**Anomaly Detection** is a binary classification identifying unusual or unexpected patterns in a dataset, which deviate significantly from the majority of the data. The goal of anomaly detection is to..."
Action Recognition,115,HowTo100M; MECCANO; FineGym; TUM Kitchen; EPIC-KITCHENS-100; PETRAW; HACS; Kinetics-Sound; THUMOS14; UAV-GESTURE,HAA500; AVA v2.1; MECCANO; RareAct; NTU RGB+D 120; BAR; THUMOS’14; VIRAT Ground 2.0; H2O  (2 Hands and Objects); EPIC-KITCHENS-100,Top-5 Accuracy; No. of Somersaults Accuracy; Accuracy (Cross-Setup); Params (M); mAP@0.5; Accuracy (Cross-View); Top-5; F-measure (%); Object Label; mAP@0.4,3D Action Recognition; Weakly-Supervised Action Recognition; Action Triplet Recognition; Action Recognition In Videos; Open Vocabulary Action Recognition,Time Series; Computer Vision,**Action Recognition** is a computer vision task that involves recognizing human actions in videos or images. The goal is to classify and categorize the actions being performed in the video or image i...
Instance Segmentation,111,GraspClutter6D; Fashionpedia; SIXray; TrashCan; UVO; OmniCity; RobotPush; Hypersim; NDD20; OCID,coco minval; NYUDv2-IS; nuScenes; KINS; COCO test-dev; COCO; UAVBillboards; COCO-N Medium; UFBA-425; BDD100K val,mAP@0.5:0.95:0.05; Params (M); mAP@0.5; APM; mAP50; box AP; Dice Coef; MOTA; Dice Scoe; APS,3D Instance Segmentation; Human Instance Segmentation; Solar Cell Segmentation; One-Shot Instance Segmentation; Semi-Supervised Person Instance Segmentation,Computer Vision,"**Instance Segmentation** is a computer vision task that involves identifying and separating individual objects within an image, including detecting the boundaries of each object and assigning a uniqu..."
Speech Recognition,96,SPEECH-COCO; Libri-Light; AliMeeting; SMS-WSJ; ClovaCall; OLR 2021; TIMIT; ADIMA; 2000 HUB5 English; RESD,Podlodka.io; Common Voice Chuvash; swb_hub_500 WER fullSWBCH; Common Voice 7.0 Odia; Libri-Light test-other; Common Voice 8.0 Central Kurdish; Common Voice Latvian; projecte-aina/parlament_parla ca; Common Voice 7.0 Portuguese; Common Voice 8.0 German,Test CER; WER (%); SwitchBoard; VoxPopuli (Dev); Hub5'00; Word Error Rate (WER); Test WER; VoxCeleb (Dev); WER; Character Error Rate (CER),Automatic Speech Recognition (ASR); Visual Speech Recognition; Distant Speech Recognition; Sequence-To-Sequence Speech Recognition; English Conversational Speech Recognition,Speech; Audio,**Speech Recognition** is the task of converting spoken language into text. It involves recognizing the words spoken in an audio recording and transcribing them into a written format. The goal is to a...
Information Retrieval,93,Robust04; i2b2 De-identification Dataset; ArCOV-19; ASNQ; TutorialBank; MuMu; ReQA; PerKey; MQ2008; Persian Reverse Dictionary Dataset,TREC-PM; dim 64; GermanQuAD; Unknown; Evaluate; News Headlines; dim 512; CQADupStack; dim 768; NanoMSMARCO,infNDCG; Recall@200; Recall@100; mAP@100; Time (ms); 10-20% Mask PSNR; 1:1 Accuracy; NDCG; nDCG@10; MRR@10,Cross-Lingual Information Retrieval; Passage Retrieval; Scientific Results Extraction; TAR; Zero Shot on BEIR (Inference Free Model),Natural Language Processing,"Information retrieval is the task of ranking a list of documents or search results in response to a query    <span style=""color:grey; opacity: 0.6"">( Image credit: [sudhanshumittal](https://github.com..."
2D Object Detection,93,SJTU Multispectral Object Detection (SMOD) Dataset; AppleBBCH81; A collection of 131 CT datasets of pieces of modeling clay containing stones; FES; DOLPHINS; Hypersim; FathomNet2023; ChessReD2K; DermSynth3D; TRR360D,; RADIATE; BDD100K val; FishEye8K; UAV-PDD2023; TXL-PBC: a freely accessible labeled peripheral blood cell dataset; DUO; SCoralDet Dataset; RF100; ExDark,; mAP@0.5; test/mAP; mAP@75; AP@0.5; mAP50; AP90(T<90); AP50(T<90); mAP@0.3; AP50,Long-tailed Object Detection; Drivable Area Detection; Object Detection; Thermal Image Segmentation; SAR Ship Detection,Methodology; Computer Vision,N/A
Image Retrieval,87,CREPE (Compositional REPresentation Evaluation); CIRCO; DeepPatent; CUB-200-2011; DyML-Product; European Flood 2013 Dataset; Flickr30k-CNA; YFCC100M; Localized Narratives; CIRR,CREPE (Compositional REPresentation Evaluation); ICFG-PEDES; DeepPatent; PhotoChat; WIT; MUGE Retrieval; In-Shop; CUB-200-2011; Par106k; LaSCo,"Recall@1 (HN-Atom, UC); A-R@5; Rank-50; (Recall@5+Recall_subset@1)/2; R@1; PNR; QPS; Average-mAP; Recall@1; R@8",Composed Image Retrieval (CoIR); Sketch-Based Image Retrieval; Content-Based Image Retrieval; Face Image Retrieval; Image Instance Retrieval,Computer Vision,"**Image Retrieval** is a fundamental and long-standing computer vision task that involves finding images similar to a given query from a large database. It is often considered a form of fine-grained, ..."
2D Semantic Segmentation,83,FES; OC-Cityscape; ACCT Data Repository; SICKLE; DermSynth3D; AUT-VI; [[get~Easy//Service//Charge]]How do I dispute a charge on Expedia?; WorldFloods; CaDIS; RUGD,xBD; Extended heartSeg; RELLIS-3D; WorldFloods; WildScenes; Deep Indices; CamVid; Time Series Prediction Benchmarks; GF-PA66 3D XCT; Cityscapes val,Localization F1-score; Jaccard (Mean); mIoU (Env DA); mIoU (Temporal DA) ; 1:3 Accuracy; Category mIoU; Mean IoU (class); GMac; Classification F1-score; Average IOU,Reflection Removal; Material Segmentation; Disjoint 10-1; Landmark-based segmentation; Overlapped 15-1,Adversarial; Computer Vision; Audio; Natural Language Processing,N/A
Relation Extraction,82,HyperRED; FOBIE; GAD; LabPics; KGRED; TimeBankPT; MAVEN-ERE; XFUND; CDR; X-WikiRE,CoNLL04; 2010 i2b2/VA; GAD; ADE Corpus; Google RE; NYT-single; LPSC-hasproperty; JNLPBA; SciERC; LPSC-contains,F1 (Zero-Shot); Average F1; RE+ Micro F1; Macro F1; Micro F1; P@10%; Triplet F1 (strict EL); RE+ Macro F1; RE+ Macro F1 ; F1 (5% Few-Shot),Hyper-Relational Extraction; Zero-shot Relation Triplet Extraction; Continual Relation Extraction; Relationship Extraction (Distant Supervised); Multi-Labeled Relation Extraction,Natural Language Processing,"**Relation Extraction** is the task of predicting attributes and relations for entities in a sentence. For example, given a sentence “Barack Obama was born in Honolulu, Hawaii.”, a relation classifier..."
Image Generation,79,ColorSVG-100K; FaceForensics++; CUB-200-2011; SPOT-10; KMNIST; WISE; ARKitScenes; ActivityNet Entities; Fashion-MNIST; Fashion-Gen,LSUN Horse 256 x 256; Landscapes 256 x 256; KMNIST; WISE; ARKitScenes; CIFAR-10 (10% data); Pokemon 256x256; Fashion-MNIST; LSUN Car 512 x 384; FFHQ 64x64,Model Size (MB); FID-50k; sFID; TextScenesHQ FID; Intra-FID; Inception score; clean-FID; KID; TextVsionBlend OCR (F1 Score); FID (SwAV),Image Harmonization; Chinese Landscape Painting Generation; Face Generation; Virtual Staining; Infinite Image Generation,Miscellaneous; Medical; Computer Vision; Natural Language Processing,"**Image Generation** (synthesis) is the task of generating new images from an existing dataset.    - **Unconditional generation** refers to generating samples unconditionally from the dataset, i.e. $p..."
Image Captioning,79,ChEBI-20; VisCon-1M; XM 3600; CITE; NoCaps; Localized Narratives; Conceptual Captions; IU X-Ray; Winoground; SCapRepo,nocaps in-domain; ChEBI-20; Peir Gross; nocaps-val-near-domain; nocaps-XD out-of-domain; VizWiz 2020 test; nocaps-XD in-domain; nocaps-val-out-domain; Flickr30k Captions test; WHOOPS!,Exact; B3; chair_i; chair_s; BLEU-1; BLEU-2; CIDER; BLEU-1 (Romantic); CLIPScore; RDK FTS,Relational Captioning; 3D dense captioning; Vietnamese Image Captioning; Hindi Image Captioning; Semi Supervised Learning for Image Captioning,Computer Vision; Natural Language Processing,**Image Captioning** is the task of describing the content of an image in words. This task lies at the intersection of computer vision and natural language processing. Most image captioning systems us...
Depth Estimation,77,Matterport3D; HRWSI; HUMAN4D; Hypersim; 3D Ken Burns Dataset; DermSynth3D; ConSLAM; FutureHouse; TransProteus; DIODE,Matterport3D; DIODE; KITTI Eigen split; KITTI 2015; Taskonomy; Cityscapes test; DCM; ScanNet; eBDtheque; ScanNetV2,Average PSNR; MSE ; Absolute relative error (AbsRel); RMSE; Delta < 1.25^3; BadPix(0.01); mean absolute error; RMS; BadPix(0.03); RMSE log,Stereo-LiDAR Fusion; 3D Depth Estimation; Depth Aleatoric Uncertainty Estimation; Depth Image Upsampling; Bathymetry prediction,Computer Vision,**Depth Estimation** is the task of measuring the distance of each pixel relative to the camera. Depth is extracted from either monocular (single) or stereo (multiple views of a scene) images. Traditi...
Autonomous Driving,70,A*3D; PRECOG; openDD; IDD; SynthCity; TITAN; CARLA; Lost and Found; PandaSet; HDD,Town05 Short; Town05 Long; CARLA Leaderboard; ApolloCar3D,Infraction penalty; DS; RC; Driving Score; A3DP; Route Completion,NavSim; Dead-Reckoning Prediction; CARLA MAP Leaderboard; Motion Forecasting; 3D Pedestrian Tracking,Robots; Computer Vision; Miscellaneous,Autonomous driving is the task of driving a vehicle without human conduction.     Many of the state-of-the-art results can be found at more general task pages such as [3D Object Detection](https://pap...
Object Tracking,69,UAVDT; Lindenthal Camera Traps; UFPR-ALPR; VOT2017; TLP; Open Radar Datasets; HT1080WT cells - 3D collagen type I matrices; HOMER; PersonPath22; MDOT,QuadTrack; SeaDronesSee; 1; MMPTRACK; BIRDSAI - ICVGIP 2020; FE108; Perception Test; KITTI; VisEvent; COESOT,Precision Rate; Success Rate; Humans; Averaged Precision; mean success; Precision Score; Precision Plot; HOTA; 3DMOTA; mean precision,Pupil Tracking; Multiple Object Tracking; Amodal Tracking; Cell Tracking; Online Multi-Object Tracking,Computer Vision,"**Object tracking** is the task of taking an initial set of object detections, creating a unique ID for each of the initial detections, and then tracking each of the objects as they move around frames..."
Face Recognition,67,23 Pairs of Identical Twins Face Image Data; CASIA-WebFace+masks; FAD; CASIA-FASD; UMDFaces; MLFW; RFW; BTS3.1; XQLFW; mEBAL,CASIA-WebFace+masks; MLFW; BTS3.1; Color FERET (Online Open Set); XQLFW; MFR; MFW+ (M-M); AgeDB-30; mebeblurf; CelebA+masks,FNMR [%] @ 10-3 FMR; MFR-ALL; MFR-MASK; TAR @ FAR=0.0001; TAR@FAR=0.0001; Caucasian; South Asian; TAR @ FAR=0.01; TAR @ FAR=1e-5; 5-class test accuracy,Unsupervised face recognition; Lightweight Face Recognition; Synthetic Face Recognition; Age-Invariant Face Recognition; Face Quality Assessement,Methodology; Computer Vision,**Facial Recognition** is the task of making a positive identification of a face in a photo or video image against a pre-existing database of faces. It begins with detection - distinguishing human fac...
3D Object Detection,67,A*3D; PreSIL; PandaSet; NYUv2; Light Snowfall; Mono3DRefer; nuScenes LiDAR only; nuScenes-C; nuScenes; V2XSet,KITTI Cars Hard val; Light Snowfall; View-of-Delft (val); Dense Fog; nuScenes LiDAR only; KITTI Pedestrian; nuScenes; V2XSet; SUN-RGBD val; nuScenes Camera Only,BEV AP@0.3 Urban;  mAP; AP|R40(moderate); mAP@0.5; AP0.7 (Noisy); mod. Pedestrian AP@.25IoU; mATE; NDS (val); BEV AP@0.3 Rain; FrameAccuracy,Monocular 3D Object Detection; Robust BEV Detection; 3D Object Detection From Stereo Images; Robust 3D Object Detection; Multiview Detection,Computer Vision,"**3D Object Detection** is a task in computer vision where the goal is to identify and locate objects in a 3D environment based on their shape, location, and orientation. It involves detecting the pre..."
Link Prediction,67,Citeseer; GO21; IMDb Movie Reviews; ICEWS; TSP/HCP Benchmark set; SINS; Orkut; Arxiv GR-QC; FB1.5M; Yelp2018,MIT; OpenBG500; Cora (biased evaluation); OpenBioLink; ICEWS05-15; KG20C; FB-AUTO; Wiki-Vote; IMDb; Alibaba-S,Mean AP; Hit@1; Macro F1; ROC AUC; Micro F1; ACC; MR; Hit@10; nDCG@10; HR@10,Link prediction on DH-KGs; Dynamic Link Prediction; Inductive Link Prediction; Anchor link prediction; Hyperedge Prediction,Graphs; Natural Language Processing,"**Link Prediction** is a task in graph and network analysis where the goal is to predict missing or future connections between nodes in a network. Given a partially observed network, the goal of link ..."
3D Reconstruction,58,HOD; SceneNet; Common Objects in 3D; Indoor and outdoor DFD dataset; ThermoScenes; BlendedMVS; ShapenetRender; Houses3K; Tragic Talkers; MobileBrick,Aria Synthetic Environments; DTU; ShapeNet; Data3D−R2N2; ScanNet; Scan2CAD; Aria Digital Twin Dataset; ApolloCar3D; 300W; 3DPeople,Comp; A3DP; 3DIoU; Precision; Average Accuracy; P2S; F-Score@1%; 1-of-100 Accuracy; IoU; Normal Consistency,Garment Reconstruction; 3D Room Layouts From A Single RGB Panorama; Point cloud reconstruction; 3D Semantic Scene Completion; 3D Shape Reconstruction from Videos,Methodology; Computer Vision,**3D Reconstruction** is the task of creating a 3D model or representation of an object or scene from 2D images or other data sources. The goal of 3D reconstruction is to create a virtual representati...
Video Understanding,56,HVU; DeepSportRadar-v1; M$^3$-VOS; VidSitu; WildQA; SoccerDB; CinePile: A Long Video Question Answering Dataset and Benchmark; MLB-YouTube Dataset; STAR Benchmark; Query-Focused Video Summarization Dataset,N/A,N/A,Anomaly Detection In Surveillance Videos; Long-video Activity Recognition; Streaming video understanding; Video Quality Assessment; Video Alignment,Computer Vision,"A crucial task of **Video Understanding** is to recognise and localise (in space and time) different actions or events appearing in the video.      <span class=""description-source"">Source: [Action Det..."
Optical Character Recognition (OCR),55,SSIG-SegPlate; UFPR-ALPR; Twitter100k; Copel-AMR; I2L-140K; SciTSR; FICS PCB Image Collection (FPIC); Visiting Card | ID Card Images | Hindi-English; VideoDB's OCR Benchmark Public Collection; ChineseLP,"I2L-140K; VideoDB's OCR Benchmark Public Collection; SUT; Benchmarking Chinese Text Recognition: Datasets, Baselines, and an Empirical Study; FSNS - Test; im2latex-100k",Accuracy (%); BLEU; Average Accuracy; Character Error Rate (CER); Sequence error; Word Error Rate (WER),Handwritten Text Recognition; Active Learning; Handwritten Chinese Text Recognition; Irregular Text Recognition; Offline Handwritten Chinese Character Recognition,Methodology; Computer Vision; Natural Language Processing,"**Optical Character Recognition** or **Optical Character Reader** (OCR) is the electronic or mechanical conversion of images of typed, handwritten or printed text into machine-encoded text, whether fr..."
Abstractive Text Summarization,53,PeerSum; Spotify Podcast; AMR Bank; WikiSum; ConvoSumm; WITS; VNDS; CNN/Daily Mail; FINDSum; WikiHow,WITS; CNN / Daily Mail; CNN/Daily Mail; mlsum-es; WikiHow; Abstractive Text Summarization from Fanpage; SAMSum Corpus: A Human-annotated Dialogue Dataset for Abstractive Summarization; MLSum-it; AESLC; PLOS,rouge1; ROUGE-1; Content F1; ROUGE-2; Test ROGUE-1; BERTScore; Rouge-L; METEOR; # Parameters; Rouge-2,Reader-Aware Summarization; Multimodal Abstractive Text Summarization; Timeline Summarization,Natural Language Processing,**Abstractive Text Summarization** is the task of generating a short and concise summary that captures the salient ideas of the source text. The generated summaries potentially contain new phrases and...
Medical Image Segmentation,52,HC18; BreastDICOM4; Polyp ASH; MICCAI 2015 Head and Neck Challenge; LiTS17; WORD; FetReg; CoCaHis; CVC-ClinicDB; Endotect Polyp Segmentation Challenge Dataset,EM; MICCAI 2015 Head and Neck Challenge; MoNuSeg 2018; Autoimmune Dataset; 2015 MICCAI Polyp Detection; CVC-ClinicDB; Endotect Polyp Segmentation Challenge Dataset; Medical Segmentation Decathlon; KvasirCapsule-SEG; AMOS,FPS; Dice; NSD; Avg DSC; IoU; mIoU (5-folds); VInfo; Average Dice (5-folds); Jaccard Index; S-Measure,Skin Lesion Segmentation; Electron Microscopy Image Segmentation; Pulmorary Vessel Segmentation; Brain Lesion Segmentation From Mri; COVID-19 Image Segmentation,Medical; Computer Vision,"**Medical Image Segmentation** is a computer vision task that involves dividing an medical image into multiple segments, where each segment represents a different object or structure of interest in th..."
Emotion Recognition,52,EmoPars; BanglaEmotion; Werewolf-XL; SEED; SEND; EmoWOZ; Human faces with mixed-race & various emotions; Video2GIF; REN-20k Dataset; BERSt,MaSaC_ERC; MPED; SEED; MAFW; RAVDESS; คลิปคุณพ่อให้ลูกสาวยืมโทรศัพท์และความสนุกสนาน; EMOTIC; MSP-Podcast; Emomusic; FER2013,"F1-score (Weighted); WAR; Concordance correlation coefficient (CCC); EmoA; Accuracy; 1'""; 5-class test accuracy; EmoV; Top-3 Accuracy (%)",Emotion Cause Extraction; A-VB Two; Emotion Recognition in Context; A-VB High; Speech Emotion Recognition,Speech; Computer Vision; Audio; Miscellaneous; Natural Language Processing,"**Emotion Recognition** is an important area of research to enable effective human-computer interaction. Human emotions can be detected using speech signal, facial expressions, body language, and elec..."
3D Human Pose Estimation,50,Parkour-dataset; TotalCapture; HSPACE; Human-Art; RePoGen; Deep Fashion3D; H3WB; HUMAN4D; SportsPose; 3DPW,SkiPose; HSPACE; Geometric Pose Affordance ; 3D Poses in the Wild Challenge; H3WB; 3DPW; AIST++; Panoptic; 3DOH50K; ITOP front-view,Params (M); PA-MPJPE; MPVPE; PVE-Hands; W-PVE; Single-view; 3DPCK; PVE; PA-PVE-All; B-NMJE,Egocentric Pose Estimation; 3D human pose and shape estimation; Global 3D Human Pose Estimation; 3D Multi-Person Pose Estimation; Pose Prediction,Computer Vision,**3D Human Pose Estimation** is a computer vision task that involves estimating the 3D positions and orientations of body joints and bones from 2D images or videos. The goal is to reconstruct the 3D p...
Hate Speech Detection,47,KnowledJe; HateXplain; HatefulDiscussions; Implicit Hate; Peer to Peer Hate; SHAJ; FairPrism; ToxiGen; ViTHSD; MLMA Hate Speech,"bajer_danish_misogyny; Automatic Misogynistic Identification; HateXplain; DKhate; Waseem et al., 2018; Ethos MultiLabel; OLID; HateMM; SHAJ; HatEval",TEST F1 (macro); AAA; Precision; F1; F1-score; Hamming Loss; Macro F1; Accuracy; Macro-F1; AUROC,Hope Speech Detection; Hate Speech Normalization; Hate Speech Detection CrisisHateMM Benchmark,Natural Language Processing,"Hate speech detection is the task of detecting if communication such as text, audio, and so on contains hatred and or encourages violence towards a person or a group of people. This is usually based o..."
Novel View Synthesis,46,ThermoScenes; BlendedMVS; PhotoShape; Tanks and Temples; Spaces; OmniObject3D; LLFF; iFF; UASOL; SYNTHIA,PhotoShape; Tanks and Temples; LLFF; iFF; Dosovitskiy Chairs; ShapeNet Chair; BLEFF; RealEstate10K; ScanNet++; ACID,LPIPS; Average PSNR; PSNR/SSIM; PSNR; Average PSNR (dB); Focal Error; FID; NLL; PSIM; Size (MB),Gournd video synthesis from satellite image; Novel LiDAR View Synthesis,Computer Vision,Synthesize a target image with an arbitrary target camera pose from given source images and their camera poses.    See [Wiki](https://en.wikipedia.org/wiki/View_synthesis) for more introductions.    T...
Object Recognition,45,MECCANO; Freiburg Groceries; Turath-150K; N-ImageNet; TUM-GAID; UW Indoor Scenes (UW-IS) Occluded dataset; Visual 3D shape matching dataset; EV-IMO; HASY; OCID,"DVS128 Gesture; ObjectNet (ImageNet classes); MECCANO; ObjectNet (ImageNet classes, trained on ImageNet); N-Caltech 101; ObjectNet (All classes); CIFAR10-DVS; N-CARS; shape bias",mAP; Top 5 Accuracy; Top 1 Accuracy; Accuracy (% ); shape bias,3D Object Recognition; Depiction Invariant Object Recognition; Continuous Object Recognition,Computer Vision,"Object recognition is a computer vision technique for detecting + classifying objects in images or videos. Since this is a combined task of object detection plus image classification, the state-of-the..."
Semantic Parsing,45,SQA; AMR Bank; WebQuestionsSP; Szeged Corpus; NomBank; Multilingual TOP; Occluded REID; SEDE; SpCQL; SPLASH,"SQA; Geo; DRG (german, MRP 2020); spider; WebQuestionsSP; PTG (czech, MRP 2020); DRG (english, MRP 2020); SParC; ATIS; UCCA (german, MRP 2020)",EM; Denotation accuracy (test); Exact; F1; F1 Score; Exact Match; Accuracy; Denotation Accuracy; Accuracy (Dev); Test Accuracy,Text-To-SQL; Semantic Dependency Parsing; text-to-Cypher; Unsupervised semantic parsing; UCCA Parsing,Natural Language Processing,**Semantic Parsing** is the task of transducing natural language utterances into formal meaning representations. The target meaning representations can be defined according to a wide variety of formal...
Segmentation,45,BRISC; Boreal Forest Fire; MMFlood; NYUDv2-IS; UIIS10K; PALMS; DeepCrack; Aachen-Heerlen Annotated Steel Microstructure Dataset; CrackVision12K; 25kTrees,SimGas; !(()&&!|*|*|; MFSD; MMFlood; SA-1B,AR-small; Precision; 10%; Average Precision; F1 Score; IoU; AR-large; AR-medium; F1 score; Recall,Open-Vocabulary Semantic Segmentation,Computer Vision,N/A
Image Clustering,44,Cards; PCam; CUB-200-2011; HAR; Letter; EuroSAT; SPOT-10; UCF101; Oxford-IIIT Pets; Extended Yale B,MNIST-full; PCam; CUB-200-2011; HAR; EuroSAT; UCF101; imagenet-1k; ARL Polarimetric Thermal Face Dataset; Tiny-ImageNet; Oxford-IIIT Pets,ACCURACY; NMI; 	 ACCURACY; ARI; Backbone; Train set; Accuracy; Train Split; Image Size; Train Set,Face Clustering; Multi-view Subspace Clustering; Online Clustering; Multi-modal Subspace Clustering,Computer Vision,"Models that partition the dataset into semantically meaningful clusters without having access to the ground truth labels.     <span style=""color:grey; opacity: 0.6""> Image credit: ImageNet clustering ..."
Visual Reasoning,44,Cops-Ref; IconQA; InfiMM-Eval; Bongard-OpenWorld; PHYRE; COG; MaRVL; FigureQA; VSR; SMART-101,VSR; NLVR2 Test; NLVR; WinoGAViL; Winoground; PHYRE-1B-Within; NLVR2 Dev; CLEVRER; IRFL: Image Recognition of Figurative Language; Bongard-OpenWorld,AUCCESS; Predictive-per ques.; Explanatory-per opt.; 1-of-100 Accuracy; accuracy; Jaccard Index; Image Score; Accuracy (Test-U); Counterfactual-per ques.; Text Score,Visual Commonsense Reasoning,Reasoning; Computer Vision,Ability to understand  actions and reasoning  associated with any  visual images
Image Super-Resolution,43,PIRM; Urban100; TESTIMAGES; MSU SR-QA Dataset; IXI; xView; BSD100; CASIA-WebFace; ShipSpotting; StereoMSI,Set5 - 2x upscaling; FFHQ 256 x 256 - 4x upscaling; Set14 - 3x upscaling; BSD100 - 16x upscaling; BSD100 - 4x upscaling; BSD100 - 2x upscaling; Sun80 - 4x upscaling; Manga109 - 8x upscaling; VggFace2 - 8x upscaling; Set5 - 8x upscaling,SSIM 4x T2w; MS-SSIM; Perceptual Index; PSNR 4x T2w; LPIPS; Frechet Inception Distance; MORAN Overall Accuracy; MOS; FED; FLOPs(G),Multispectral Image Super-resolution; Multi-Frame Super-Resolution; satellite image super-resolution; Burst Image Super-Resolution; Stereo Image Super-Resolution,Computer Vision,"**Image Super-Resolution** is a machine learning task where the goal is to increase the resolution of an image, often by a factor of 4x or more, while maintaining its content and details as much as po..."
Image Segmentation,43,MARIDA; MSD (Mirror Segmentation Dataset); WildScenes; UIIS10K; PASCAL VOC; MineralImage5k; CEMS-W; 2DeteCT; Linear Equation Image Dataset; CaBuAr,MSD (Mirror Segmentation Dataset); EVD4UAV; Pascal Panoptic Parts; PMD; MAS3K; PASCAL VOC; RMAS; ImageNet; OxfordPets; COCO val2017,Detection: Full (mAP@0.5); F1@M; Dice; E-measure; F1; mIoUPartS; IoU; GFLOPs; mask AP; MAE,Few-shot Instance Segmentation,Computer Vision,"**Image Segmentation** is a computer vision task that involves dividing an image into multiple segments or regions, each of which corresponds to a different object or part of an object. The goal of im..."
Video Question Answering,43,MovieQA; How2QA; MSRVTT-MC; TVQA+; TVQA; HowTo100M; SUTD-TrafficQA; EgoTaskQA; MVBench; VALUE,How2QA; MSRVTT-MC; TVQA; SUTD-TrafficQA; NExT-QA (Efficient); MVBench; WildQA; MSR-VTT; STAR Benchmark; AGQA 2.0 balanced,ROUGE-1; CW; ROUGE-2; 1:1 Accuracy; 	 ACCURACY; Average Accuracy; 1/4; CH; 1/2; AVG,Zero-Shot Video Question Answer; Few-shot Video Question Answering,Reasoning; Computer Vision,N/A
Multi-Object Tracking,43,UAVDT; BEE23; PersonPath22; 2024 AI City Challenge; HiEve; JRDB; MMPTRACK; BDD100K; GMOT-40; TAO-Amodal,UAVDT; PersonPath22; 2024 AI City Challenge; VisDrone2019; HiEve; JRDB; 2DMOT15; BDD100K; MOT16; Synthehicle,e2e-MOT; AssA; LocA; MOTA; ClsA; TETA; Speed (FPS); IDF1; Track mAP; MOTP,Grounded Multiple Object Tracking; 3D Multi-Object Tracking; Referring Multi-Object Tracking; Trajectory Long-tail Distribution for Muti-object Tracking; Real-Time Multi-Object Tracking,Computer Vision,**Multi-Object Tracking** is a task in computer vision that involves detecting and tracking multiple objects within a video sequence. The goal is to identify and locate objects of interest in each fra...
Scene Understanding,43,ADE20K; RADIATE; SceneNet; PSI-AVA; Indoor and outdoor DFD dataset; UAVid; SynPick; Apron Dataset; COQE; CDS2K,ADE20K val; Semantic Scene Understanding Challenge (passive actuation & ground-truth localisation); Semantic Scene Understanding Challenge (active actuation & ground-truth localisation),OMQ; avg_fp_quality; avg_pairwise; Mean IoU; avg_spatial; avg_label,Lighting Estimation; 3D Room Layouts From A Single RGB Panorama; Video Semantic Segmentation; road scene understanding; Outdoor Light Source Estimation,Computer Vision,"Scene understanding involves interpreting the visual information of a scene, including objects, their spatial relationships, and the overall layout. It goes beyond simple object recognition by conside..."
Temporal Action Localization,42,RISE; FineGym; MCAD; Metaphorics; TUM Kitchen; HVU; HiEve; WEAR; Composable activities dataset; EPIC-KITCHENS-100,Ego4D MQ val; THUMOS'14; FineAction; MEXaction2; THUMOS14; MUSES; ActivityNet-1.2; MultiTHUMOS; THUMOS’14; CrossTask,Avg mAP (0.3:0.7); mAP@0.5; mAP IOU@0.1; Avg mAP (0.1-0.5); Average-mAP; mAP@0.4; mAP IOU@0.2; mAP IOU@0.5; mAP@0.3; mAP IOU@0.95,Weakly Supervised Action Localization; 3D Action Recognition; Activity Recognition In Videos; Open-vocab Temporal Action Detection; Weakly-supervised Temporal Action Localization,Computer Vision,Temporal Action Localization aims to detect activities in the video stream and  output beginning and end timestamps. It is closely related to  Temporal Action Proposal Generation.
Fine-Grained Image Classification,41,Herbarium 2021 Half–Earth; Kuzushiji-MNIST; CropAndWeed; Aircraft Context Dataset; CUB-200-2011; Tsinghua Dogs; CiNAT-Birds-2021; SPOT-10; FGSCM-52; IP102,Herbarium 2021 Half–Earth; Kuzushiji-MNIST; CUB-200-2011; Herbarium 2022; Con-Text; SOP; Oxford-IIIT Pets; Bird-225; STL-10; EMNIST-Digits,Accuracy (%); Test F1 score (private); mAP; PARAMS; Top 1 Accuracy; Accuracy; Average Per-Class Accuracy; Top-1; Test F1 score; FLOPS,Displaced People Recognition,Computer Vision,"**Fine-Grained Image Classification** is a task in computer vision where the goal is to classify images into subcategories within a larger category. For example, classifying different species of birds..."
3D Semantic Segmentation,41,ScanNet200; WildScenes; nuScenes-C; nuScenes; SemanticKITTI-C; DALES; 3D Platelet EM; STPLS3D; Hypersim; AMOS,ScanNet200; WildScenes; RELLIS-3D Dataset; nuScenes; DALES; 3D Platelet EM; STPLS3D; Hypersim; ScanNet++; SensatUrban,Mean IoU (test); mIoU (Env DA); Mean IoU (class); test mIoU; mAcc; mIoU (6-Fold); mIoU (test); Mean IoU; mIoU; OA,Unsupervised 3D Semantic Segmentation; furniture segmentation; Robust 3D Semantic Segmentation; Real-Time 3D Semantic Segmentation; 3D Point Cloud Part Segmentation,Computer Vision,**3D Semantic Segmentation** is a computer vision task that involves dividing a 3D point cloud or 3D mesh into semantically meaningful parts or regions. The goal of 3D semantic segmentation is to iden...
Face Detection,39,ADE20K; 23 Pairs of Identical Twins Face Image Data; MS-EVS Dataset; FDDB-360; PASCAL Face; DARK FACE; FAD; UMDFaces; AnimalWeb; Human-Parts,ADE20K; WIDER Face (Hard); WIDER Face (Medium); PASCAL Face; Manga109; DCM; COCO-WholeBody; Annotated Faces in the Wild; FDDB; WIDER FACE,AP75; Average Top-1 Accuracy; APL; Average Precision; GFLOPs; AP50; Accuracy; APM; mIoU; AP,Occluded Face Detection,Computer Vision,**Face Detection** is a computer vision task that involves automatically identifying and locating human faces within digital images or videos. It is a fundamental technology that underpins many applic...
Retrieval,38,ALCE; DAPFAM; LLeQA; Spiced; Natural Questions; InfoSeek; Luna-1; RoMQA; DTGB; ToolLens,OK-VQA; Quora Question Pairs; PubMedQA corpus with metadata; HotpotQA; คลิปไวรัล!! ไอซ์ ปรีชญา ลืมปิดไลฟ์สดตอนอาบน้ำ ถูกแชร์กระหึ่มเน็ต; PubMedQA; Natural Questions; InfoSeek; ToolLens; Polyvore,text-to-video Mean Rank; Recall@5; COMP@; Queries per second; 0L; Accuracy (Top-1),Table Retrieval; Deep Hashing; Text Retrieval,Methodology; Computer Vision; Natural Language Processing,"A methodology that involves selecting relevant data or examples from a large dataset to support tasks like prediction, learning, or inference. It enhances models by providing context or additional inf..."
Video Captioning,38,How2QA; HowTo100M; 2024 AI City Challenge; Shot2Story20K; VALUE; MSR-VTT; V2C; VATEX; MSVD-Indonesian; How2R,Hindi MSR-VTT; ChinaOpen-1k; VATEX; MSVD-CTN; TVC; VidChapters-7M; Shot2Story20K; MSVD-Indonesian; ActivityNet Captions; MSRVTT-CTN,BLEU4; SPICE; BLEU-3; METEOR; GS; CIDEr; ROUGE; ROUGE-L; BLEU-4,Boundary Captioning; Live Video Captioning; Video Boundary Captioning; Dense Video Captioning; Audio-Visual Video Captioning,Computer Vision,**Video Captioning** is a task of automatic captioning a video by understanding the action and event in the video which can help in the retrieval of the video efficiently through text.      <span clas...
Panoptic Segmentation,37,ADE20K; NYUv2; CropAndWeed; SB20; MUSES: MUlti-SEnsor Semantic perception dataset; LaRS; BDD100K; DALES; Cityscapes; Mapillary Vistas Dataset,ADE20K; Indian Driving Dataset; COCO panoptic; COCO test-dev; LaRS; MUSES: MUlti-SEnsor Semantic perception dataset; ScanNetV2; DALES; KITTI Panoptic Segmentation; NYU Depth v2,PQ (test); Params (M); PQ_th; SQst; RQ; PQ (with stuff); SQth; PQ; PQst; mIoU (test),Uncertainty-Aware Panoptic Segmentation; Video Panoptic Segmentation,Computer Vision,**Panoptic Segmentation** is a computer vision task that combines semantic segmentation and instance segmentation to provide a comprehensive understanding of the scene. The goal of panoptic segmentati...
Text Retrieval,37,CompMix-IR; Image-Chat; TripClick; LLeQA; CURE; TREC-COVID; DAPFAM; Natural Questions; COVID-19 Twitter Chatter Dataset; SciFact,N/A,N/A,N/A,N/A,N/A
3D Pose Estimation,36,CarFusion; HSPACE; Accidental Turntables; SportsPose; HUMAN4D; Event-Human3.6m; Hypersim; Symmetric Solids; SLAM2REF; Mirrored-Human,CarFusion; Human3.6M; HARPER; Google-AR; ApolloCar3D; Google-Yoga; K2HPD,FPS; PCK@0.2; Average MPJPE (mm); 3DPCK; A3DP,N/A,Computer Vision,"Image credit: [GSNet: Joint Vehicle Pose and Shape Reconstruction with Geometrical and Scene-aware Supervision  , ECCV'20](https://www.ecva.net/papers/eccv_2020/papers_ECCV/papers/123600511.pdf)"
Optical Flow Estimation,35,TUM-GAID; CrowdFlow; DSEC; RobotPush; QUVA Repetition; SynWoodScape; HD1k; VLOG Dataset; Virtual KITTI; Blackbird,KITTI 2015 unsupervised; Sintel Clean unsupervised; KITTI 2012 unsupervised; KITTI 2015; Spring; Sintel Final unsupervised; Sintel-final; KITTI 2015 (train) ; KITTI 2012; Sintel-clean,Fl-fg; F1-all; Out-Noc; Average End-Point Error; Noc; EPE; 1px total; Fl-all,Video Stabilization,Computer Vision,**Optical Flow Estimation** is a computer vision task that involves computing the motion of objects in an image or a video sequence. The goal of optical flow estimation is to determine the movement of...
Video Retrieval,35,How2QA; HowTo100M; TVR; Shot2Story20K; IAW Dataset; DiDeMo; MSR-VTT; VTC; VATEX; MSVD-Indonesian,TVR; MSR-VTT-1kA; DiDeMo; MSR-VTT; SSv2-template retrieval; VATEX; MSVD-Indonesian; MSVD; EgoExoLearn; FIVR-200K,text-to-video Mean Rank; video-to-text Mean Rank; video-to-text R@1; R@1; text-to-video R@5; video-to-text R@5; R@100; text-to-videoMedian Rank; text-to-video R@10; video-to-text Median Rank,Video-Text Retrieval; Composed Video Retrieval (CoVR); Replay Grounding; Video-Adverb Retrieval; Video Grounding,Computer Vision,"The objective of video retrieval is as follows: given a text query and a pool of candidate videos, select the video which corresponds to the text query.  Typically, the videos are returned as a ranked..."
Trajectory Prediction,35,PRECOG; HEV-I; TITAN; HDD; SDD; nuScenes; UCY; Waymo Open Motion Dataset; uniD Dataset; IITB Corridor,HEV-I; SDD; nuScenes; UCY; YJMob100K@D; JAAD; ApolloScape; TrajAir: A General Aviation Trajectory Dataset; Lyft Level 5; STATS SportVu NBA [ATK],minFDE6; brier-minFDE (K=6); RMSE; FDE-8/12; MinADE_5; MinFDE_1; ADE; ADE (in world coordinates); MR (K=6); FIOU(1.5),Trajectory Forecasting; Human motion prediction; Out-of-Sight Trajectory Prediction,Time Series; Computer Vision,"**Trajectory Prediction** is the problem of predicting the short-term (1-3 seconds) and long-term (3-5 seconds) spatial coordinates of various road-agents such as cars, buses, pedestrians, rickshaws, ..."
Stance Detection,35,MGTAB; COVMis-Stance; COVID-CQ; Monant Medical Misinformation; Perspectrum; HpVaxFrames; P-Stance; VaccineLies; ExaASC; CoVaxLies v2,MGTAB; emergent; RumourEval; Perspectrum; P-Stance; SemEval 2019; poldeb; VAST; argmin; mtsd,Macro Recall; Accuracy (10-fold); Average F1; Precision; F1; F1 Score; Accuracy; Avg F1; Recall; Macro Precision,Zero-Shot Stance Detection; Few-Shot Stance Detection; Stance Detection (US Election 2020 - Trump); Stance Detection (US Election 2020 - Biden),Natural Language Processing,"Stance detection is the extraction of a subject's reaction to a claim made by a primary actor. It is a core part of a set of approaches to fake news assessment.  Example:  * Source: ""Apples are the mo..."
Action Detection,34,ESAD; WEAR; JHMDB; IITB Corridor; VidHOI; ROAD; MLB-YouTube Dataset; UCLA Protest Image; OREBA; TTStroke-21 ME22,TTStroke-21 ME22; THUMOS' 14; Charades; UCF101-24; MultiSports; Multi-THUMOS; MultiTHUMOS; UCF Sports; TSU; J-HMDB,mAP; Video-mAP 0.5; Video-mAP 0.1; IoU; Frame-mAP; Frame-mAP 0.5; Video-mAP 0.2,Fine-Grained Action Detection; Multiple Action Detection; Few Shot Temporal Action Localization; Online Action Detection; Human Activity Recognition,Time Series; Computer Vision,"Action Detection aims to find both where and when an action occurs within a video clip and classify what the action is taking place. Typically results are given in the form of action tublets, which ar..."
Automatic Speech Recognition (ASR),34,VoxPopuli; ReVerb Challenge; Jam-ALT; GneutralSpeech Female; FLEURS; VibraVox (rigid in-ear microphone); EdAcc; Fongbe Speech Dataset; RealMAN; SpeechInstruct,LRS2; RealMAN; VoxPopuli; Sagalee; HUI speech corpus; The Spoken Wikipedia Corpora; M-AILabs speech dataset; Voxforge German; LRS3-TED,CER; WER; WER (%); Word Error Rate (WER); Test WER,Automatic Phoneme Recognition,Speech,"**Automatic Speech Recognition (ASR)** involves converting spoken language into written text. It is designed to transcribe spoken words into text in real-time, allowing people to communicate with comp..."
Monocular Depth Estimation,33,Matterport3D; NYUv2; Holopix50k; WSVD; HRWSI; HUMAN4D; Cityscapes; VA (Virtual Apartment); UASOL; Hypersim,N/A,N/A,N/A,N/A,N/A
Action Classification,33,HAA500; SoccerNet; MLB; WLASL; SoccerDB; UCF101; Sims4Action; SoccerNet-v2; TTStroke-21 ME22; THUMOS14,N/A,N/A,N/A,N/A,N/A
Visual Question Answering,32,GRIT; CII-Bench; MapEval-Visual; MMBench; VNHSGE; CLEVR; MM-Vet; PlotQA; GQA; Visual Question Answering v2.0,TextVQA test-standard; GRIT; MMHal-Bench; MapEval-Visual; VQA v2 val; MMBench; MM-Vet (w/o External Tools); CLEVR; MM-Vet; GQA,Percentage correct; yes/no; GPT-3.5 score; number; overall; GPT-4 score; other; CIDEr; Hallucination Rate; GPT-4 score (bbox),Explanatory Visual Question Answering; Object Hallucination; MM-Vet v2; Vietnamese Visual Question Answering; Spatial Reasoning,Computer Vision; Natural Language Processing,MLLM Leaderboard
Image-to-Image Translation,32,ADE20K; BCNB; IXI; selfie2anime; People Snapshot Dataset; Cityscapes; AFHQ; SYNTHIA; RASMD; Synscapes,anime-to-selfie; GTAV-to-Cityscapes Labels; Cityscapes-to-Foggy Cityscapes; IXI; selfie2anime; Cityscapes Photo-to-Labels; BRATS; cat2dog; AFHQ; COCO-Stuff Labels-to-Photos,Average PSNR; Number of Params; Number of params; Per-pixel Accuracy; LPIPS; Frechet Inception Distance; fwIOU; Class IOU; mIoU (13 classes); Per-class Accuracy,Image-to-Image Regression; Multimodal Unsupervised Image-To-Image Translation; Photo-To-Caricature Translation; Virtual Try-Off; Synthetic-to-Real Translation,Computer Vision,"**Image-to-Image Translation** is a task in computer vision and machine learning where the goal is to learn a mapping between an input image and an output image, such that the output image can be used..."
Skeleton Based Action Recognition,30,MSRC-12; NTU RGB+D 120; Metaphorics; JHMDB; H2O  (2 Hands and Objects); G3D; UCF101; UT-Kinect; Kinetics 400; MSR ActionPairs,N/A,N/A,N/A,N/A,N/A
Activity Recognition,30,INDRA; GazeFollow; MEx; MOSAD; Sims4Action; PETRAW; MoVi; UniMiB SHAR; MEVA; EGOK360,First-Person Hand Action Benchmark; RWF-2000; Self-Stimulatory Behavior Dataset; Stanford40,Accuracy; 1:1 Accuracy; Activity Recognition; Top-3 Accuracy (%),Group Activity Recognition; Human Activity Recognition; Egocentric Activity Recognition; Human action generation; Recognizing And Localizing Human Actions,Robots; Time Series; Computer Vision,Human **Activity Recognition** is the problem of identifying events performed by humans given a video input. It is formulated as a binary (or multiclass) classification problem of outputting activity ...
Fake News Detection,30,COVID-19 Fake News Dataset; NELA-GT-2018; NELA-GT-2020; Twitter MediaEval; UPFD-GOS; BanFakeNews; Fake News Filipino Dataset; LIAR; BanMANI; CIDII Dataset,COVID-19 Fake News Dataset; Weibo NER; Grover-Mega; Hostility Detection Dataset in Hindi; FNC-1; Social media; MediaEval2016; RAWFC; LIAR; PolitiFact,Per-class Accuracy (Disagree); Per-class Accuracy (Unrelated); Per-class Accuracy (Agree); F1; Weighted Accuracy; Unpaired Accuracy; Accuracy; Validation Accuracy; Per-class Accuracy (Discuss); Test Accuracy,N/A,Natural Language Processing,**Fake News Detection** is a natural language processing task that involves identifying and classifying news articles or other types of text as real or fake. The goal of fake news detection is to deve...
Sign Language Recognition,30,CSL-Daily; How2Sign; ASLG-PC12; YouTube-ASL; WLASL; Znaki; ASL-Skeleton3D; American Sign Language Dataset; Slovo: Russian Sign Language Dataset; SCOPE Dataset,CSL-Daily; WLASL; Znaki; Slovo: Russian Sign Language Dataset; MSASL-1000; FDMSE-ISL; RWTH-PHOENIX-Weather 2014 T; RWTH-PHOENIX-Weather 2014; Bukva; LSA64,Accuracy (%); Accuracy (Top-1); P-C Top-1 Accuracy; Precision; F1-score; Rank-1 Recognition Rate; Official Test Split; CER (%); Actions Top-1; P-I Top-1 Accuracy,N/A,Computer Vision,**Sign Language Recognition** is a computer vision and natural language processing task that involves automatically recognizing and translating sign language gestures into written or spoken language. ...
Visual Object Tracking,29,VOT2017; TLP; YouTube-VOS 2018; BioDrone; RGBD1K; AU-AIR; LaSOT; OTB-2013; RF100; TREK-150,VOT2017; YouTube-VOS 2018; LaSOT-ext; LaSOT; OTB-2013; DiDi; TempleColor128; VOT2022; VOT2019; AVisT,Success Rate; AUC; Jaccard (Unseen); F-Measure (Unseen); O (Average of Measures); Jaccard (Seen); Precision; Tracking quality; Average Overlap; Success Rate 0.75,Zero-Shot Single Object Tracking,Computer Vision,"**Visual Object Tracking** is an important research topic in computer vision, image understanding and pattern recognition. Given the initial state (centre location and scale) of a target in the first ..."
Facial Expression Recognition (FER),29,MMI; FERG; BP4D; Human faces with mixed-race & various emotions; SAVEE; 4DFAB; AffectNet; Oulu-CASIA; RAF-DB; MAFW,MMI; FERG; BP4D; SAVEE; Cohn-Kanade; AffectNet; Oulu-CASIA; RAF-DB; CAER; Real-World Affective Faces,Accuracy (6 emotion); 0..5sec; Accuracy (10-fold); Avg. Accuracy; Accuracy (8 emotion); Accuracy(pretrained); UAR; Overall Accuracy; Accuracy (7 emotion); Accuracy ,Micro-Expression Spotting; Micro-Expression Recognition; Cross-corpus; 3D Facial Expression Recognition; Smile Recognition,Computer Vision,**Facial Expression Recognition (FER)** is a computer vision task aimed at identifying and categorizing emotional expressions depicted on a human face. The goal is to automate the process of determini...
Scene Text Recognition,29,SSIG-SegPlate; IIIT-ILST; UFPR-ALPR; Copel-AMR; RCTW-17; ChineseLP; TS-TR; SignboardText; MLe2e; SVT,ICDAR 2003; SVTP; IC19-Art; ICDAR2013; Uber-Text; CUTE80; SVT; MSDA; IC13; ICDAR2015,Average Accuracy; Accuracy (%); Accuracy; 1:1 Accuracy,Jersey Number Recognition,Computer Vision,See [Scene Text Detection](https://paperswithcode.com/task/scene-text-detection) for leaderboards in this task.
Autonomous Vehicles,29,PRECOG; PreSIL; SynthCity; TITAN; CARLA; RadarScenes; Lyft Level 5 Prediction; ROAD; comma 2k19; Ford AV Dataset,ApolloCar3D,A3DP,Traffic Sign Recognition; Driver Attention Monitoring; Fast Vehicle Detection; 3D Car Instance Understanding; Traffic Signal Control,Robots; Computer Vision; Computer Code,Autonomous vehicles is the task of making a vehicle that can guide itself without human conduction.    Many of the state-of-the-art results can be found at more general task pages such as [3D Object D...
Unsupervised Anomaly Detection,28,ISP-AD; UBI-Fights; PAD Dataset; DAGM2007; SPOT-10; KolektorSDD; ECG5000; SMAP; STL-10; Fashion-MNIST,ECG5000; KolektorSDD2; SMAP; STL-10; AeBAD-S; Fashion-MNIST; Reuters-21578; 20NEWS; DAGM2007; Vehicle Claims,Detection AUROC; ROC-AUC NEAR; ROC-AUC-ID (In-Distribution setup); Recall; Precision; ROC-AUC IID; Segmentation AUPRO; F1; ROC-AUC FAR; Best F1,Anomaly Detection at 30% anomaly; Anomaly Detection at Various Anomaly Percentages; Root Cause Ranking; Unsupervised Anomaly Detection with Specified Settings -- 30% anomaly; Unsupervised Contextual Anomaly Detection,Miscellaneous; Computer Vision; Graphs,The objective of **Unsupervised Anomaly Detection** is to detect previously unseen rare objects or events without any prior knowledge about these. The only information available is that the percentage...
Object Counting,28,RSOC; Fish Counting; SmartCity; HowMany-QA; LeukemiaAttri; PASCAL VOC; Penguin dataset; iWildCam 2021; Artificial fluorescent bacteria dataset; FSC147,TallyQA-Complex; HowMany-QA; PASCAL VOC; FSC147; Pascal VOC 2007 count-test; COCO count-test; CARPK; TRANCOS; TallyQA-Simple; Omnicount-191,mRMSE; RMSE; mRMSE-nz; MAE(test); RMSE(test); MSE; m-reIRMSE; Accuracy; m-relRMSE; MAE(val),Training-free Object Counting; Open-vocabulary object counting; Few-shot Object Counting and Detection; Exemplar-Free Counting,Computer Vision,"The goal of **Object Counting** task is to count the number of object instances in a single image or video sequence. It has many real-world applications such as traffic flow monitoring, crowdedness es..."
Visual Place Recognition,28,SF-XL Occlusion; KITTI360pose; NYU-VPR; GTA-UAV; Oxford RobotCar Dataset; Mapillary Vistas Dataset; AmsterTime; ALTO; MSLS; NCLT,SF-XL Occlusion; Tokyo247; KITTI360pose; Nardo-Air; St Lucia; Oxford RobotCar Dataset; SVOX-Overcast; Mapillary test; Laurel Caverns; Pittsburgh-250k-test,Recall@10; Recall@5; Average F1; Localization Recall@1 ; recall@top1%; recall@top1; Recall@1,3D Place Recognition; geo-localization; Indoor Localization,Computer Vision,"**Visual Place Recognition** is the task of matching a view of a place with a different view of the same place taken at a different time.    <span class=""description-source"">Source: [Visual place reco..."
Visual Tracking,28,Dialogue State Tracking Challenge; TLP; Kubric; MDOT; MMPTRACK; OxUva; MobiFace; YT-BB; DAVIS; SurgT,DAVIS; Kubric; Second dialogue state tracking challenge; RGB-Stacking; LaSOT; Kinetics; OTB-2013; OTB-100; TNL2K; TrackingNet,Average Jaccard; ACCURACY; precision; Normalized Precision; Score; AUC,Rgb-T Tracking; Real-Time Visual Tracking; Point Tracking; RF-based Visual Tracking,Computer Vision,"**Visual Tracking** is an essential and actively researched problem in the field of computer vision with various real-world applications such as robotic services, smart surveillance systems, autonomou..."
Neural Architecture Search,27,EA-HAS-Bench; Freiburg Groceries; NAS-Bench-201; Visual Wake Words; Oxford-IIIT Pets; STL-10; CINIC-10; Oxford-IIIT Pet Dataset; HW-NAS-Bench; ImageNet,"NAS-Bench-201; NAS-Bench-201, CIFAR-100; NATS-Bench Size, ImageNet16-120; NATS-Bench Topology, CIFAR-100; STL-10; CINIC-10; NAS-Bench-301; NATS-Bench Size, CIFAR-10; Oxford-IIIT Pet Dataset; ImageNet",PARAMS; Search time (s); Accuracy (Val); Spearman Correlation; Validation Accuracy; FLOPs; Spearman's Rho; Top-1 Error Rate; Percentage error; MACs,Activation Function Synthesis,Methodology,"**Neural architecture search (NAS)** is a technique for automating the design of artificial neural networks (ANN), a widely used model in the field of machine learning. NAS essentially takes the proce..."
2D Human Pose Estimation,27,C2A: Human Detection in Disaster Scenarios; Human-Art; JHMDB; RePoGen; SportsPose; NVIDIA Synthetic Head Dataset; MPII Human Pose Descriptions; Complain~How do I complain to Expedia?; Bizarre Pose Dataset; DeepSport Dataset,Alibaba Cluster Trace; ExLPose-LL-E; OCHuman; ExLPose-LL-H; Human-Art; ExLPose-OCN-A7M3; COCO-WholeBody; ExLPose-OCN-RICOH3; ExLPose-LL-N; JHMDB (2D poses only),WB; foot; Validation AP; hand; AP (gt bbox); face; 10-20% Mask PSNR; Test AP; PCK; body,Action Anticipation; Community Question Answering; Style Transfer; Articles; 3D Face Animation,Reasoning; Computer Vision; Knowledge Base,"What is Human Pose Estimation?  Human pose estimation is the process of estimating the configuration of the body (pose) from a single, typically monocular, image. Background. Human pose estimation is ..."
Emotion Classification,27,EmoPars; BanglaEmotion; ROCStories; SILICONE Benchmark; PhyMER; Human faces with mixed-race & various emotions; REN-20k Dataset; CAER-Dynamic; EMOPIA; ISEAR,ShortPersianEmo; CAER-Dynamic; CMU-MOSEI; RAVDESS; ROCStories; ArmanEmo; EWALK; SemEval 2018 Task 1E-c; MFA,V-F1 score (NA); F1; F-F1 score (Comb.); Weighted Accuracy; Macro F1; F-F1 score (Persian); Top-1 Accuracy; Accuracy; V-F1 score (Persian); Macro-F1,N/A,Computer Vision; Natural Language Processing,"Emotion classification, or emotion categorization, is the task of recognising emotions to classify them into the corresponding category. Given an input, classify it as 'neutral or no emotion' or as on..."
Visual Localization,27,Aachen Day-Night; PhotoSynth; Virtual Gallery; CrossLoc Benchmark Datasets; SeasonDepth; Danish Airs and Grounds; Long-term visual localization; NCLT; Robust e-NeRF Synthetic Event Dataset; Cambridge Landmarks,Oxford RobotCar Full; RobotCar Seasons v2; Extended CMU Seasons; Oxford Radar RobotCar (Full-6); Aachen Day-Night v1.1 Benchmark,"Acc @ .5m, 5°; Mean Translation Error; Acc@0.5m, 5°; Acc @ 5m, 10°; Acc@5m, 10°; Acc@0.25m, 2°; Acc @ .25m, 2°",N/A,Computer Vision,"**Visual Localization** is the problem of estimating the camera pose of a given image relative to a visual representation of a known scene.   <span class=""description-source"">Source: [Fine-Grained Seg..."
Visual Odometry,26,"MineNav; BPOD; KAIST VIO Dataset; ALTO; SLAM2REF; Virtual KITTI; Multi-Spectral Stereo Dataset  (RGB, NIR, thermal images, LiDAR, GPS/IMU); AUT-VI; Aqualoc; BASEPROD",EuRoC MAV,Relative Position Error Translation [cm],Face Anti-Spoofing; Monocular Visual Odometry,Robots; Computer Vision,"**Visual Odometry** is an important area of information fusion in which the central aim is to estimate the pose of a robot using data collected by visual sensors.   <span class=""description-source"">So..."
Intent Detection,26,Dialogue State Tracking Challenge; SPEECH-COCO; Almawave-SLU; NoMusic; MultiWOZ; BANKING77-OOS; MIPD; diaforge-utc-r-0725; HWU64; HINT3,Dialogue State Tracking Challenge; HWU64 10-shot; ASOS.com user intent; CLINC150 5-shot; HWU64; HWU64 5-shot; MULTIWOZ 2.2; ATIS; ATIS (vi); CLINC150 10-shot,Accuracy (%); F1; f1 macro; Intent Accuracy; Accuracy; Acc; Accuarcy,Open Intent Detection,Natural Language Processing,**Intent Detection** is a task of determining the underlying purpose or goal behind a user's search query given a context. The task plays a significant role in search and recommendations.   A traditio...
DeepFake Detection,26,WaveFake; DeepSpeak Dataset v1.0; DeePhy; FaceForensics++; LAV-DF; DeepFake MNIST+; KoDF; GANGen-Detection; WildDeepfake; Celeb-DF,DFFD; COCOFake; วีเค..!! ชมคลิป ‘ไอซ์ ปรีชญา’ ลืมปิดถ่ายทอดสดขณะอาบน้ำ โด; FaceForensics++; FakeAVCeleb; LAV-DF; CIFAKE: Real and AI-Generated Synthetic Images; 1; DFDC; ^(#$!@#$)(()))******,0..5sec; AP; Accuracy (%); Real; FS; Total Accuracy; ROC AUC; FSF; Accuracy; DF,diffusion-generated faces detection; Multimodal Forgery Detection; Human Detection of Deepfakes; Audio Deepfake Detection; Synthetic Speech Detection,Miscellaneous; Speech; Computer Vision; Audio,**DeepFake Detection** is the task of detecting fake videos or images that have been generated using deep learning techniques. Deepfakes are created by using machine learning algorithms to manipulate ...
Music Information Retrieval,26,CAL10K; RWC; YouTube-100M; MuMu; EMOPIA; Guitar-TECHS; iKala; Haydn Annotation Dataset; Lakh Pianoroll Dataset; Niko Chord Progression Dataset,N/A,N/A,N/A,Music,N/A
Video Prediction,25,Moving Symbols; EarthNet2021; SynPick; PHYRE; Cityscapes; YouTube-8M; DAVIS; QST; MNIST; Robotic Pushing,"Kinetics-600 12 frames, 64x64; Cityscapes; YouTube-8M; KTH 64x64 cond10 pred30; Something-Something V2; Human3.6M; BAIR Robot Pushing; SynpickVP; DAVIS 2017; CMU Mocap-1",LPIPS; Average PSNR; Test Error; Params (M); Pred; PSNR; ST-RRED; MSE; FVD; Train,Predict Future Video Frames; Earth Surface Forecasting,Time Series; Computer Vision,Script for Amee Marketing & Trading Company Short Video    *(Duration: 45-60 seconds)*      ---    Opening Scene (0:00-0:05):    - *Visual:* Close-up of fresh organic grains spilling gently into a woo...
Text-to-Image Generation,25,RichHF-18K; Human-Art; CUB-200-2011; HRS-Bench; Conceptual Captions; Paper2Fig100k; Fashion-Gen; GLAMI-1M; ENTIGEN; Flickr-8k,DPG; Multi-Modal-CelebA-HQ; Conceptual Captions; CUB; DrawBench; Colors; GenEval; MS-COCO; Oxford 102 Flowers; COCO,Complex; Inception score; FID-4; Zero shot FID; Overall; LPIPS; Real; rsim; Shape; Validation Accuracy,Conditional Text-to-Image Synthesis; Consistent Character Generation; text-guided-image-editing; Concept Alignment; Zero-Shot Text-to-Image Generation,Computer Vision; Natural Language Processing,"The development of the brain's blood supply in an embryo involves a complex process with several stages. Initially, there is a network of connections between the carotid and basilar artery systems tha..."
Few-Shot Image Classification,24,CIFAR-FS; AwA2; CUB-200-2011; MineralImage5k; Omni-Image; mini-Imagenet; FewSOL; ImageNet; tieredImageNet; Caltech-256,"CIFAR-FS - 5-Shot Learning; Flowers-102 - 0-Shot; ORBIT Clean Video Evaluation; SUN - 0-Shot; Mini-Imagenet 20-way (5-shot); CUB-200-2011 - 0-Shot; Mini-Imagenet 5-way (5-shot); Dirichlet Mini-Imagenet (5-way, 5-shot); ImageNet-FS (5-shot, novel); Mini-ImageNet - 1-Shot Learning",Avg. Accuracy; Top-5 Accuracy; Top-5 Accuracy (%); ACCURACY; Top 1 Accuracy; Frame accuracy; AP50; Top-1 Accuracy; Accuracy; Mean Rank,Unsupervised Few-Shot Image Classification; Generalized Few-Shot Classification; Unsupervised Few-Shot Learning,Computer Vision,**Few-Shot Image Classification** is a computer vision task that involves training machine learning models to classify images into predefined categories using only a few labeled examples of each categ...
Face Verification,24,23 Pairs of Identical Twins Face Image Data; UMDFaces; RFW; BTS3.1; XQLFW; CASIA-WebFace; VGG Face; QMUL-SurvFace; Oulu-CASIA; DroneSURF,BTS3.1; CASIA NIR-VIS 2.0; CPLFW; Oulu-CASIA NIR-VIS; AgeDB-30; QMUL-SurvFace; Oulu-CASIA; CFP-FP; BUAA-VisNir; Labeled Faces in the Wild,TAR @ FAR=0.0001; TAR@FAR=0.0001; Rank-1 (Video2Single); Rank-1 (Video2Video); TAR @ FAR=0.001; TAR @ FAR=0.01; Rank-1 (Video2Booking); TAR @ FAR=1e-5; TAR @ FAR=1e-4; training dataset,Disguised Face Verification,Computer Vision,**Face Verification** is a machine learning task in computer vision that involves determining whether two facial images belong to the same person or not. The task involves extracting features from the...
Out-of-Distribution Detection,24,NINCO; Icons-50; OpenImage-O; COVID-19 Twitter Chatter Dataset; ImageNet-1k vs Textures; STL-10; iSUN; Fashion-MNIST; ImageNet-1k vs NINCO; ADE-OoD,CIFAR-100 vs SVHN; SVHN vs Uniform; ImageNet-1K vs SSB-hard; CIFAR-10 vs Gaussian; CIFAR-10 vs iSUN; CIFAR-10 vs ImageNet (C); cifar10; ImageNet-1k vs Textures; CIFAR-100 vs LSUN (R); ImageNet-1K vs ImageNet-C,"ID ACC; FPR@95; Percentage correct; FPR95; AUPR; Latency, ms; AUROC; AP",N/A,Computer Vision,Detect out-of-distribution or anomalous examples.
Cross-Modal Retrieval,24,Recipe1M+; CTC; ChEBI-20; Twitter100k; ChineseFoodNet; CiNAT-Birds-2021; RSITMD; Flickr30k; SemArt; PASCAL VOC 2007,Recipe1M+; RSITMD; ChEBI-20; COCO 2014; MS-COCO-2014; Flickr30k; RSICD; MSCOCO; MSCOCO-1k; Recipe1M,Text-to-image R@1; Mean Recall; Test MRR; Hits@1; Text-to-image R@10; Hits@10; Image-to-text R@10; text-to-image R@1; text-to-imageR@1; Sound-to-image R@100,multilingual cross-modal retrieval; Cross-modal retrieval with noisy correspondence; Image-text matching; Zero-shot Composed Person Retrieval; Cross-Modal Retrieval on RSITMD,Computer Vision; Natural Language Processing,"**Cross-Modal Retrieval (CMR)** is a task of retrieving items across different modalities, such as image, text, video, and audio. The core challenge of CMR is the *heterogeneity gap*, which arises bec..."
Video Generation,24,YouTube Driving; Deep Fakes Dataset; How2Sign; DropletVideo-10M; MSR-VTT; OpenS2V-5M; UCF101; QST; AVSync15; TLFM dataset,"Kinetics-600 48 frames, 64x64; YouTube Driving; UCF-101 16 frames, Unconditional, Single GPU; How2Sign; Sky Time-lapse; BAIR Robot Pushing; Kinetics-600 12 frames, 128x128; Kinetics-600 12 frames, 64x64; TrailerFaces; LAION-400M",LPIPS; FVD16; Pred; Inception Score; PSNR; CLIP; Inception score; FVD128; FID; FVD,Unconditional Video Generation; Image to Video Generation,Computer Vision; Natural Language Processing,"<span style=""color:grey; opacity: 0.6"">( Various Video Generation Tasks.  Gif credit: [MaGViT](https://paperswithcode.com/paper/magvit-masked-generative-video-transformer) )</span>"
Speech Enhancement,24,ReVerb Challenge; GneutralSpeech Female; AVA-ActiveSpeaker; LibriMix; VoiceBank + DEMAND; GRID Dataset; RealMAN; TIMIT; CAS-VSR-S101; CHiME-5,DEMAND; GRID corpus (mixed-speech); RealMAN; LibriSpeechDuplicate; DNS-4; CHiME-3; VoiceBank+DEMAND; spatialized DNS challenge; TCD-TIMIT corpus (mixed-speech); VB-DemandEx,PESQ-WB; DNSMOS SIG; Para. (M); SSNR; COVL; DNSMOS BAK; SI-SDR-NB; FLOPS (G); ViSQOL; DNSMOS OVRL,Speech Intelligibility Evaluation; Bandwidth Extension; Packet Loss Concealment; Speech Dereverberation,Speech; Audio,**Speech Enhancement** is a signal processing task that involves improving the quality of speech signals captured under noisy or degraded conditions. The goal of speech enhancement is to make speech s...
Crowd Counting,23,RSOC; SmartCity; CrowdFlow; Crowd in a rally | Crowd Counting | Crowd Human; DLR-ACD; CVCS; Multi Task Crowd; Cross-View Cross-Scene Multi-View Crowd Counting Dataset; UP-COUNT; WWW Crowd,N/A,N/A,N/A,N/A,N/A
Human-Object Interaction Detection,23,H2O; MECCANO; FineGym; EgoISM-HOI; V-HICO; HAKE; VidHOI; COUCH; H²O Interaction; Watch-n-Patch,MECCANO; HICO-DET; HICO; VidHOI; Ambiguious-HOI; V-COCO,mAP; AP(S2); Oracle: Non-Rare (mAP@0.5); Time Per Frame (ms); Oracle: Rare (mAP@0.5); Detection: Rare (mAP@0.5); Detection: Non-Rare (mAP@0.5); AP(S1); Oracle: Full (mAP@0.5); Time Per Frame(ms),Hand-Object Interaction Detection; Affordance Recognition,Computer Vision,"Human-Object Interaction (HOI) detection is a task of identifying ""a set of interactions"" in an image, which involves the i) localization of the subject (i.e., humans) and target (i.e., objects) of in..."
Denoising,23,SIDD; 2DeteCT; CommitBART; DND; FMD; CBSD68; CRVD; PointDenoisingBenchmark; iris; RawNIND,iris; CBSD68 sigm75; DND; DIV2K; Darmstadt Noise Dataset; AAPM,SSIM (sRGB); Average PSNR; Average; PSNR/SSIM; PSNR; Average PSNR (dB); SSIM,Salt-And-Pepper Noise Removal; Image Denoising; 3D Mesh Denoising; Sar Image Despeckling; Color Image Denoising,Computer Vision,"**Denoising** is a task in image processing and computer vision that aims to remove or reduce noise from an image. Noise can be introduced into an image due to various reasons, such as camera sensor l..."
Low-Light Image Enhancement,23,AFLW; DICM; MEF; Canon RAW Low Light; LOL-v2-synthetic; LLNeRF Dataset; LOL; SDSD-outdoor; ExDark; LOLv2-synthetic,N/A,N/A,N/A,N/A,N/A
6D Pose Estimation,23,OPT; GraspClutter6D; Trajectory calibration experiments; CORSMAL; YCB-Ev 1.1; UW Indoor Scenes (UW-IS) Occluded dataset; HouseCat6D; NERDS 360; SLAM2REF; LM,OPT; LineMOD; 3D-BSLS-6D; DTTD-Mobile; ApolloCar3D; YCB-Video,AR CoU; eRE; AR pCH; AUC; Accuracy (ADD); AR CH; ADDS AUC; ADD-S AUC; ADD AUC; Mean ADD-S,hand-object pose; Robot Pose Estimation,Computer Vision,Image: [Zeng et al](https://arxiv.org/pdf/1609.09475v3.pdf)
Hand Pose Estimation,23,Custom FINNgers; BigHand2.2M Benchmark; ContactArt; 3D Hand Pose; 3DPW; Surgical Hands; EgoDexter; ThermoHands; ARCTIC; MuViHand,NYU Hands; MSRA Hands; ICVL; HANDS 2017; ICVL Hands; Custom FINNgers; COCO-WholeBody; 3DPW; K2HPD; HANDS 2019,FPS; keypoint AP; Average 3D Error; Error (mm); PDJ@5mm; 1:1 Accuracy; MPJPE,3D Hand Pose Estimation,Computer Vision; Graphs,"Hand pose estimation is the task of finding the joints of the hand from an image or set of video frames.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Pose-REN](https://github.com/xinghao..."
Scene Classification,23,DCASE 2013; BigEarthNet; LITIS Rouen; Million-AID; AIDER; DCASE 2019 Mobile; UC Merced Land Use Dataset; Places365; RS_NS92; TAU Urban Acoustic Scenes 2019,UC Merced Land Use Dataset; Places365-Standard,Accuracy (%); Top 1 Error; Top 5 Error,N/A,Computer Vision,"**Scene Classification** is a task in which scenes from photographs are categorically classified. Unlike object classification, which focuses on classifying prominent objects in the foreground, Scene ..."
Automatic Speech Recognition,22,VoxPopuli; MASC; MediBeng; Jam-ALT; BERSt; FLEURS; Video Dataset; OpenSLR; SPGISpeech; LJSpeech,N/A,N/A,N/A,N/A,N/A
Speech Emotion Recognition,22,BERSt; SEWA DB; RESD; MSP-Podcast; MNIST; SES; JTES; Dusha; LSSED; CREMA-D,EmoDB Dataset; EMODB; MSP-IMPROV; RESD; LSSED; Quechua-SER; CREMA-D; RAVDESS; MSP-Podcast (Valence); Dusha Crowd,Unweighted Accuracy (UA); Weighted Accuracy (WA); Recall; Unweighted Accuracy; Weighted F1; Precision; F1; WA; F1 Score; CCC (Arousal),Cultural Vocal Bursts Intensity Prediction; Vocal Bursts Type Prediction; Vocal Bursts Valence Prediction; Vocal Bursts Intensity Prediction,Speech,**Speech Emotion Recognition** is a task of speech processing and computational paralinguistics that aims to recognize and categorize the emotions expressed in spoken language. The goal is to determin...
Video Classification,22,Multimodal PISA; MOMA-LRG; Video Dataset; YouTube-8M; Something-Something V1; Charades; Bukva; COIN; Crowd 11; Breakfast,YouTube-8M; SRI-APPROVE Fine-Grained Video Classification; Multimodal PISA; Something-Something V1; Something-Something V2; Charades; MoB; Kinetics; Home Action Genome; COIN,Accuracy (%); Top-5 Accuracy; mAP; Hit@1; AUPR; Accuracy; Global Average Precision; Top-1; PERR; Hit@5,Student Engagement Level Detection (Four Class Video Classification); Multi Class Classification (Four-level Video Classification),Computer Vision,"**Video Classification** is the task of producing a label that is relevant to the video given its frames. A good video level classifier is one that not only provides accurate frame labels, but also be..."
Image Denoising,22,Phase 1 dataset; RENOIR; Urban100; M4Raw; ELD; SIDD; 2DeteCT; DND; FMD; CBSD68,FFHQ 64x64 - 4x upscaling; SID SonyA7S2 x250; SIDD; ELD SonyA7S2 x200; DND; BSD68 sigma30; FMD; SID SonyA7S2 x300; FFHQ; Image Denoising on SID x300,LPIPS; PSNR (sRGB); SSIM (sRGB); Average PSNR; PSNR; PSNR (Raw); ODRMSE; SSIM (Raw); SSIM,lifetime image denoising; intensity image denoising,Medical; Computer Vision,"**Image Denoising** is a computer vision task that involves removing noise from an image. Noise can be introduced into an image during acquisition or processing, and can reduce image quality and make ..."
Cell Segmentation,22,NIH3T3; Fluo-N2DH-GOWT1; PhC-C2DH-U373; PanNuke; ACCT Data Repository; DigestPath; DIC-C2DH-HeLa; ALFI; YIM Dataset; LIVECell,MoNuSeg; Fluo-C3DL-MDA231; CoNSeP; Fluo-N2DH-SIM+; EVICAN; Fluo-N2DH-GOWT1; DIC-C2DH-HeLa; STARE; Fluo-N2DL-HeLa; LIVECell,LIVECell Transferability; LIVECell Extrapolation (A172); SEG (~Mean IoU); Average Dice; mask AFNR; mask AP50; LIVECell Extrapolation (A549); AUC; mask AP,Nuclei Segmentation and Classfication,Computer Vision,"**Cell Segmentation** is a task of splitting a microscopic image domain into segments, which represent individual instances of cells. It is a fundamental step in many biomedical studies, and it is reg..."
Speech Synthesis,22,TaL Corpus; SOMOS; THCHS-30; Silent Speech EMG; United-Syn-Med; JIT Dataset; LibriTTS; LJSpeech; RUSLAN; CSS10,LibriTTS; LJSpeech; Mandarin Chinese; Blizzard Challenge 2013; North American English,Periodicity; MCD; NLL; Mean Opinion Score; M-STFT; V/UV F1; PESQ,Speech Synthesis - Gujarati; Speech Synthesis - Assamese; Speech Synthesis - Hindi; Speech Synthesis - Rajasthani; Speech Synthesis - Malayalam,Speech; Audio,"Speech synthesis is the task of generating speech from some other modality like text, lip movements etc.     Please note that the leaderboards here are not really comparable between studies - as they ..."
Super-Resolution,22,Holopix50k; TESTIMAGES; MSU SR-QA Dataset; Alsat-2B; QMUL-SurvFace; StereoMSI; SICKLE; CATS; TextZoom; Stanford Light Field,hradis et al dataset,Average PSNR; SSIM,3D Object Super-Resolution; Video Super-Resolution; Depth Map Super-Resolution; Image Rescaling; Reference-based Video Super-Resolution,Computer Vision; Graphs,**Super-Resolution** is a task in computer vision that involves increasing the resolution of an image or video by generating missing high-frequency details from low-resolution input. The goal is to pr...
Handwriting Recognition,22,BRUSH; Konzil; Burmese Handwritten Digit Dataset (BHDD); DigiLeTs; BN-HTRd; IAM; Patzig; Schiller; Bentham; BanglaLekha-Isolated,KOHTD; An extensive dataset of handwritten central Kurdish isolated characters; BanglaLekha Isolated Dataset,Cross Entropy Loss; CER; Epochs; Accuracy; 1:1 Accuracy,Handwritten Line Segmentation; Handwritten Word Segmentation,Computer Vision,Image source: [Handwriting Recognition of Historical Documents with few labeled data](https://arxiv.org/pdf/1811.07768v1.pdf)
Molecular Property Prediction,21,QM7; Photoswitch; SIDER; MoleculeNet; QM8; Tox21; Molecule3D; PubChem18; HIV (Human Immunodeficiency Virus); FreeSolv (Free Solvation),Lipophilicity; ToxCast; QM7; MUV; QM8; ESOL; FreeSolv; QM9; SIDER; Clearance,ROC-AUC; RMSE; R2; Molecules (M); MAE; AUC,Odor Descriptor Prediction; mixture property prediction; NMR J-coupling; 3D Geometry Prediction,Miscellaneous; Medical; Methodology; Graphs,Molecular property prediction is the task of predicting the properties of a molecule from its structure.
Simultaneous Localization and Mapping,21,MAOMaps; Hilti SLAM Challenge; KAIST VIO Dataset; SLAM2REF; Virtual KITTI; Robust e-NeRF Synthetic Event Dataset; S3E; BASEPROD; TUM RGB-D; ConSLAM,N/A,N/A,Semantic SLAM; Object SLAM,Computer Vision,Simultaneous localization and mapping (SLAM) is the task of constructing or updating a map of an unknown environment while simultaneously keeping track of an agent's location within it.    <span style...
Medical Diagnosis,21,BreastDICOM4; BreastClassifications4; Niramai Oncho Dataset; MedConceptsQA; Liver-US; EBHI-Seg; REFLACX; LLM Health Benchmarks; Kvasir-VQA; Sakha-TB,Clinical Admission Notes from MIMIC-III; BreastDICOM4,Average Recall; Average Precision; AUROC,Retinal OCT Disease Classification; Alzheimer's Disease Detection; Thoracic Disease Classification; Blood Cell Count; CBC TEST,Medical; Computer Vision,"**Medical Diagnosis** is the process of identifying the disease a patient is affected by, based on the assessment of specific risk factors, signs, symptoms and results of exams.      <span class=""desc..."
Sound Event Detection,20,WildDESED; DCASE 2013; TUT-SED Synthetic 2016; URBAN-SED; TAU-NIGENS Spatial Sound Events 2020; BIOSED-ACPD; SINGA:PURA; L3DAS22; TUT Sound Events 2018; TAU-NIGENS Spatial Sound Events 2021,Mivia Audio Events; WildDESED; DESED; L3DAS21; Mivia Road Events,PSDS1 (Clean); SED-score; Rank-1 Recognition Rate; PSDS1; PSDS1 (10dB); event-based F1 score; PSDS2; PSDS1 (-5dB); Error Rate; PSDS1 (0dB),N/A,Audio,"**Sound Event Detection** (SED) is the task of recognizing the sound events and their respective temporal start and end time in a recording. Sound events in real life do not always occur in isolation,..."
Traffic Prediction,20,METR-LA; Q-Traffic; NYCTaxi; PeMSD4; PeMSD8; CTV-Dataset; PeMS04; NYCBike1; GRD-TRT-BUF-4I Technical Validation Data; PeMSD7,"METR-LA; PeMSD8 (10 days' training data, 30min); HZME(outflow); Q-Traffic; PeMSD4 (10 days' training data, 60min); BJTaxi; PeMSD7(M); NYCTaxi; PeMSD4; PeMSD8",RMSE; MAE @ 12 step; MAPE (%) @ out; MAE; MAE @ out; MAPE; 1 step MAE; GBA MAE; FLOPs(M); 12 Steps MAE,Traffic Data Imputation,Time Series,"**Traffic Prediction** is a task that involves forecasting traffic conditions, such as the volume of vehicles and travel time, in a specific area or along a particular road. This task is important for..."
Aspect-Based Sentiment Analysis (ABSA),20,ACOS; Twitter Sentiment Analysis; SemEval-2014 Task-4; COVID19-CountryImage; TASD; CAIL2019-SCM; DiaASQ; ASTE; Laptop-ACOS; UIT-ViSFD,SemEval 2014 Task 4 Subtask 4; ASQP; ASTE; SemEval 2015 Task 12; Rest15; TASD; ACOS; FABSA; Sentihood;  SemEval 2015 Task 12,F1(R14); Restaurant (Acc); Mean Acc (Restaurant + Laptop); F1 (Laptop); F1 (L14); Restaurant (F1); Aspect; Accuracy (4-way); Binary Accuracy; F1 (R15),Extract Aspect; Aspect-oriented  Opinion Extraction; Extract aspect-polarity tuple; Aspect-Category-Opinion-Sentiment Quadruple Extraction; Aspect Category Sentiment Classification,Natural Language Processing,**Aspect-Based Sentiment Analysis (ABSA)** is a Natural Language Processing task that aims to identify and extract the sentiment of specific aspects or components of a product or service. ABSA typical...
Named Entity Recognition,20,OntoNotes 5.0; SIGARRA News Corpus; SciERC; CoNLL; NCBI Disease; ARF; CoNECo; LatamXIX; GUM; First HAREM,N/A,N/A,N/A,N/A,N/A
Change Detection,20,ChaBuD; Changen2-S1-15k; V-PCCD; WHU Building Dataset; BRIGHT; EGY-BCD; OSCD; CDD Dataset (season-varying); urban_change_monitoring_mariupol_ua; LEVIR-CD,DSIFN-CD; OSCD - 13ch; WHU Building Dataset; PCD; SYSU-CD; S2Looking; OSCD - 3ch; GoogleGZ-CD; EGY-BCD; SECOND,Precision; F1; Overall Accuracy; F1-score; KC; IoU; Category mIoU; Overal Accuracy; Fscd; F1-Score,Semi-supervised Change Detection,Computer Vision,"**Change Detection** is a computer vision task that involves detecting changes in an image or video sequence over time. The goal is to identify areas in the image or video that have undergone changes,..."
Stereo Matching,20,IMC PhotoTourism; Middlebury 2005; Helvipad; UASOL; Middlebury 2014; GUISS dataset; Virtual KITTI; CATS; DrivingStereo; PST900,N/A,N/A,N/A,Computer Vision,"**Stereo Matching** is one of the core technologies in computer vision, which recovers 3D structures of real world from 2D images. It has been widely used in areas such as autonomous driving, augmente..."
3D Hand Pose Estimation,20,BigHand2.2M Benchmark; HInt: Hand Interactions in the wild; HO-3D v3; 3D Hand Pose; H2O  (2 Hands and Objects); H3WB; EgoPAT3D-DT; EgoPAT3D; EgoDexter; ThermoHands,HO-3D v2; FreiHAND; HO-3D v3; HInt: Hand Interactions in the wild; InterHand2.6M; DexYCB; H3WB,PA-MPJPE (mm); PA-F@15mm; PA-MPJPE; MPVPE; VAUC; PCK@0.05 (VISOR) Occ; Procrustes-Aligned MPJPE; F@15mm; PA-F@5mm; AUC_V,Grasp Generation; 3D Canonical Hand Pose Estimation; hand-object pose,Computer Vision,Image: [Zimmerman et l](https://arxiv.xsrg/pdf/1705.01389v3.pdf)
Image Manipulation Detection,20,Casia V1+; DSO (OSN-transmitted - Facebook); Columbia (OSN-transmitted - Weibo); CASIA (OSN-transmitted - Facebook); NIST (OSN-transmitted - Wechat); Columbia (OSN-transmitted - Facebook); CASIA (OSN-transmitted - Wechat); DIS100k; CASIA (OSN-transmitted - Weibo); DSO (OSN-transmitted - Wechat),Casia V1+; DSO (OSN-transmitted - Facebook); Columbia (OSN-transmitted - Weibo); CASIA (OSN-transmitted - Facebook); NIST (OSN-transmitted - Wechat); Columbia (OSN-transmitted - Facebook); CASIA (OSN-transmitted - Wechat); CASIA (OSN-transmitted - Weibo); DSO-1; DSO (OSN-transmitted - Wechat),F-score; f-Score; Intersection over Union; Balanced Accuracy; AUC,N/A,Computer Vision,"The task of detecting images or image parts that have been tampered or manipulated (sometimes also referred to as doctored).  This typically encompasses image splicing, copy-move, or image inpainting."
Semantic Textual Similarity,19,Interpretable STS; GLUE; PARANMT-50M; SemEval-2014 Task-10; PIT; KorNLI; CzechNewsDatasetForSTS; SentEval; JGLUE; CxC,MRPC; STS13; MTEB; SICK-R; SICK; CxC; STS14; MRPC Dev; STS Benchmark; STS16,MRPC; avg ± std; SICK-R; F1; Dev Spearman Correlation; STS; Accuracy; Dev Pearson Correlation; SICK-E; Spearman Correlation,Cross-Lingual Semantic Textual Similarity; Paraphrase Identification,Natural Language Processing,Semantic textual similarity deals with determining how similar two pieces of texts are.  This can take the form of assigning a score from 1 to 5. Related tasks are paraphrase or duplicate identificati...
Medical Image Classification,19,Galaxy Zoo DECaLS; Malaria Dataset; MIMIC-CXR-LT; ISIC 2020 Challenge Dataset; OASIS; Heel Dataset; Liver-US; ImageNet; NCT-CRC-HE-100K; NIH-CXR-LT,IDRiD; PCOS Classification; Malaria Dataset; OASIS 3; Galaxy10 DECals; ISIC 2020 Challenge Dataset; ImageNet; ISIC 2017; NCT-CRC-HE-100K; CheXphoto,Accuracy (%); Specificity; Precision; Top 1 Accuracy; GFLOPs; Sensitivity; Accuracy; F1-Score; Mean AUC; Accuracy (% ),Semi-supervised Medical Image Classification,Medical,"**Medical Image Classification** is a task in medical image analysis that involves classifying medical images, such as X-rays, MRI scans, and CT scans, into different categories based on the type of i..."
Video Object Segmentation,19,PUMaVOS; MOSE; M$^3$-VOS; YouTube-VOS 2018; Infinity Spills Basic Dataset; AVSBench; VISOR - Semi supervised video object segmentation; DAVIS; Referring Expressions for DAVIS 2016 & 2017; BL30K,DAVIS 2017 (val); YouTube-VOS 2019; MOSE; DAVIS 2017 (test-dev); FBMS; M$^3$-VOS; YouTube; YouTube-VOS 2018; DAVIS 2016; DAVIS 2017,Jaccard (Mean); F-Measure (Seen); Average; Jaccard (Unseen); F-Measure (Unseen); Jaccard (Seen); Contour Accuracy; J&F; Jaccard; Mean Jaccard & F-Measure,Video Salient Object Detection; Video Shadow Detection; Long-tail Video Object Segmentation; Interactive Video Object Segmentation; Semi-Supervised Video Object Segmentation,Computer Vision,Video object segmentation is a binary labeling problem aiming to separate foreground object(s) from the background region of a video.    For leaderboards please refer to the different subtasks.
Visual Navigation,19,Replica; House3D Environment; Luna-1; Talk2Nav; AVD; MineRL; UAV Multiview Navigation; HELP; Talk the Walk; HM3DSem,"Cooperative Vision-and-Dialogue Navigation; SOON Test; R2R; Help, Anna! (HANNA); Dmlab-30; AI2-THOR",Medium Human-Normalized Score; SR; Nav-SPL; Success Rate (All); SPL (All); dist_to_end_reduction; SPL (L≥5); Success Rate (L≥5); spl,ObjectGoal Navigation,Robots; Computer Vision,"**Visual Navigation** is the problem of navigating an agent, e.g. a mobile robot, in an environment using camera input only. The agent is given a target image (an image it will see from the target pos..."
Animal Pose Estimation,19,Grévy’s Zebra; SuperAnimal-Quadruped; Animal3D; TriMouse-161; ATRW; Desert Locust; MacaquePose; LoTE-Animal; StanfordExtra; 3D-POP,AP-10K; Animal-Pose Dataset; Fish-100; Animal3D; StanfordExtra; TriMouse-161; Horse-10; Marmoset-8K,mAP; PCK@0.1; PA-MPJPE; Normalized Error (OOD); PCK@0.3 (OOD); AP,N/A,Computer Vision,"Animal pose estimation is the task of identifying the pose of an animal.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Using DeepLabCut for 3D markerless pose estimation across species an..."
Robot Navigation,19,MotIF-1K; ISOD; SESIV; PInNED; MLGESTURE DATASET; SFU-Store-Nav; Gibson Environment; QDax; TAO; BASEPROD,Habitat 2020 Object Nav test-std; Habitat 2020 Point Nav test-std; Habitat 2020 Point Nav minival; Habitat 2020 Object Nav minival,SPL; SUCCESS; DISTANCE_TO_GOAL; SOFT_SPL,VNLA; PointGoal Navigation; ObjectGoal Navigation; Sequential Place Learning; Social Navigation,Robots; Computer Vision,The fundamental objective of mobile **Robot Navigation** is to arrive at a goal position without collision. The mobile robot is supposed to be aware of obstacles and move freely in different working s...
Motion Synthesis,19,KIT Motion-Language; HumanML3D; Motion-X; FineDance; TMD; BOTH57M; Guitar Playing Motion Dataset; AIST++; InterHuman; LaFAN1,HumanML3D; Motion-X; KIT Motion-Language; AIST++; FineDance; TMD; BRACE; InterHuman; Trinity Speech-Gesture Dataset; Inter-X,GenDiv; BAS; PFC; L2Q@5; MModality; TIF; L2Q@15; L2Q@30; Frechet Inception Distance; MMC,Motion Style Transfer; Temporal Human Motion Composition; motion in-betweening,Computer Vision; Computer Code,"Creating a video where people in the images move (such as blinking or smiling) requires specialized AI technology like Deepfake or Motion Synthesis, which I cannot do directly here.      However, if y..."
Transductive Zero-Shot Classification,18,Food-101; FGVC-Aircraft; AID; Oxford-IIIT Pets; Oxford 102 Flower; Stanford Cars; CIFAR-100; PatternNet; SUN397; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Object Localization,18,GRIT; IllusionVQA; HRPlanesV2; HDD; SolarDK; Mall; Spiideo SoccerNet SynLoc; PASCAL VOC 2007; Spatial Commonsense Graph Dataset; RF100,GRIT; IllusionVQA; Plant; KITTI Cars Moderate; KITTI Cars Hard; KITTI Pedestrian Easy; PASCAL VOC 2007; KITTI Cyclists Moderate; Pupil; KITTI Cars Easy,Recall; Localization (ablation); Nav-SPL; Precision; RGS; CorLoc; Localization (test); RGSPL; Nav-OSucc; Nav-Length,Active Object Localization; Unsupervised Object Localization; Image-Based Localization; Monocular 3D Object Localization; Weakly-Supervised Object Localization,Computer Vision,"**Object Localization** is the task of locating an instance of a particular object category in an image, typically by specifying a tightly cropped bounding box centered on the instance. An object prop..."
Multiple Object Tracking,18,RADIATE; SOMPT22; YouTube-Hands; SportsMOT; PersonPath22; CFC; HiEve; MOTChallenge; BDD100K; Waymo Open Dataset,BDD100K test; RADIATE; YouTube-Hands; SportsMOT; CroHD; KITTI Test (Offline Methods); Waymo Open Dataset; GMOT-40; KITTI Test (Online Methods); BDD100K val,IDEucl; AssA; ML; mAP; MOTA; IDF1; DetA; HOTA; IDs; Category,Multiple Object Tracking with Transformer; Multiple Object Track and Segmentation,Computer Vision,"**Multiple Object Tracking** is the problem of automatically identifying multiple objects in a video and representing them as a set of trajectories with high accuracy.   <span class=""description-sourc..."
Gaze Estimation,18,MPSGaze; EGO-CH-Gaze; GAFA; RT-GENE; EYEDIAP; LISA Gaze Dataset; UCO-LAEO; Gaze360; GD; EyeInfo,MPSGaze; RT-GENE; Gaze360; EYEDIAP; EYEDIAP (screen target); ETH-XGaze; EYEDIAP (floating target); MPII Gaze; GazeCapture; MPIIGaze,FPS; Euclidean Mean Error (EME); Angular Error; Mean Angle Error,N/A,Computer Vision,**Gaze Estimation** is a task to predict where a person is looking at given the person’s full face. The task contains two directions: 3-D gaze vector and 2-D gaze position estimation. 3-D gaze vector ...
Action Segmentation,18,InHARD; Watch-n-Patch; SICS-155; TUM Kitchen; HA-ViD; COIN; HOI4D; JIGSAWS; LARa; 50 Salads,COIN; JIGSAWS; Youtube INRIA Instructional; 50 Salads; 50Salads; Assembly101; MPII Cooking 2 Dataset; GTEA; Breakfast,F1@10%; Edit Distance; Average F1; F1@10; F1; F1@50; F1@25%; Frame accuracy; Edit; MoF,Unsupervised Action Segmentation; Skeleton Based Action Segmentation; Weakly Supervised Action Segmentation (Action Set)); Weakly Supervised Action Segmentation (Transcript),Computer Vision,"**Action Segmentation** is a challenging problem in high-level video understanding. In its simplest form, Action Segmentation aims to segment a temporally untrimmed video by time and label each segmen..."
3D Instance Segmentation,18,XA Bin-Picking; ScanNet200; FOR-instance; SceneNN; ScanNet++; MultiScan; Fraunhofer EZRT XXL-CT Instance Segmentation Me163; CREMI; MitoEM; PartNet,ScanNet200; ScanNet(v2); SceneNN; ScanNet++; PartNet; MitoEM; ScanNet; S3DIS; STPLS3D,mAP@0.5; AP75-H-Val; mAcc; AP75-R-Test; AP75-R-Val; mAP50; AP50; AP25; AP75-H-Test; mRec,Interactive 3D Instance Segmentation,Computer Vision,Image: [OccuSeg](https://arxiv.org/pdf/2003.06537v3.pdf)
Text-To-Speech Synthesis,18,EMOVIE; Thorsten voice 21.02 neutral; RyanSpeech; SOMOS; AISHELL-3; LibriTTS; 20000 utterances; CVSS; HUI speech corpus; Gumar Corpus,Thorsten voice 21.02 neutral; 20000 utterances; HUI speech corpus; CMUDict 0.7b; LJSpeech; Trinity Speech-Gesture Dataset,WER (%); 10-keyword Speech Commands dataset; Audio Quality MOS; Phoneme Error Rate; Mean Opinion Score; Pleasantness MOS; MOS; Word Error Rate (WER),Zero-Shot Multi-Speaker TTS; Prosody Prediction,Speech; Audio; Natural Language Processing,**Text-To-Speech Synthesis** is a machine learning task that involves converting written text into spoken words. The goal is to generate synthetic speech that sounds natural and resembles human speech...
Zero-Shot Video Question Answer,18,NExT-GQA; TVQA+; TVQA; Neptune; Video-MME; NExT-QA; MSRVTT-QA; Shot2Story20K; IntentQA; TGIF-QA,N/A,N/A,N/A,N/A,N/A
Image Restoration,18,HIDE; CDD-11; L1BSR; HRI; PIRM; RawNIND; AWMM-100k; UHDM; HAC; CBSD68,^(#$!@#$)(()))******; CDD-11; UHDM,PSNR; Average PSNR (dB); SSIM; 0L,Spectral Reconstruction; Unified Image Restoration; JPEG Artifact Correction; Flare Removal; JPEG Artifact Removal,Computer Vision,"**Image Restoration** is a family of inverse problems for obtaining a high quality image from a corrupted input image. Corruption may occur due to the image-capture process (e.g., noise, lens blur), p..."
Image Enhancement,18,LoLI-Street; SICE-Mix; IRBFD; PolarRR; Nikon RAW Low Light; MIT-Adobe FiveK; UHDM; SICE-Grad; UIEB; SQUID,SICE-Mix; PSNR; MIT-Adobe 5k; MIT-Adobe FiveK; SICE-Grad; TIP 2018; Exposure-Errors,LPIPS; Average PSNR; PSNR on proRGB; PSNR on sRGB; PSNR; FSIM; SSIM on sRGB; SSIM on proRGB; SSIM; DeltaE,Low-Light Image Enhancement; Local Color Enhancement; Vignetting Removal; De-aliasing; Color Manipulation,Computer Vision,**Image Enhancement** is basically improving the interpretability or perception of information in images for human viewers and providing ‘better’ input for other automated image processing techniques....
Semantic Similarity,17,COS960; SugarCrepe++; Phrase-in-Context; Semantic Question Similarity in Arabic; SV-Ident; Names pairs dataset; SICK; BIOSSES; SpokenSTS; LatamXIX,sts dev; sts dev 512; Unknown; stsb multi mt en; test t; CHIP-STS; sts17 ua ua; sts17 es en test; MedSTS; JSTS,Precision; F1; MSE; Macro F1; Recall; Spearman Correlation; Pearson Correlation,Similarity Explanation; Semantic Shift Detection,Methodology; Natural Language Processing,"The main objective **Semantic Similarity** is to measure the distance between the semantic meanings of a pair of words, phrases, sentences, or documents. For example, the word “car” is more similar to..."
Face Alignment,17,3DFAW; CelebA; FaceScape; AFLW; Helen; COFW; SAMM Long Videos; MERL-RAV; LFPW; AFW,N/A,N/A,N/A,N/A,N/A
Image Inpainting,17,FVI; Apolloscape Inpainting; DREAMING Inpainting Dataset; CelebA; FDF; QST; Unsplash_1k; PAL4Inpaint; CASIA V2; Fashion-Gen,Apolloscape Inpainting; FFHQ 1024 x 1024; Places2; CelebA; Paris StreetView; Places2 val; ImageNet; FFHQ 512 x 512; ApolloScape; Places365,LPIPS; 30-40% Mask PSNR; RMSE; PSNR; U-IDS; 10-20% Mask PSNR; FID; 40-50% Mask PSNR; P-IDS; PD,Facial Inpainting; Cloud Removal; Fine-Grained Image Inpainting,Computer Vision,Remove the preview tag from template
Image Dehazing,17,Rendered WB dataset; RESIDE; NH-HAZE; RS-Haze; AWMM-100k; SMOKE; D-HAZY; DENSE; I-HAZE; SQUID,RESIDE; SOTS Outdoor; HD-NH-HAZE; NH-HAZE; RS-Haze; RESIDE-6K; NH-HAZE2; O-Haze; I-Haze; RB-Dust,PSNR; SSIM,N/A,Computer Vision,"<span style=""color:grey; opacity: 0.6"">( Image credit: [Densely Connected Pyramid Dehazing Network](https://github.com/hezhangsprinter/DCPDN) )</span>"
3D Object Tracking,17,Argoverse 2 Motion Forecasting; The RBO Dataset of Articulated Objects and Interactions; DTTD; TrackML challenge Throughput phase dataset; K-Radar; Argoverse; MOTFront; SimBEV; RTB; Aria Digital Twin Dataset,Argoverse CVPR 2020; RTB,AVG-RANK; Runtime [ms]; ADDS AUC,3D Single Object Tracking,Computer Vision,3D Object Tracking is a computer vision task dedicated to monitoring and precisely locating objects as they navigate within a three-dimensional environment. It frequently utilizes 3D object detection ...
Action Recognition In Videos,17,NTU RGB+D; ActivityNet; PKU-MMD; Something-Something V2; Something-Something V1; THUMOS14; Kinetics; Sports-1M; Kinetics 400; AVA,miniSports; Something-Something V1; NTU RGB+D; Something-Something V2; ActivityNet; PKU-MMD; AVA v2.1; FS-Something-Something V2-Full; Sports-1M; AVA v2.2,Top-5 Accuracy; mAP@0.5; Video hit@1; mAP@0.4; 3-fold Accuracy; X-View; Accuracy (CS); Top-1 Accuracy(5-Way-5-Shot); mAP@0.3; Top 1 Accuracy,Action Anticipation,Computer Vision,**Action Recognition in Videos** is a task in computer vision and pattern recognition where the goal is to identify and categorize human actions performed in a video sequence. The task involves analyz...
Scene Text Detection,17,Total-Text; ICDAR 2015; ICDAR 2013; PKU (License Plate Detection); RCTW-17; CNTD; SCUT-CTW1500; Chinese Text in the Wild; UrduDoc; TextOCR,Total-Text; IC19-Art; ICDAR 2013; ICDAR 2015; SCUT-CTW1500; ICDAR 2017 MLT; IC19-ReCTs; MSRA-TD500; COCO-Text,FPS; Precision; H-Mean; Accuracy; F-Measure; Recall,Multi-Oriented Scene Text Detection; Curved Text Detection,Computer Vision,**Scene Text Detection** is a computer vision task that involves automatically identifying and localizing text within natural images or videos. The goal of scene text detection is to develop algorithm...
Video Anomaly Detection,17,HR-ShanghaiTech; UCSD Ped2; CUHK Avenue; CamNuvem Dataset; HR-UBnormal; ShanghaiTech; GoodsAD; Street Scene; UBnormal; IITB Corridor,HR-ShanghaiTech; CUHK Avenue; UCSD Ped2; HR-UBnormal; CHUK Avenue; XD-Violence; ShanghaiTech; Street Scene; UBnormal; IITB Corridor,RBDC; TBDC; AUC,Weakly-supervised Video Anomaly Detection,Computer Vision,N/A
Lane Detection,17,AssistTaxi; comma 2k19; K-Lane; ONCE-3DLanes; nuScenes; CULane; OpenLane-V2 val; VIL-100; CurveLanes; BDD100K,K-Lane; tvtLane; Caltech Lanes Washington; nuScenes; CULane; OpenLane-V2 val; OpenLane; CurveLanes; DET; LLAMAS,Accuracy (%); FPS; mAP; Params (M); IoU (%); Precision; F1; mF1; Average IOU; GFLOPs,3D Lane Detection,Computer Vision,**Lane Detection** is a computer vision task that involves identifying the boundaries of driving lanes in a video or image of a road scene. The goal is to accurately locate and track the lane markings...
Face Anti-Spoofing,17,HiFiMask; SiW; CelebA-Spoof-Enroll; CASIA-MFSD; CASIA-FASD; MSU-MFSD; HQ-WMCA; MLFP; CASIA-SURF; SiW-Enroll,N/A,N/A,N/A,N/A,N/A
Pedestrian Detection,17,KAIST multi-spectral Day/Night 2018; INRIA Person; MMPD-Dataset; PRW; TJU-DHD; Caltech Pedestrian Dataset; BGVP; CADP; RailEye3D Dataset; Virtual-Pedcross-4667,MMPD-Dataset; Caltech; CVC14; TJU-Ped-traffic; Caltech Pedestrian Dataset; CityPersons; LLVIP; DVTOD; TJU-Ped-campus, mAP; MR; Reasonable Miss Rate; RS (miss rate); AP50; Bare MR^-2; Reasonable MR^-2; HO (miss rate); mAP; Heavy MR^-2,Thermal Infrared Pedestrian Detection,Computer Vision,Pedestrian detection is the task of detecting pedestrians from a camera.    Further state-of-the-art results (e.g. on the KITTI dataset) can be found at [3D Object Detection](https://paperswithcode.co...
RGB Salient Object Detection,17,DUTS; SOD; HKU-IS; MD4K; VT5000; DAVIS-S; HRSOD; UHRSD; ECSSD; DUT-OMRON,SOD; UCF; HKU-IS; DAVIS-S; HRSOD; UHRSD; PASCAL-S; ECSSD; DUT-OMRON; ISTD,Weighted F-Measure; Balanced Error Rate; S-Measure; mean F-Measure; mBA; max F-measure; max F-Measure; mean E-Measure; MAE; F-Score,Video Salient Object Detection; Dichotomous Image Segmentation; Co-Salient Object Detection,Computer Vision,"RGB Salient object detection is a task-based on a visual attention mechanism, in which algorithms aim to explore objects or regions more attentive than the surrounding areas on the scene or RGB images..."
Salient Object Detection,17,DUTS; SOD; Lytro Illum; USIS10K; O3; P3; HKU-IS; MD4K; HRSOD; ECSSD,SOD; HKU-IS; ECSSD; DUT-OMRON; PASCAL-S; DUTS-TE,Smeasure; Sm; E-measure; {max}Fβ; relaxFbβ; MAE; Fwβ; max_F1; S-measure,RGB-T Salient Object Detection; Saliency Ranking,Computer Vision,N/A
Deblurring,17,Dialogue State Tracking Challenge; HIDE; DAVANet; QMUL-SurvFace; Rendered WB dataset; Beam-Splitter Deblurring (BSD); YorkTag; Motion Blurred and Defocused Dataset; Gun Detection Dataset; RSBlur,RealBlur-J (trained on GoPro); .; HIDE; RealBlur-R (trained on GoPro); Beam-Splitter Deblurring (BSD); Second dialogue state tracking challenge; RealBlur-J; MSU BASED; HIDE (trained on GOPRO); GoPro,LPIPS; SSIM (sRGB); PSNR (sRGB); Average PSNR; Params (M); Subjective; PSNR; Params; ERQAv2.0; VMAF,Blind Image Deblurring; Single-Image Blind Deblurring,Computer Vision,"**Deblurring** is a computer vision task that involves removing the blurring artifacts from images or videos to restore the original, sharp content. Blurring can be caused by various factors such as c..."
Lesion Segmentation,17,ISIC 2018 Task 3; Retinal-Lesions; SD-198; FGADR; ISIC 2017 Task 1; CoIR; ISIC 2018 Task 1; AutoPET; ISIC 2017 Task 2; HAM10000,N/A,N/A,N/A,N/A,N/A
Fact Verification,17,CREAK; DanFEVER; HoVer; Snopes; VitaminC; X-Fact; FaVIQ; CFEVER; Evidence-based Factual Error Correction; FACTIFY,FEVER; DanFEVER; KILT: FEVER,Recall@5; KILT-AC; F1; R-Prec; FEVER; Accuracy,N/A,Natural Language Processing,"Fact verification, also called ""fact checking"", is a process of verifying facts in natural text against a database of facts."
Beat Tracking,17,Groove; Strain gauge platforms: Time-lapse microscopy dataset of engineered cardiac microbundles; GuitarSet; Ballroom; JAAH; Beatles; SMC; FibroTUG platforms: Time-lapse microscopy dataset of engineered cardiac microbundles; SIMAC; TapCorrect,Groove; GuitarSet; Ballroom; JAAH; Beatles; SMC; SIMAC; TapCorrect; GTZAN; HJDB,F1,N/A,Audio,Determine the positions of all beats in a music recording.
Motor Imagery Decoding (left-hand vs right-hand),17,Mental Arithmetic Dataset from Shin et al 2017; AlexMI; Motor Imagey Dataset from Shin et al 2017; Motor Imagery dataset from Weibo et al 2014.; Munich Motor Imagery dataset; BMI/OpenBMI dataset for MI.; Motor Imagery dataset from Cho et al 2017.; Motor Imagery dataset from Zhou et al 2016.; Motor Imagery dataset from Ofner et al 2017; BNCI 2014-002 Motor Imagery dataset,N/A,N/A,N/A,N/A,N/A
Image Quality Assessment,16,PIQ23; Hephaestus; CSIQ; EyeQ; KADID-10k; KonIQ-10k; SPAQ; MSU FR VQA Database; CROSS; MSU NR VQA Database,KADID10K; KADID-10k; KonIQ-10k; SPAQ; MSU FR VQA Database; MSU NR VQA Database,PLCC; KLCC; SRCC,Full-Reference Image Quality Assessment; Image Quality Estimation; No-Reference Image Quality Assessment; Stereoscopic image quality assessment; Full reference image quality assessment,Computer Vision,N/A
Scene Recognition,16,AID; ADE20K; HOWS; Places205; Stairs Image Dataset | Parts of House | Indoor; SUN397; ScanNet; ADVANCE; HSD; SUN RGB-D,N/A,N/A,N/A,N/A,N/A
Facial Landmark Detection,16,AFLW; Helen; COFW; CatFLW; LFPW; AFW; COCO-WholeBody; Thermal Face Database; AFLW2000-3D; Toronto NeuroFace Dataset,AFLW-Full; COFW; CatFLW; COCO-WholeBody; AFLW-Front; AFLW2000-3D; 300W; 300W (Full); 300-VW (C); WFLW,Mean NME; keypoint AP; NME (inter-ocular); AUC@10 (inter-ocular); GTE; NME (inter-pupil); Mean NME ; NME; FR@10 (inter-ocular); AUC0.08 private,3D Facial Landmark Localization; Unsupervised Facial Landmark Detection; Speech to Facial Landmark,Computer Vision,"**Facial Landmark Detection** is a computer vision task that involves detecting and localizing specific points or landmarks on a face, such as the eyes, nose, mouth, and chin. The goal is to accuratel..."
Grammatical Error Correction,16,GitHub Typo Corpus; GMEG-wiki; Kor-Lang8; GMEG-yahoo; UA-GEC; CoNLL-2014 Shared Task: Grammatical Error Correction; Kor-Native; NaSGEC; FCGEC; The Write & Improve Corpus 2024,Falko-MERLIN; CoNLL-2014 Shared Task; Restricted; UA-GEC; CoNLL-2014 Shared Task (10 annotations); Unrestricted; EstGEC-L2; JFLEG; _Restricted_; WI-LOCNESS,GLEU; F0.5; Precision; exact match; Recall,Grammatical Error Detection,Natural Language Processing,"Grammatical Error Correction (GEC) is the task of correcting different kinds of errors in text such as spelling, punctuation, grammatical, and word choice errors.     GEC is typically formulated as a ..."
Video Summarization,16,VideoXum; MultiSum; SumMe; TvSum; RAD; Multi-Ego; Shot2Story20K; OSTD; MVS1K; Mr. HiSum,SumMe; TvSum; Shot2Story20K; Mr. HiSum; videoxum; Query-Focused Video Summarization Dataset,1 shot Micro-F1; MAP (50%); F1-score (Canonical); METEOR; BLEU-4; F1-score (Augmented); Spearman's Rho; F1 (avg); ROUGE; CIDEr,Unsupervised Video Summarization; Supervised Video Summarization,Computer Vision,**Video Summarization** aims to generate a short synopsis that summarizes the video content by selecting its most informative and important parts. The produced summary is usually composed of a set of ...
Active Learning,16,Photoswitch; MNIST-8M; Groove; Illness-dataset; Arxiv GR-QC; Goldfinch; CIFAR-10; SYNTHIA-AL; HJDataset; COMP6,"CIFAR10 (10,000)",Accuracy,Active Object Detection,Methodology; Computer Vision; Natural Language Processing,"**Active Learning** is a paradigm in supervised machine learning which uses fewer training examples to achieve better optimization by iteratively training a predictor, and using the predictor in each ..."
Joint Entity and Relation Extraction,16,2012 i2b2 Temporal Relations; A Dataset for Relation Extraction of Natural-Products; CoNLL04; SemEval-2022 Task-12; New York Times Annotated Corpus; GDA; CDR; DocRED; DocRED-IE; SciERC,N/A,N/A,N/A,N/A,N/A
Event Extraction,16,WikiEvents; Catalan TimeBank 1.0; BKEE; IndiaPoliceEvents; Phee; EventNarrative; French Timebank; LEMONADE; Personal Events in Dialogue Corpus; Title2Event,GENIA 2013; Cancer Genetics 2013 (CG); ACE2005; Multi-Level Event Extraction (MLEE); Epigenetics and Post-translational Modifications 2011 (EPI); Infectious Diseases 2011 (ID); ^(#$!@#$)(()))******; Pathway Curation 2013 (PC); GENIA,F1; Argument Cl; Argument Id; Trigger Id; Trigger Cl; 0L,Zero-shot Event Extraction; NER; Event Causality Identification,Natural Language Processing,Determine the extent of the events in a text.    Other names: Event Tagging; Event Identification
3D Action Recognition,16,NTU RGB+D; MultiviewC; BABEL; UAV-Human; CAP; [[Talk!!Person]]How do I talk to a person at Expedia?; FR-FS; FLAG3D; Florence3D; 3DYoga90,Assembly101; 100 sleep nights of 8 caregivers; NTU RGB+D,Verbs Top-1; Cross Subject Accuracy; Object Top-1; 10%; Cross View Accuracy; Actions Top-1,Image Manipulation Detection; Skeleton Based Action Recognition; Model Editing; Zero Shot Skeletal Action Recognition; motion retargeting,Computer Vision; Natural Language Processing,Image: [Rahmani et al](https://www.cv-foundation.org/openaccess/content_cvpr_2016/papers/Rahmani_3D_Action_Recognition_CVPR_2016_paper.pdf)
Hand Gesture Recognition,16,DriverMHG; BdSLW60; FDMSE-ISL; IPN Hand; CamGes; SHREC; MLGESTURE DATASET; EgoGesture; LSA16; VIVA,N/A,N/A,N/A,N/A,N/A
Trajectory Forecasting,16,TrajNet; EgoPAT3D-DT; Euro-PVI; rounD Dataset; NBA SportVU; highD Dataset; UCY; uniD Dataset; JAAD; FPL,N/A,N/A,N/A,N/A,N/A
Video Super-Resolution,16,SEPE 8K; TbD-3D; QST; Vid4; TbD; Inter4K; RealMCVSR; MSU Video Upscalers: Quality Enhancement; MSU Super-Resolution for Video Compression; MSU Video Super Resolution Benchmark: Detail Restoration,Vimeo-90K; Xiph HD - 4x upscaling; Vid4 - 4x upscaling - BD degradation; SPMCS - 4x upscaling; TbD-3D; UDM10 - 4x upscaling; TbD; MSU Video Upscalers: Quality Enhancement; MSU Super-Resolution for Video Compression; MSU Video Super Resolution Benchmark: Detail Restoration,BSQ-rate over PSNR; LPIPS; Average PSNR; Subjective score; ERQAv1.0; QRCRv1.0; FPS; BSQ-rate over MS-SSIM; PSNR; 1 - LPIPS,Key-Frame-based Video Super-Resolution (K = 15),Computer Vision,"**Video Super-Resolution** is a computer vision task that aims to increase the resolution of a video sequence, typically from lower to higher resolutions. The goal is to generate high-resolution video..."
Single-View 3D Reconstruction,15,SceneNet; Common Objects in 3D; SynthEVox3D-Tiny; Parcel2D Real; ShapeNet; CUB-200-2011; Drunkard's Dataset; GSO; TransProteus; Parcel3D,Market-HQ; Common Objects in 3D; SynthEVox3D-Tiny; ShapeNet; ATR; CUB-200-2011; GSO; TransProteus; ShapeNetCore,Avg. F1; FID; R2; IoU; Chamfer Distance; A-mIoU; F-Score; 3DIoU,3D Semantic Scene Completion from a single RGB image,Computer Vision,N/A
Video Frame Interpolation,15,DAVIS; SEPE 8K; ATD-12K; Vid4; SlowFlow; Middlebury; Inter4K; GoPro; VFITex; MSU Video Frame Interpolation,Nvidia Dynamic Scene; Xiph-4k; Xiph-2K; Xiph 4k; UCF101; DAVIS; GoPro; Xiph-4K (Crop); SNU-FILM (easy); SNU-FILM (hard),LPIPS; PSNR (sRGB); Subjective score; FPS; tOF; PSNR; runtime (s); VMAF; Speed (ms/f); MS-SSIM,eXtreme-Video-Frame-Interpolation; 3D Video Frame Interpolation; Unsupervised Video Frame Interpolation,Computer Vision,The goal of **Video Frame Interpolation** is to synthesize several frames in the middle of two adjacent frames of the original video. Video Frame Interpolation can be applied to generate slow motion v...
Human Activity Recognition,15,MuscleMap136; Radar Dataset (DIAT-μRadHAR: Radar micro-Doppler Signature dataset for Human Suspicious Activity Recognition); RHM; PAMAP2; WEAR; OAD dataset; Wallhack1.8k; HAR; FLAG3D; MOSAD,Radar Dataset (DIAT-μRadHAR: Radar micro-Doppler Signature dataset for Human Suspicious Activity Recognition); RHM; MM-Fit; OAD dataset; PAMAP2; UCF 101; HAR; HMDB51,NMI; 1:1 Accuracy; ARI; Macro F1; Accuracy; F1 - macro; F1 Macro; Macro-F1; Accuracy (Top-1),Sports Activity Recognition,Time Series,Classify various human activities
Gesture Recognition,15,11k Hands; Aff-Wild; MSRC-12; IPN Hand; SHREC; DVS128 Gesture; SALSA; MLGESTURE DATASET; TCG; Florentine,MSRC-12; SHREC 2017 track on 3D Hand Gesture Recognition; DVS128 Gesture; CapgMyo DB-c; ChaLearn 2013; Chalearn 2014; GesturePod; Ninapro DB-1 8 gestures; Montalbano; ChaLearn 2016,Accuracy (%); Jaccard (Mean); Real World Accuracy; 14 gestures accuracy; Error rate; Precision; Accuracy; Recall,Hand Gesture Recognition; RF-based Gesture Recognition; Hand-Gesture Recognition,Computer Vision,"**Gesture Recognition** is an active field of research with applications such as automatic recognition of sign language, interaction of humans and robots or for new ways of controlling video games.   ..."
2D Pose Estimation,15,Vinegar Fly; ConSLAM; MP-100; Human3.6M; SLAM2REF; Desert Locust; MacaquePose; HARPER; iRodent; Animal Kingdom,Vinegar Fly; MP-100; iRodent; Human3.6M; Desert Locust; MacaquePose; HARPER; Animal Kingdom; 300W,PCK@0.05; Mean PCK@0.2 - 1shot; Average mAP; EPE; PCK; Mean PCK@0.2; Mean PCK@0.2 - 5shot; AP,Category-Agnostic Pose Estimation; Overlapping Pose Estimation,Computer Vision,detective pose
Computed Tomography (CT),15,PadChest; CPCXR; X-ray and Visible Spectra Circular Motion Images Dataset; Large COVID-19 CT scan slice dataset; LiTS17; LUNA; COVID-19-CT-CXR; BIMCV COVID-19; CC-19; LUNA16,N/A,N/A,Stroke Classification,Methodology,"The term “computed tomography”, or CT, refers to a computerized x-ray imaging procedure in which a narrow beam of x-rays is aimed at a patient and quickly rotated around the body, producing signals th..."
Saliency Detection,15,DUTS; CAT; Lytro Illum; ReDWeb-S; USIS10K; AViMoS; HKU-IS; iSUN; CAT2000; COME15K,HKU-IS; CAT2000; DUTS-test; DUT-OMRON; ECSSD; PASCAL-S; PASCAL Context,Sm; {max}Fβ; relaxFbβ; NSS; MAE; max_F1; Fwβ; AUC,Video Saliency Detection; Saliency Prediction; Unsupervised Saliency Detection; Co-Salient Object Detection,Computer Vision,"**Saliency Detection** is a preprocessing step in computer vision which aims at finding salient objects in an image.   <span class=""description-source"">Source: [An Unsupervised Game-Theoretic Approach..."
Video Inpainting,15,Apolloscape Inpainting; FVI; DAVIS; DREAMING Inpainting Dataset; QST; How2Sign; DEVIL; VideoRemoval4K; WRV; VPData,DAVIS; HQVI (2K); How2Sign; HQVI (240p); YouTube-VOS 2018; HQVI (480p); YouTube-VOS,LPIPS; Ewarp; PSNR; PSNR (square); LPIPS (square); VFID; SSIM (object); PNSR (object); SSIM (square); LPIPS (object),N/A,Computer Vision,"The goal of **Video Inpainting** is to fill in missing regions of a given video sequence with contents that are both spatially and temporally coherent. Video Inpainting, also known as video completion..."
Sarcasm Detection,15,ArSarcasm; WITS; iSarcasmEval; BIG-bench; Reddit; SPIRS; SARC; MUStARD++; ArSarcasm-v2; Cards Against Humanity,WITS; SARC (all-bal); MUStARD++; FigLang 2020 Twitter Dataset; BIG-bench (SNARKS); SARC (pol-bal); FigLang 2020 Reddit Dataset; iSarcasm; SARC (pol-unbal),Precision; F1; R1; Accuracy; F1-Score; Avg F1; Recall,N/A,Natural Language Processing,"The goal of **Sarcasm Detection** is to determine whether a sentence is sarcastic or non-sarcastic. Sarcasm is a type of phenomenon with specific perlocutionary effects on the hearer, such as to break..."
License Plate Recognition,15,SSIG-SegPlate; CCPD; UFPR-ALPR; EnglishLP; UCSD-Stills; Vehicle-Rear; CD-HARD; UFPR-VCR Dataset; ChineseLP; Caltech Cars,N/A,N/A,N/A,N/A,N/A
Tumor Segmentation,15,The ULS23 Challenge Test Set; The ULS23 Challenge Public Training Dataset; CARE; AutoPET; LiTS17; CUTS; Extended Task10_Colon Medical Decathlon; DigestPath; BraTS 2014; BraTS 2015,N/A,N/A,N/A,N/A,N/A
3D Human Reconstruction,15,MoVi; CustomHumans; X-Humans; HEADSET; BEHAVE; MMBody; Synthetic Human Model Dataset; 4D-DRESS; Dynamic FAUST; HBW,N/A,N/A,N/A,N/A,N/A
3D Classification,15,Teeth3DS+; 3D-Point Cloud dataset of various geometrical terrains; Corn Seeds Dataset; ModelNet40-C; Calcium imaging of glomeruli in the olfactory bulb of the mouse in response to thirty-five monomolecular odors; InLUT3D; FER2013 Blendshapes; ADHD-200; U-10: United-10 COVID19 CT Dataset; RAD-ChestCT Dataset,U-10: United-10 COVID19 CT Dataset,AUC,3D Object Classification; MRI classification,Medical; Computer Vision,N/A
Semi-Supervised Image Classification,14,Salinas; STL-10; BIOSCAN-5M; CIFAR-100; BarkNet 1.0; CIFAR-10; Semi-iNat; ImageNet; SVHN; Caltech-101,"Salinas; CIFAR-100, 5000Labels; Caltech-101, 202 Labels; ImageNet - 10% labeled data; SVHN (250 Labels, ImageNet-100 Unlabeled); cifar-100, 10000 Labels; SVHN, 1000 labels; SVHN, 2000 Labels; EuroSAT, 20 Labels; CIFAR-100, 200 Labels",Accuracy (%); Percentage error; Top 5 Accuracy; Percentage correct; Overall Accuracy; ImageNet Top-1 Accuracy; Top 1 Accuracy; Accuracy; Number of params; Accuracy (Test),Semi-Supervised Image Classification (Cold Start); Open-World Semi-Supervised Learning,Computer Vision,Semi-supervised image classification leverages unlabelled data as well as labelled data to increase classification performance.    You may want to read some blog posts to get an overview before readin...
Weakly Supervised Object Detection,14,IconArt; Charades; Clipart1k; LeukemiaAttri; MSCOCO; PASCAL VOC 2007; PASCAL VOC 2012 test; HICO-DET; ImageNet; Watercolor2k,N/A,N/A,N/A,N/A,N/A
Open Information Extraction,14,hasPart KB; LSOIE; OPIEC; OIE2016; Penn Treebank; CaRB; New York Times Annotated Corpus; WiRe57; TupleInf Open IE Dataset; SemTabNet,LSOIE; CaRB; Penn Treebank; Web; CaRB OIE benchmark (Greek Use-case); DocOIE-healthcare; OIE2016; WiRe57; OpenIE; BenchIE,Precision; F1; EN-F1; EN-AUC; Recall; AUC,Event Extraction,Natural Language Processing,"In natural language processing, open information extraction is the task of generating a structured, machine-readable representation of the information in text, usually in the form of triples or n-ary ..."
Text-to-Video Generation,14,Something-Something V2; OpenS2V-5M; Kinetics; EvalCrafter Text-to-Video (ECTV) Dataset; ChronoMagic-Pro; WebVid; CelebV-Text; Vript; ConsisID-preview-Data; OpenS2V-Eval,Something-Something V2; Kinetics; EvalCrafter Text-to-Video (ECTV) Dataset; WebVid; MSR-VTT; UCF-101,CLIPSIM; FVD16; Total Score; CLIP-FID; Visual Quality; Motion Quality; FID; Text-to-Video Alignment; Temporal Consistency; FVD,Text-to-Video Editing; Subject-driven Video Generation,Computer Vision; Natural Language Processing,"Ma grand-mère m’a raconté que quand elle était étudiante, elle avait un petit-ami. À l’âge de 18 ans, il a dû partir pour le service militaire, elle ne l’a pas attendu et elle a épousé quelqu’un d’aut..."
Emotion Recognition in Conversation,14,SEMAINE; DailyDialog; KD-EmoR; MaSaC_ERC; CMU-MOSI; MELD; Emotional Dialogue Acts; EmoWOZ; EmotionLines; CPED,N/A,N/A,N/A,N/A,N/A
Boundary Detection,14,TriBERT; Oxford Road Boundaries; SoccerNet-v2; NYUv2; Kinetics; Contour Drawing Dataset; Kinetics 400; CaFFe; RoFT; MDBD,TriBERT (in-domain); RoFT; CoAuthor; RoFT-chatgpt; Kinetics-400; UruDendro; PASCAL Context; NYU-Depth V2,Accuracy (%); FScore; Pairwise F1; Cohen’s Kappa score; odsF; F1-score; Average Recall; Average Precision; Precision; MSE,Junction Detection,Computer Vision,"**Boundary Detection** is a vital part of extracting information encoded in images, allowing for the computation of quantities of interest including density, velocity, pressure, etc.      <span class=..."
Nested Named Entity Recognition,14,RuTermEval (Track 2); DaN+; GUM; RuTermEval (Track 3); ACE 2004; RuTermEval (Track 1); NNE; LegalNERo; ACE 2005; AMALGUM,N/A,N/A,N/A,N/A,N/A
Semantic Role Labeling,14,CoNLL-2009; QA-SRL Bank 2.0; RuSRL; PropBank-PT; OntoNotes 5.0; CoNLL-2012; ExHVV; NomBank; X-SRL; FrameNet,CoNLL-2009; CoNLL12; CoNLL 2005; CoNLL05 WSJ; CoNLL 2012; OntoNotes; CoNLL05 Brown,F1; F1 (Prd.); F1 (Arg.); Avg. F1,Semantic Role Labeling (predicted predicates); Textual Analogy Parsing; Predicate Detection,Natural Language Processing,"Semantic role labeling aims to model the predicate-argument structure of a sentence  and is often described as answering ""Who did what to whom"". BIO notation is typically  used for semantic role label..."
Network Intrusion Detection,14,UNSW-NB15; SIDD-Image; UQ NIDS Datasets (FlowMeter Format); CICIDS2017; IoT Benign and Attack Traces; WEB-IDS23 Dataset; IoT Network Intrusion Dataset; IoT ENVIRONMENT DATASET; UQ NetFlow NIDS v1; Kitsune Network Attack Dataset,N/A,N/A,N/A,N/A,N/A
motion prediction,14,GTA-IM Dataset; Freiburg Street Crossing; Occ-Traj120; Argoverse 2 Motion Forecasting; UCY; VIENA2; NTU RGB+D 2D; Waymo Open Motion Dataset; CITR & DUT; Lyft Level 5 Prediction,N/A,N/A,OPD: Single-view 3D Openable Part Detection,Computer Vision,N/A
Handwritten Text Recognition,14,IAM; READ 2016; LAM(line-level); Digital Peter; IAM(line-level); Bentham; Saint Gall; SIMARA; Belfort; Burmese Handwritten Digit Dataset (BHDD),READ 2016; IAM; LAM(line-level); IAM(line-level); Digital Peter; IAM-D; Bentham; Saint Gall; SIMARA; Belfort,Test CER; CER; WER (%); WER; CER (%); Test WER,Handwritten Document Recognition; Unsupervised Text Recognition,Adversarial; Computer Vision,Handwritten Text Recognition (HTR) is the task of automatically identifying and transcribing handwritten text from images or scanned documents into machine-readable text. The goal is to develop a syst...
Pose Tracking,14,"VR Mocap Dataset for Pose/Orientation Prediction; ConSLAM; VR-Folding; SLAM2REF; 10,000 People - Human Pose Recognition Data; COPE-119; YCBInEOAT Dataset; VRMocap: VR Mocap Dataset for Pose Reconstruction; Occluded-PoseTrack-ReID; InfiniteRep",Multi-Person PoseTrack; PoseTrack2017; PoseTrack2018,mAP; MOTA; IDF1; MOTP; IDs,3D Human Pose Tracking,Computer Vision,**Pose Tracking** is the task of estimating multi-person human poses in videos and assigning unique instance IDs for each keypoint across frames. Accurate estimation of human keypoint-trajectories is ...
Human Detection,14,PTB-TIR; PP-HumanSeg14K; PANDA; C2A: Human Detection in Disaster Scenarios; SoccerNet-GSR; JRDB; MIAP; PeopleSansPeople; TikTok Dataset; NREC Agricultural Person-Detection,N/A,N/A,N/A,Computer Vision,"DEFINICIÓN   EL Granada SOUND es un festival de música india de ámbito nacional e internacional que se celebra en Granada, España. Desde su primera edición en 2012, el evento ha ido creciendo en popul..."
Event Detection,14,Brazilian Protest; MUSIED; BKEE; LEVEN; MAVEN-ERE; OpenTTGames; FewEvent; MAVEN; LEMONADE; ROAD,N/A,N/A,N/A,N/A,N/A
Indoor Localization,14,ConSLAM; LuViRA; DATOR-synth; UJI Probes; SLAM2REF; Structured3D; NILoc; Fusion-DHL; Indoor-6; dichasus-cf0x,"Structured3D (perspective, furnished); Structured3D (perspective, emtpy); Structured3D (perspective, empty)",N/A,"Indoor Localization (6-DoF Pose); Indoor Localization (3-DoF Pose: X, Y, Yaw)",Computer Vision,Indoor localization is a fundamental problem in indoor location-based applications.
Instruction Following,14,InstructOpenWiki; Tamil Alpaca Orca; Sequential Instructions; GSCAN; IFEval; MIMIC-IT; Tamil Alpaca; UGIF; SurgeGlobal/Evol-Instruct; CIDAR,IFEval,Prompt-level loose-accuracy; Prompt-level strict-accuracy; Inst-level loose-accuracy; Inst-level strict-accuracy,visual instruction following,Computer Vision; Natural Language Processing,Instruction following is the basic task of the model. This task is dedicated to evaluating the ability of the large model to follow human instructions. It is hoped that the model can generate controll...
Keypoint Detection,13,KeypointNet; AwA Pose; GRIT; OCHuman; PASCAL3D+; ViCoS Towel Dataset; ApolloCar3D; RadioGalaxyNET; COCO (Common Objects in Context); MPII,N/A,N/A,N/A,N/A,N/A
Video Object Tracking,13,VOTChallenge; SoccerNet-v2; CATER; NT-VOT211; GOT-10k; SOTVerse; BL30K; RF100; VISEM-Tracking; TREK-150,N/A,N/A,N/A,N/A,N/A
Dialogue State Tracking,13,Dialogue State Tracking Challenge; KLUE; diaforge-utc-r-0725; RiSAWOZ; IndirectRequests; SGD; Wizard-of-Oz; Taskmaster-1; CrossWOZ; SIMMC2.0,N/A,N/A,N/A,N/A,N/A
Small Object Detection,13,SOD4SB; DOTA; EVD4UAV; iSAID; Bee4Exp Honeybee Detection; RF100; Aircraft Context Dataset; Blood Cell Detection Dataset; SODA-D; SODA-A,Bee4Exp Honeybee Detection; SOD4SB Public Test; SOD4SB Private Test; SODA-D,AP50; mAP@0.5:0.95; Average F1,Rice Grain Disease Detection,Computer Vision,**Small Object Detection** is a computer vision task that involves detecting and localizing small objects in images or videos. This task is challenging due to the small size and low resolution of the ...
Video Object Detection,13,"Underwater Trash Detection; ImageNet VID; THGP; 5,011 Images – Human Frontal face Data (Male); EPIC-KITCHENS-55; SYNTHIA-AL; Waymo Open Dataset; OAK; GEN1 Detection; VISEM-Tracking",N/A,N/A,N/A,N/A,N/A
Semi-Supervised Video Object Segmentation,13,PUMaVOS; Long Video Dataset; DAVIS; MOSE; VOTChallenge; Referring Expressions for DAVIS 2016 & 2017; Long Video Dataset (3X); BL30K; VOT2020; YouTube-VOS 2018,DAVIS 2017 (val); Long Video Dataset; YouTube-VOS 2019; MOSE; BURST-test; DAVIS 2017 (test-dev); Long Video Dataset (3X); DAVIS-2016; DAVIS-2017; BURST-val,FPS; Jaccard (Unseen); D17 test (J); Jaccard (Seen); D17 val (J); F-measure (Mean); D17 val (G); J score (unseen); J; F-measure (Recall),One-shot visual object segmentation,Computer Vision,The semi-supervised scenario assumes the user inputs a full mask of the object(s) of interest in the first frame of a video sequence. Methods have to produce the segmentation mask for that object(s) i...
Semi-Supervised Semantic Segmentation,13,ADE20K; 2017 Robotic Instrument Segmentation Challenge; ScribbleKITTI; 2D-3D-S; nuScenes; WoodScape; ImageNet-S; Dronescapes; Kvasir-Instrument; SUIM,N/A,N/A,N/A,N/A,N/A
Hyperspectral Image Classification,13,Salinas; Kennedy Space Center; Tecnalia WEEE HYPERSPECTRAL DATASET; Houston; Hyper Drive; Pavia University; Botswana; HSI-Drive v2.0; LIB-HSI; Hyperspectral City,N/A,N/A,N/A,N/A,N/A
Community Detection,13,Citeseer; Yeast; HLA-Chat; Cora; Orkut; A collection of LFR benchmark graphs; SNAP; Twitter-HyDrug; Email-EU; Placenta,Citeseer; Facebook Politicians; Cora; Facebook Government; Facebook Artists; Facebook TV Show; Facebook Celebrities; Facebook Athletes; Twitter-HyDrug; Facebook Media,NMI; Modularity; F1-score; Accuracy-NE; ACC; F1-Score; Jaccard,Local Community Detection; Network Community Partition; Online Community Detection,Graphs,"**Community Detection** is one of the fundamental problems in network analysis, where the goal is to find groups of nodes that are, in some sense, more similar to each other than to the other nodes.  ..."
Downbeat Tracking,13,Groove; GuitarSet; Ballroom; JAAH; Beatles; TapCorrect; GTZAN; HJDB; Hainsworth; Candombe,Groove; GuitarSet; Ballroom; JAAH; Beatles; TapCorrect; GTZAN; HJDB; Hainsworth; Candombe,F1,N/A,Audio,Determine the positions of all downbeats in a music recording.
Vision and Language Navigation,13,ArraMon; Fine-Grained R2R; Touchdown Dataset; map2seq; MINOS; robo-vln; Talk the Walk; WebLINX; Talk2Nav; RUN,Touchdown Dataset; map2seq; robo-vln; RxR; VLN Challenge,error; success; length; ndtw; SPL (Sucess Weighted by Path Length); Task Completion (TC); oracle success; spl,N/A,Robots,N/A
Multiple Instance Learning,13,Colorectal Adenoma; CamNuvem Dataset; TCGA; Musk v1; Wiki-zh; DAiSEE; CAMELYON16; Elephant; Musk v2; SPOT,Musk v1; Elephant; CAMELYON16; Musk v2; TCGA,Expected Calibration Error; FROC; Patch AUC; ACC; AUC,N/A,Methodology,"**Multiple Instance Learning** is a type of weakly supervised learning algorithm where training data is arranged in bags, where each bag contains a set of instances $X=\\{x_1,x_2, \ldots,x_M\\}$, and ..."
Image Registration,13,L1BSR; VBR; ALTO; BRIGHT; Learn2Reg; Fetoscopy Placenta Data; FIRE; LLVIP; FetReg: Largescale Multi-centre Fetoscopy Placenta Dataset; DIR-LAB COPDgene, Osteoarthritis Initiative; FIRE; Unpaired-lung-CT; Unpaired-abdomen-CT; DIR-LAB COPDgene,Dice; DSC; ASD; mAUC; landmarks,distortion correction; Unsupervised Image Registration,Computer Vision,"Image registration is the process of transforming different sets of data into one coordinate system. Data may be multiple photographs, data from different sensors, times, depths, or viewpoints. It is ..."
Image Reconstruction,12,OADAT; Ultra-High Resolution Image Reconstruction Benchmark; WiFiCam; General-100; Spike-X4K; SEN12MS-CR; ImageNet; CBCT Walnut; SEN12MS-CR-TS; 2DeteCT,Ultra-High Resolution Image Reconstruction Benchmark; Spike-X4K; Audio Set; Edge-to-Clothes; Edge-to-Handbags; ImageNet; Edge-to-Shoes; ImageNet 256x256,LPIPS; Average PSNR; PSNR; FID; HP; MMD; SSIM; rFID,MRI Reconstruction; Film Removal; CT Reconstruction; Blind Super-Resolution; WiFi CSI-based Image Reconstruction,Miscellaneous; Medical; Computer Vision,N/A
Image Compression,12,PINet; PCam; Oxford-IIIT Pets; OSN-transmission_mini_CelebA; STL-10; ImageNet-32; Oxford-IIIT Pet Dataset; UPIQ; CIFAR-10; BSDS500,Food-101; Cars-196; PCam; STL-10; Oxford-IIIT Pet Dataset; CIFAR-10; ImageNet32; Caltech101; BSDS500; kodak,Average PSNR; 10%; Bit rate; BD-Rate over VTM-17.0; bpsp; SSIM,Feature Compression; Jpeg Compression Artifact Reduction; Lossy-Compression Artifact Reduction; Color Image Compression Artifact Reduction,Computer Vision,"**Image Compression** is an application of data compression for digital images to lower their storage and/or transmission requirements.      <span class=""description-source"">Source: [Variable Rate Dee..."
Point Cloud Registration,12,FAUST-partial; 3DCSR dataset; ScanNet++; 3DMatch; DeformingThings4D; 4DMatch; UrbanLoco; FPv1; KITTI; CODD,3DLoMatch (10-30% overlap); FP-T-M; FP-R-E; ETH (trained on 3DMatch); nuScenes (Distant PCR); FP-R-M; FP-O-M; 3DMatch (at least 30% overlapped - FCGF setting); FP-R-H; FP-T-E,"Recall (30cm, 5 degrees); RTE; RE (all); RR@(1,0.1); Recall (3cm, 10 degrees); Recall ( correspondence RMSE below 0.2); Feature Matching Recall; RRE (degrees); RTE (cm); RR @ Loose Criterion (5°&2m), on LoKITTI",Image to Point Cloud Registration,Computer Vision,"**Point Cloud Registration** is a fundamental problem in 3D computer vision and photogrammetry. Given several sets of points in different coordinate systems, the aim of registration is to find the tra..."
Real-Time Semantic Segmentation,12,Kvasir; NYUv2; Medico automatic polyp segmentation challenge (dataset); CamVid; FLAME; COCO-Stuff; Kvasir-SEG; Kvasir-Instrument; Cityscapes; HelixNet,N/A,N/A,N/A,N/A,N/A
Fine-Grained Image Recognition,12,"WikiChurches; WebFG-496; Ultra Fine-Grained Leaves (Cotton, SoyAgeing, SoyGene, SoyGlobal, SoyLocal); fruit-SALAD; Crowd Activity Dataset; DAPlankton; Goldfinch; OmniBenchmark; CUB-200-2011; OVEN",N/A,N/A,N/A,N/A,N/A
Object Detection In Aerial Images,12,HRSC2016; DOTA; AID; VME & CDSI; iSAID; C2A: Human Detection in Disaster Scenarios; DOTA 2.0; SODA-A; FMARS; LandCover.ai,N/A,N/A,N/A,N/A,N/A
Activity Detection,12,AVA-Speech; DAHLIA; MEVA; Home Action Genome; AVA; TSU; IITB Corridor; InfiniteRep; ROAD; Toyota Smarthome Dataset,AVA-Speech,ROC-AUC,N/A,Computer Vision,Detecting activities in extended videos.
Fraud Detection,12,Yelp-Fraud; BAF; CIDII Dataset; Elliptic Dataset; Amazon-Fraud; IBM Transactions for Anti Money Laundering; Vehicle Claims; IEEE-CIS Fraud Detection; Kaggle-Credit Card Fraud Dataset; FDCompCN,Yelp-Fraud; BAF – Variant I; Elliptic Dataset; BAF – Base; BAF – Variant V; BAF – Variant III; Amazon-Fraud; BAF – Variant II; Kaggle-Credit Card Fraud Dataset; BAF – Variant IV,G-mean; Recall @ 5% FPR; Average Precision; AUPRC; AUC-ROC; Accuracy; Recall @ 1% FPR; F1 Macro; Averaged Precision; AUC,N/A,Miscellaneous,"**Fraud Detection** is a vital topic that applies to many industries including the financial sectors, banking, government agencies, insurance, and law enforcement, and more. Fraud endeavors have detec..."
Video Segmentation,12,PP-HumanSeg14K; LSDBench; Dynamic Replica; MM-OR; ConferenceVideoSegmentationDataset; BDD100K; EgoProceL; Infinity Spills Basic Dataset; MOMA-LRG; SegTrack-v2,SegTrack v2,Accuracy,Camera shot boundary detection; Open-World Video Segmentation; Open-Vocabulary Video Segmentation,Computer Vision,N/A
Surface Normals Estimation,12,NYUv2; Taskonomy; IBims-1; GRIT; Stanford-ORB; 3D Ken Burns Dataset; Pano3D; ScanNet; SynFoot; TransProteus,PCPNet; IBims-1; Taskonomy; Stanford-ORB; NYU-Depth V2 Surface Normals; ScanNetV2; PASCAL Context; NYU Depth v2,% < 22.5; Cosine Distance; % < 30; RMSE; RMSE ; L1 error; Mean Angle Error; % < 11.25; Mean,N/A,Computer Vision,Surface normal estimation deals with the task of predicting the surface orientation of the objects present inside a scene. Refer to [Designing Deep Networks for Surface Normal Estimation (Wang et al.)...
Conversational Response Selection,12,Advising Corpus; Reddit Corpus; E-commerce; RRS; BBAI Dataset; UDC; Reddit; Douban Conversation Corpus; Ubuntu IRC; Douban,"Advising Corpus; E-commerce; RRS; Ubuntu Dialogue (v1, Ranking); Douban Conversation Corpus; personachat; PolyAI Reddit; PolyAI AmazonQA; Ubuntu IRC; Douban",NDCG@3; R@1; R10@5; R2@1; R@50; 1-of-100 Accuracy; P@1; NDCG@5; R@10; Accuracy,N/A,Natural Language Processing,Conversational response selection refers to the task of identifying the most relevant response to a given input sentence from a collection of sentences.
Motion Forecasting,12,Argoverse 2 Lidar; Argoverse 2 Motion Forecasting; Argoverse; Argoverse 2 Sensor; Waymo Open Motion Dataset; FPL; Lyft Level 5 Prediction; FollowMe Vehicle Behaviour Prediction Dataset; Autonomous-driving Streaming Perception Benchmarrk; Argoverse 2 Map Change,Argoverse CVPR 2020,minFDE (K=1); brier-minFDE (K=6); minFDE (K=6); minADE (K=1); MR (K=1); MR (K=6); DAC (K=6); minADE (K=6),Multi-Person Pose forecasting; Multiple Object Forecasting,Computer Vision,Motion forecasting is the task of predicting the location of a tracked object in the future
License Plate Detection,12,SSIG-SegPlate; Common Voice; UFPR-ALPR; Vehicle-Rear; PKU (License Plate Detection); CD-HARD; Nomeroff Russian License Plates; MSDA; ChineseLP; Caltech Cars,Common Voice Estonian,0S,N/A,Computer Vision,License Plate Detection is an image-processing technology used to identify vehicles by their license plates. This technology is used in various security and traffic applications.
Video Quality Assessment,12,Raw_-Subjective-Scores-120-videos; LIVE Livestream; YouTube-UGC; LIVE-FB LSVQ; MSU FR VQA Database; MSU NR VQA Database; LIVE-VQC; MSU SR-QA Dataset; Video Call MOS Set; KoNViD-1k,LIVE Livestream; YouTube-UGC; MSU FR VQA Database; LIVE-FB LSVQ; MSU NR VQA Database; LIVE-VQC; MSU SR-QA Dataset; KoNViD-1k; LIVE-ETRI; LIVE-YT-HFR,Type; SRCC; PLCC; KLCC; SROCC,N/A,Time Series; Computer Vision,"Video Quality Assessment is a computer vision task aiming to mimic video-based human subjective perception. The goal is to produce a mos score, where higher score indicates better perceptual quality. ..."
Time Series Prediction,12,Extreme Events > Natural Disasters > Hurricane; STREETS; NBA player performance prediction dataset; Hotel; TSFM-ScalingLaws-Dataset; Box-Jenkins; EEG Eye State; Data Collected with Package Delivery Quadcopter Drone; Air Quality Index; EarthNet2021,Data Collected with Package Delivery Quadcopter Drone; Sunspot,RMSE; Average mean absolute error,N/A,Time Series,"The goal of **Time Series Prediction** is to infer the future values of a time series from the past.      <span class=""description-source"">Source: [Orthogonal Echo State Networks and stochastic evalua..."
Fact Checking,12,Politi Hop; VitaminC; STVD-FC; BEIR; PANACEA; Spiced; AVeriTeC; Stanceosaurus; Mocheg; CoVERT,CLIMATE-FEVER (BEIR); CDCD; AVeriTeC; ^(#$!@#$)(()))******; SciFact (BEIR); LIAR2; FEVER (BEIR),0..5sec; Recall; F1-Micro (Test); Precision; Question + Answer score; AveriTeC; F1-Macro (Test); nDCG@10; Question Only score; Accuracy (Test),FEVER (3-way); Sentence Ambiguity; FEVER (2-way); Known Unknowns; Misconceptions,Miscellaneous,N/A
Key Information Extraction,12,SROIE; ETD500; DocILE; Information Extraction from Tables; ARF; Kleister NDA; POIE; SOMD; SIBR; SIMARA,SROIE; ETD500; Kleister NDA; SIMARA; CORD; EPHOIE,F1; Accuracy; F1 (%); Average F1,Key-value Pair Extraction,Computer Vision; Natural Language Processing,"Key Information Extraction (KIE) is aimed at extracting structured information (e.g. key-value pairs) from form-style documents (e.g. invoices), which makes an important step towards intelligent docum..."
Zero-Shot Composed Image Retrieval (ZS-CIR),11,ImageNet-R; WebVid-CoVR; CIRR; CIRCO; GeneCIS; ImageNet; NICO++; COCO (Common Objects in Context); Fashion IQ; PatternCom,N/A,N/A,N/A,N/A,N/A
Outlier Detection,11,ECG5000; SKAB; WikiSem500; Fashion-MNIST; Vistas-NP; ImageNet-O; Epinion; MVTecAD; pathbased; Ecoli,ECG5000; SKAB; Breast cancer Wisconsin_class 4; Fashion-MNIST; Hepatitis; Balance scale_class 1; Internet Ad; Heart-C; Ionosphere_class b; Glass identification,Average F1; Average Accuracy; AUC-ROC; Accuracy; AUROC; AUC,One-class classifier; Outlier Interpretation; outlier ensembles; Graph Outlier Detection,Methodology; Graphs,**Outlier Detection** is a task of identifying a subset of a given data set which are considered anomalous in that they are unusual from other instances. It is one of the core data mining tasks and is...
Scene Graph Generation,11,SpaceSGG; GQA; 4D-OR; PSG Dataset; VRD; MM-OR; 3DSSG; Haystack; COCO (Common Objects in Context); Visual Genome,VRD; MM-OR; 3R-Scan; MS-COCO; 4D-OR; Visual Genome; GQA,zR@100; Top-5 Accuracy; R@20; mR@50; Recall@50; Recall@20; zR@20; F1; Recall@100; mean Recall @100,Panoptic Scene Graph Generation; Unbiased Scene Graph Generation,Computer Vision,"A scene graph is a structured representation of an image, where nodes in a scene graph correspond to object bounding boxes with their object categories, and edges correspond to their pairwise relation..."
Depth Completion,11,"Matterport3D; PLAD; ConSLAM; NYUv2; VOID; Multi-Spectral Stereo Dataset  (RGB, NIR, thermal images, LiDAR, GPS/IMU); KITTI-Depth; BIDCD; SuperCaustics; KITTI",Matterport3D; PLAD; KITTI Depth Completion 500 points; VOID; VOID-150; NYU-Depth V2; KITTI; KITTI Depth Completion Eigen Split; KITTI Depth Completion,iMAE; RMSE; RMSE ; iRMSE; REL; Runtime [ms]; MAE,N/A,Computer Vision,"The **Depth Completion** task is a sub-problem of depth estimation. In the sparse-to-dense depth completion problem, one wants to infer the dense depth map of a 3-D scene given an RGB image and its co..."
Graph Matching,11,IMCPT-SparseGM-100; RARE; PASCAL VOC; SPair-71k; DispScenes; IMCPT-SparseGM-50; Mindboggle; CUB-200-2011; Linux; Occluded REID,IMCPT-SparseGM-100; RARE; CUB; Willow Object Class; PASCAL VOC; SPair-71k; IMCPT-SparseGM-50,F1 score; matching accuracy; Spearman Correlation,N/A,Graphs,**Graph Matching** is the problem of finding correspondences between two sets of vertices while preserving complex relational information among them. Since the graph structure has a strong capacity to...
Multimodal Emotion Recognition,11,Video Dataset; RESD; Werewolf-XL; DEAP; MELD; CMU-MOSEI; CPED; EMOTIC; IEMOCAP; SES,CMU-MOSEI-Sentiment-3; CMU-MOSEI-Sentiment; Expressive hands and faces dataset (EHF).; MELD-Sentiment; MELD; IEMOCAP; IEMOCAP-4,Weighted F1; F1; v2v error; Accuracy; Weighted Recall,Video Emotion Detection,Computer Vision,This is a leaderboard for multimodal emotion recognition on the IEMOCAP dataset. The modality abbreviations are  A: Acoustic  T: Text  V: Visual    Please include the modality in the bracket after the...
Head Pose Estimation,11,HPD; AFLW; Biwi Kinect Head Pose; COFW; ARKitFace; ICT-3DHP; Panoptic; DAD-3DHeads; AFLW2000-3D; WFLW,N/A,N/A,N/A,N/A,N/A
Unsupervised Object Segmentation,11,DUTS; ObjectsRoom; FBMS; Shelf&Tote Training Dataset; DAVIS 2016; ECSSD; SegTrack-v2; ShapeStacks; FBMS-59; Multi-dSprites,DUTS; ObjectsRoom; Shelf&Tote Training Dataset; ECSSD; DAVIS 2016; ShapeStacks; FBMS-59; SegTrack-v2; ClevrTex,ARI-FG; ARI; MSE; mIoU; J score,N/A,Computer Vision,Image credit: [ClevrTex: A Texture-Rich Benchmark for Unsupervised Multi-Object Segmentation](https://paperswithcode.com/paper/clevrtex-a-texture-rich-benchmark-for)
Referring Expression Segmentation,11,A2Dre; CLEVR-Ref+; Referring Expressions for DAVIS 2016 & 2017; Google Refexp; JHMDB; A2Dre+; Refer-YouTube-VOS; DAVIS 2017; PhraseCut; RefCOCO,RefCOCO testB; PhraseCut; A2D Sentences; RefCoCo val; G-Ref test A; ReferIt; RefCOCOg-test; Referring Expressions for DAVIS 2016 & 2017; A2Dre test; RefCOCO+ test B,IoU (%); IoU; Zero-Shot Transfer; Overall IoU; J&F Full video; J; IoU overall; Pr@0.5; Precision@0.9; Mean IoU,Weakly Supervised Referring Expression Segmentation; Generalized Referring Expression Segmentation,Computer Vision,"The task aims at labeling the pixels of an image or video that represent an object instance referred by a linguistic expression. In particular, the referring expression (RE) must allow the identificat..."
3D Face Reconstruction,11,FaceWarehouse; REALY; Florence; FaMoS; HEADSET; NoW Benchmark; DAD-3DHeads; FFHQ-UV; AFLW2000-3D; ExPose,!(()&&!|*|*|; Stirling-HQ (FG2018 3D face reconstruction challenge); REALY; 13.8; NoW Benchmark; AFLW2000-3D; Stirling-LQ (FG2018 3D face reconstruction challenge); REALY (side-view); Florence,Mean NME; 0..5sec; Stdev Reconstruction Error (mm); Median Reconstruction Error; all; @forehead; RMSE Cooperative; Mean Reconstruction Error (mm); RMSE Indoor; @cheek,Facial Recognition and Modelling,Computer Vision,**3D Face Reconstruction** is a computer vision task that involves creating a 3D model of a human face from a 2D image or a set of images. The goal of 3D face reconstruction is to reconstruct a digita...
Action Anticipation,11,EGTEA; OST; VIENA2; Ego4D; CP2A dataset; EPIC-KITCHENS-100; DARai; MM-OR; Assembly101; EgoExoLearn,EGTEA; EPIC-KITCHENS-55 (Unseen test set (S2); 50-Salads; EPIC-KITCHENS-100 (test); EPIC-KITCHENS-55 (Seen test set (S1)); EPIC-KITCHENS-100; Assembly101; EgoExoLearn,Recall@5; Verbs Recall@5; Top 1 Accuracy - Noun; Top 1 Accuracy - Verb; Top 5 Accuracy - Act.; Top 1 Accuracy - Act.; Top-1 Accuracy; Accuracy; Top 5 Accuracy - Noun; Top 5 Accuracy - Verb,N/A,Computer Vision,"Next action anticipation is defined as observing 1, ... , T frames and predicting the action that happens after a gap of T_a seconds. It is important to note that a new action starts after T_a seconds..."
Bias Detection,11,international faces; WEATHub; rt-inod-bias; TwinViews-13k; NewB; StereoSet; CI-MNIST; BASIL; Filipino CrowS-Pairs and Filipino WinoQueer; CLEAR-Bias,rt-inod-bias; ICAT LLM bias; StereoSet; Wiki Neutrality Corpus; PlantVillage_8px,Accuracy (%); SS; ICAT Score; F1; Best-of; LMS,Selection bias,Natural Language Processing,"Bias detection is the task of detecting and measuring racism, sexism and otherwise discriminatory behavior in a model (Source: https://stereoset.mit.edu/)"
Medical Visual Question Answering,11,MediConfusion; VQA-RAD; SLAKE-English; Kvasir-VQA; SLAKE; MedPromptX-VQA; OVQA; MedTrinity-25M; PathVQA; PMC-VQA,N/A,N/A,N/A,N/A,N/A
Image Manipulation,11,LRS2; CelebAMask-HQ; Satire Dataset; CASIA (OSN-transmitted - Whatsapp); CASIA V2; DSO (OSN-transmitted - Facebook); Digital Forensics 2023 dataset - DF2023; CASIA (OSN-transmitted - Facebook); CASIA (OSN-transmitted - Weibo); NIST (OSN-transmitted - Facebook),LRS2,LPIPS (S1); SIFID (S1); SIFID (S2); LPIPS (S3); SIFID (S3); LPIPS (S4); SIFID (S5); SIFID (S4); LPIPS (S5); LPIPS (S2),N/A,Computer Vision,"Image Manipulation is the process of altering or transforming an existing image to achieve a desired effect or to modify its content. This can involve various techniques and tools to enhance, modify, ..."
Dimensionality Reduction,11,SoF; GoodSounds; Deep Fakes Dataset; ALGAD; EMNIST; HolStep; Oxford105k; AtariARI; Oxford-Affine; CN-CELEB,MCA; EMNIST,Classification Accuracy,Online nonnegative CP decomposition; Supervised dimensionality reduction,Methodology; Computer Vision,"Dimensionality reduction is the task of reducing the dimensionality of a dataset.    <span style=""color:grey; opacity: 0.6"">( Image credit: [openTSNE](https://github.com/pavlin-policar/openTSNE) )</sp..."
Event-based vision,11,MS-EVS Dataset; Spike-X4K; N-ImageNet; EKubric; TUM-VIE; Robust e-NeRF Synthetic Event Dataset; FE108; Event-Human3.6m; RGB-DAVIS Dataset; COESOT,1 Megapixel Automotive Detection Dataset,mAP,Event-based Motion Estimation; Event-Based Video Reconstruction; Event-based Optical Flow,Computer Vision,"An event camera, also known as a neuromorphic camera, silicon retina or dynamic vision sensor, is an imaging sensor that responds to local changes in brightness. Event cameras do not capture images us..."
Material Recognition,11,MINC; LabPics; OpenSurfaces; MatSeg; DMS; LAS&T: Large Shape & Texture Dataset; MatSim; MCubeS; TransProteus; SpectroVision,N/A,N/A,N/A,Computer Vision,"Material recognition focuses on identifying classes, types, states, and properties of materials."
Visual Grounding,11,DIOR-RSVG; Mono3DRefer; SkyEye-968k; AutomotiveUI-Bench-4K; SK-VG; VisArgs; MRR-Benchmark; Infinity-MM; A Game Of Sorts; RefCOCO,RefCOCO+ test B; RefCOCO+ val; RefCOCO+ testA; RefCOCO testA,Accuracy (%); IoU,3D visual grounding; Person-centric Visual Grounding; Phrase Extraction and Grounding (PEG),Computer Vision,"Visual Grounding (VG) aims to locate the most relevant object or region in an image, based on a natural language query. The query can be a phrase, a sentence, or even a multi-round dialogue. There are..."
Multi-Label Image Classification,10,BigEarthNet; COCO-MLT; MSCOCO; dacl10k; LADI v2; VOC-MLT; Bengali.AI Handwritten Graphemes; COCO (Common Objects in Context); Sewer-ML; VizWiz-Classification,BigEarthNet (official test set); BigEarthNet; VOC2007; MSCOCO; BigEarthNet-10%; BigEarthNet-S1 (official test set); VizWiz-Classification,FScore; mAP; official split; F1 Score; mAP (macro); mAP (micro); Accuracy; mean average precision; MAP,Multi-label Image Recognition with Partial Labels,Computer Vision,The Multi-Label Image Classification focuses on predicting labels for images in a multi-class classification problem where each image may belong to more than one class.
Human Part Segmentation,10,Pascal Panoptic Parts; Human3.6M; MHP; Cityscapes Panoptic Parts; CIHP; VESSEL12; CCIHP; AeroPath; PASCAL-Part; TikTok Dataset,Human3.6M; CIHP; ATR; PASCAL-Part; PASCAL-Person-Part; MHP v2.0,mIoU; Mean IoU; pACC,N/A,Computer Vision,N/A
Unsupervised Object Detection,10,Clipart1k; LeukemiaAttri; PASCAL VOC 2007; Objects365; UVO; Watercolor2k; KITTI; Comic2k; OpenImages-v6; LVIS,N/A,N/A,N/A,N/A,N/A
Table Detection,10,IIIT-AR-13K; ICDAR 2019; ICDAR 2013; PubTables-1M; SciTSR; CISOL; TableBank; FUNSD; STDW; TNCR Dataset,STDW; ICDAR2013; ICDAR 2019,Weighted Average F1-score; Avg F1; AP; IoU,N/A,Miscellaneous,Image credit:[Table Detection in the Wild: A Novel Diverse Table Detection Dataset and Method](https://paperswithcode.com/paper/table-detection-in-the-wild-a-novel-diverse)
Passage Retrieval,10,CoreSearch; MS MARCO; PeerQA; DAPFAM; BEIR; Natural Questions; CSPRD; EntityQuestions; QAMPARI; GermanDPR,N/A,N/A,N/A,N/A,N/A
Out of Distribution (OOD) Detection,10,MUAD; FathomNet2023; Persian-ATIS; OpenImage-O; ImageNet-1k vs NINCO; CIFAR-10; RMOT-223; ImageNet-O; ATIS; SNIPS,N/A,N/A,N/A,N/A,N/A
Video Grounding,10,YouwikiHow; MAD; Kinetics-GEB+; Kinetics; QVHighlights; Animal Kingdom; Vript; DTTD-Mobile; LongVALE; STAR Benchmark,QVHighlights; MAD,"R@5,IoU=0.1; R@1,IoU=0.3; R@5,IoU=0.3; R@1,IoU=0.5; R@100,IoU=0.1; R@1,IoU=0.1; R@1,IoU=0.7; R@10,IoU=0.1; R@50,IoU=0.1",Boundary Grounding; Video Narrative Grounding,Computer Vision,"**Video grounding** is the task of linking spoken language descriptions to specific video segments. In video grounding, the model is given a video and a natural language description, such as a sentenc..."
Video Recognition,10,BosphorusSign22k; Imbalanced-MiniKinetics200; Kinetics; MIDV-2019; MOD++; Crowd 11; Win-Fail Action Understanding; RAISE-LPBF; 3DYoga90; L-SVD,N/A,N/A,N/A,N/A,N/A
Online Multi-Object Tracking,10,Oxford Town Center; MOT16; BEE23; SportsMOT; PersonPath22; MOTChallenge; MMPTRACK; MOT15; CholecTrack20; MOT17,N/A,N/A,N/A,N/A,N/A
Scene Segmentation,10,NYUv2; USIS10K; UAVid; MovieNet; ScanNet; SUN RGB-D; DARai; Mila Simulated Floods; Berkeley DeepDrive Video; StreetHazards,SUN-RGBD; UAVid; MovieNet; ScanNet; StreetHazards; NYU Depth v2,Average Accuracy; Open-mIoU; Category mIoU; Mean IoU; 3DIoU; AP,Thermal Image Segmentation,Computer Vision,Scene segmentation is the task of splitting a scene into its various object components.    Image adapted from [Temporally coherent 4D reconstruction of complex dynamic scenes](https://paperswithcode.c...
Face Swapping,10,HOD; Celeb-DF; VideoForensicsHQ; VGGFace2 HQ; FaceForensics++; DFDC; DeeperForensics-1.0; DFDM; AFLW2000-3D; WildDeepfake,N/A,N/A,N/A,N/A,N/A
Facial Attribute Classification,10,DiveFace; CelebV-HQ; A View From Somewhere (AVFS); FairFace; MORPH; UTKFace; IMDB-Clean; bFFHQ; LFWA; LAOFIW Dataset,N/A,N/A,N/A,N/A,N/A
Answer Selection,10,MilkQA; WikiQAar; UDC; WikiQA; WikiHowQA; ASNQ; SelQA; CICERO; InsuranceQA; TrecQA,N/A,N/A,N/A,N/A,N/A
LIDAR Semantic Segmentation,10,A Curb Dataset; nuScenes (Cross-City UDA); SemanticSTF; ULS labeled data; S.MID; ScribbleKITTI; WildScenes; nuScenes; Paris-Lille-3D; SemanticKITTI,ULS labeled data; S.MID; SemanticSTF; nuScenes; Paris-Lille-3D; SemanticKITTI,G-mean; mIOU; Specificity; test mIoU; Mean IoU; val mIoU; Binary Accuracy,N/A,Computer Vision,N/A
Visual Dialog,10,GuessWhat?!; VisPro; Blended Skill Talk; Image-Chat; Wizard of Wikipedia; EmpatheticDialogues; CLEVR-Dialog; ConvAI2; VisDial; PhotoBook,BlendedSkillTalk; Image-Chat; Wizard of Wikipedia; VisDial v0.9 val; EmpatheticDialogues; Visual Dialog v1.0 test-std; ConvAI2; VisDial v1.0 test-std,NDCG (x 100); R@1; F1; R@5; R@10; NDCG; Mean; BLEU-4; Mean Rank; MRR (x 100),N/A,Computer Vision,"Visual Dialog requires an AI agent to hold a meaningful dialog with humans in natural, conversational language about visual content. Specifically, given an image, a dialog history, and a follow-up que..."
Motion Estimation,10,EV-IMO; VBR; Headcam; X-ray and Visible Spectra Circular Motion Images Dataset; DDD17; Fisheye; Retinal Microsurgery; PST900; ADVIO; SegTHOR,N/A,N/A,N/A,Computer Vision,"**Motion Estimation** is used to determine the block-wise or pixel-wise motion vectors between two frames.      <span class=""description-source"">Source: [MEMC-Net: Motion Estimation and Motion Compens..."
Acoustic Scene Classification,10,TUT Sound Events 2018; DCASE 2019 Mobile; DCASE 2013; TUT Urban Acoustic Scenes 2018; LITIS Rouen; DCASE 2016; TAU Urban Acoustic Scenes 2019; MTG-Jamendo; TUT Acoustic Scenes 2017; CochlScene,DCASE 2019 Mobile; TUT Urban Acoustic Scenes 2018; TAU Urban Acoustic Scenes 2019; TUT Acoustic Scenes 2017; CochlScene,Accuracy; 1:1 Accuracy; Acc,N/A,Audio,The goal of acoustic scene classification is to classify a test recording into one of the provided predefined classes that characterizes the environment in which it was recorded.    Source: [DCASE 201...
Object Detection In Indoor Scenes,10,ISOD; Transparent Object Images | Indoor Object Dataset; Stairs Image Dataset | Parts of House | Indoor; Bottles and Cups Dataset | Household Objects; Masks Dataset | Unattended Mask Images; Electronics Object Image Dataset | Computer Parts; Mobile Phone Dataset | Smartphone & Feature Phone; Kitchen Scenes; SUN RGB-D; Suitcase/Luggage Dataset Indoor Object Image,N/A,N/A,N/A,N/A,N/A
Semantic SLAM,10,ConSLAM; WildScenes; SLAM2REF; TorWIC; Bonn RGB-D Dynamic; ConsInv Dataset; ViViD++; DTTD-Mobile; TUM RGB-D; KITTI-360,N/A,N/A,N/A,N/A,N/A
10-shot image generation,10,How do you request an upgrade to first class?; ####How do i ask a question at Expedia?; FlyingThings3D; ((Speak))How do I speak with someone at Expedia?; [FaQ's--Help]How do I speak to someone on Expedia?; Gun Detection Dataset; Music21; FQL-Driving; [[Ask!!Question]]How do I ask a question on Expedia?; MEAD,.; FlyingThings3D; Music21; FQL-Driving; Babies; MEAD,0..5sec; 12k; 10-20% Mask PSNR; FID; 0-shot MRR,Face Swapping; Text to Video Retrieval; Audio Super-Resolution; Talking Face Generation; Image Restoration,Robots; Computer Code; Time Series; Adversarial; Computer Vision; Audio; Miscellaneous; Playing Games; Knowledge Base; Music; Medical; Graphs,Generate 10 image of famous arts
Multiview Detection,10,Wildtrack; MultiviewC; CVCS; Home Action Genome; GMVD; CityStreet; RailEye3D Dataset; MultiviewX; Three-view Synthetic data; DOLPHINS,N/A,N/A,N/A,N/A,N/A
Remote Sensing Image Classification,10,AID; OpenStreetMap Multi-Sensor Scene Classification; RTI Rwanda Drone Crop Types; HRPlanesV2; GDIT; DRIFT; WHU-RS19; Million-AID; FireRisk; Remote Flash LiDAR Vehicles Dataset,FireRisk,Accuracy (%),Sentinel-1 SAR processing; Webcam (RGB) image classification,Computer Code,N/A
3D Depth Estimation,10,Relative Human; EUEN17037_Daylight_and_View_Standard_TestDataSet; Pano3D; WinSyn; Aria Digital Twin Dataset; DurLAR; DTTD-Mobile; IBISCape; HUMAN4D; DRACO20K,Relative Human,PCDR-Kid; mPCDK; PCDR-Teen; PCDR-Adult; PCDR-Baby; PCDR,Transparent Object Depth Estimation,Computer Vision,Image: [monodepth2](https://github.com/nianticlabs/monodepth2)
Font Recognition,10,AdobeVFR syn; VFR-447; AdobeVFR real; VFR-Wild; VFR-2420; Persian Font Recognition (PFR); Explor_all; Dafonts Free; Persian Text Image Segmentation (PTI SEG); MRR-Benchmark,AdobeVFR syn; VFR-447; AdobeVFR real; VFR-Wild; VFR-2420; Persian Font Recognition (PFR); Explor_all; Persian Text Image Segmentation (PTI SEG),Top 5 Accuracy; Top 1 Accuracy; Top 5 Error Rate; IOU50; Top-1 Error Rate; Top 10 Accuracy,N/A,Computer Vision,Font recognition (also called *visual font recognition* or *optical font recognition*) is the task of identifying the font family or families used in images containing text. Understanding which fonts ...
Time Series Anomaly Detection,10,FedTADBench; Consumer Spendings; SMAP; UCR Anomaly Archive; edeniss2020; MSL; Paderbone University Bearing Fault Benckmark; SMD; ODDS; ATMs fault prediction,SMAP; KPI; UCR Anomaly Archive; MSL; SMD; SWaT; WADI; Yahoo A1,accuracy; F1 Score; AUPR; F1 score; Recall; precision,N/A,Time Series,N/A
Within-Session Motor Imagery (left hand vs. right hand),10,Zhou2016 MOABB; BNCI2014-001 MOABB; Shin2017A MOABB; Weibo2014 MOABB; Schirrmeister2017 MOABB; Cho2017 MOABB; BNCI2014-004 MOABB; Lee2019-MI MOABB; GrosseWentrup2009 MOABB; PhysionetMotorImagery MOABB,N/A,N/A,N/A,N/A,N/A
Color Image Denoising,9,BSD; RENOIR; McMaster; Urban100; CBSD68; ImageNet; Cell; Fingerprint inpainting and denoising; Darmstadt Noise Dataset,N/A,N/A,N/A,N/A,N/A
Video Description,9,M-VAD Names; Flickr30k; ActivityNet Entities; ViDAS; DeVAn; YouCook; TACoS Multi-Level Corpus; VideoCC3M; EDUB-Seg,N/A,N/A,N/A,N/A,N/A
Unsupervised Semantic Segmentation,9,KITTI-STEP; Nighttime Driving; Dark Zurich; COCO-Stuff; ACDC (Adverse Conditions Dataset with Correspondences); SUIM; ImageNet-S; COCO (Common Objects in Context); Cityscapes,COCO-Stuff-15; Cityscapes test; ImageNet-S-300; Nighttime Driving; COCO-All; Dark Zurich; COCO-Persons; COCO-Stuff-27; ACDC (Adverse Conditions Dataset with Correspondences); COCO-Stuff-3,Linear Classifier [mIoU]; mIoU (val); Linear Classifier [Accuracy]; mIoU (test); Accuracy; Clustering [mIoU]; FCN [mIoU]; mIoU; Clustering [Accuracy]; Pixel Accuracy,Unsupervised Semantic Segmentation with Language-image Pre-training,Computer Vision,"Models that learn to segment each image (i.e. assign a class to every pixel) without seeing the ground truth labels.    <span style=""color:grey; opacity: 0.6"">( Image credit: [SegSort: Segmentation by..."
Conditional Image Generation,9,Large Labelled Logo Dataset (L3D); CelebAMask-HQ; ImageNet-LT; CIFAR-100; Human-Art; ArtBench-10 (32x32); CIFAR-10; COCO (Common Objects in Context); Tiny ImageNet,CelebAMask-HQ; ImageNet-LT; CIFAR-10 LT; CIFAR-100; ArtBench-10 (32x32); CIFAR-10; ImageNet 64x64; COCO-Animals; ImageNet 128x128; Tiny ImageNet,LPIPS; Intra-FID; Inception Score; Inception score; FID; IS; mIoU,Human-Object Interaction Generation; Image-Guided Composition; Noisy Semantic Image Synthesis,Computer Vision,"Conditional image generation is the task of generating new images from a dataset conditional on their class.    <span style=""color:grey; opacity: 0.6"">( Image credit: [PixelCNN++](https://github.com/o..."
Interactive Segmentation,9,PUMaVOS; DAVIS; ssTEM; PASCAL VOC; DAVIS-585; COCO (Common Objects in Context); Cityscapes; RClicks; SBD,COCO minival; DAVIS; Rooftop; Berkeley; ssTEM; PASCAL VOC; PascalVOC; DAVIS-585; DRIONS-DB; Cityscapes val,NoC@90; Instance Average IoU; NoC@95; NoC@85; NoC@80,N/A,Computer Vision,N/A
Real-Time Object Detection,9,Kvasir; PASCAL VOC 2007; Kvasir-SEG; Kvasir-Instrument; Argoverse-HD; COCO (Common Objects in Context); SFCHD; Endotect Polyp Segmentation Challenge Dataset; Hyper-Kvasir Dataset,N/A,N/A,N/A,N/A,N/A
Robust Object Detection,9,Occluded COCO; Separated COCO; PASCAL VOC 2007; RF100; Aircraft Context Dataset; DWD; COCO (Common Objects in Context); Apron Dataset; Cityscapes,N/A,N/A,N/A,N/A,N/A
Edge Detection,9,BRIND; INRIA-Horse; CID; UDED; MDBD; BIPED; BSDS500; Cityscapes; SBD,BRIND; CID; Cityscapes test; UDED; MDBD; BIPED; BSDS500; SBD,ODS; F1; Number of parameters (M); Maximum F-measure; AP,N/A,Computer Vision,**Edge Detection** is a fundamental image processing technique which involves computing an image gradient to quantify the magnitude and direction of edges in an image. Image gradients are used in vari...
Scene Generation,9,3D FRONT HUMAN; Replica; InstaOrder; CoDraw; AVD; KITTI; GoogleEarth; VizDoom; OSM,Replica; AVD; KITTI; GoogleEarth; VizDoom; OSM,Average FID; FID; Camera Error; KID; SwAV-FID; Depth Error,N/A,Computer Vision,make to t shirt an Ad with a little bit of action
Vehicle Re-Identification,9,Vehicle-Rear; VehicleX; VeRi-776; VeRi-Wild; VRAI; CityFlow; Car datasets in multiple scenes; VehicleID; Vehicle-1M,VeRi-Wild Small; VehicleID Medium; VehicleID Small; VeRi; VeRi-776; VeRi-Wild Large; VeRi-Wild Medium; CityFlow; VehicleID Large; VehicleID,Rank-5; Rank-1; mAP; CMC5; Rank-10; CMC10; CMC1; Rank1; Rank5; MAP,N/A,Computer Vision,"Vehicle re-identification is the task of identifying the same vehicle across multiple cameras.    <span style=""color:grey; opacity: 0.6"">( Image credit: [A Two-Stream Siamese Neural Network for Vehicl..."
Small Data Image Classification,9,WikiChurches; DEIC Benchmark; CIFAR-100; TMED; CIFAR-10; CUB-200-2011; UCF-Crime; Ecoli; ImageNet 50 samples per class,N/A,N/A,N/A,N/A,N/A
Fine-Grained Visual Recognition,9,FGVC-Aircraft; Stanford Dogs; WebFG-496; WikiChurches; New Plant Diseases Dataset; CUB-200-2011; MTL-AQA; WHOI-Plankton; FeathersV1,N/A,N/A,N/A,N/A,N/A
Gait Recognition,9,USF; CASIA-B; OUMVLP; OUMVLP-Pose; PsyMo; CCGR; TUM-GAID; Gait3D; Gait3D-Parsing,OUMVLP; Gait3D,Rank-5; mAP; Rank-1; Averaged rank-1 acc(%); mINP,Gait Recognition in the Wild; Multiview Gait Recognition,Computer Vision,"<span style=""color:grey; opacity: 0.6"">( Image credit: [GaitSet: Regarding Gait as a Set for Cross-View Gait Recognition](https://github.com/AbnerHqC/GaitSet) )</span>"
6D Pose Estimation using RGBD,9,T-LESS; 6IMPOSE; SLAM2REF; UW Indoor Scenes (UW-IS) Occluded dataset; YCB-Ev 1.1; YCB-Video; REAL275; LM; Drunkard's Dataset,N/A,N/A,N/A,N/A,N/A
3D Shape Reconstruction,9,Pix3D; FewSOL; LAS&T: Large Shape & Texture Dataset; Foot3D; ApolloCar3D; HUMAN4D; 3D-POP; 4DFAB; Hypersim,ApolloCar3D; Pix3D,EMD; A3DP; CD; IoU,3D Shape Reconstruction From A Single 2D Image,Computer Vision,"Image credit: [GSNet: Joint Vehicle Pose and Shape Reconstruction with Geometrical and Scene-aware Supervision  , ECCV'20](https://www.ecva.net/papers/eccv_2020/papers_ECCV/papers/123600511.pdf)"
Pedestrian Attribute Recognition,9,PEARL30K; UPAR; Market1501-Attributes; DukeMTMC-attribute; CAR; PETA; PA-100K; RAP; UAV-Human,UPAR; Market1501-Attributes; DukeMTMC-attribute; RAPv2; RAP; PA-100K; PETA; UAV-Human,UCS; UCC; Hat; Accuracy ; Accuracy; Gender; LCC; LCS; Backpack; F1 score,N/A,Computer Vision,"Pedestrian attribution recognition is the task of recognizing pedestrian features - such as whether they are talking on a phone, whether they have a backpack, and so on.    <span style=""color:grey; op..."
Action Quality Assessment,9,Multimodal PISA; FineDiving; EgoExoLearn; JIGSAWS; AQA-7; Rhythmic Gymnastic; MTL-AQA; UI-PRMD; Fitness-AQA,FineDiving; EgoExoLearn; JIGSAWS; AQA-7; Rhythmic Gymnastic; MTL-AQA; UI-PRMD; KIMORE,Accuracy; Average mean absolute error; Spearman Correlation; RL2(*100),N/A,Computer Vision,Assessing/analyzing/quantifying how well an action was performed.
Video Reconstruction,9,Tai-Chi-HD; MGif; Event-Camera Dataset; Videezy4K; MVSEC; SEN12MS-CR-TS; TED-talks; I2-2000FPS; VoxCeleb1,Tai-Chi-HD (256); Tai-Chi-HD; UVG; MGif; Tai-Chi-HD (512); Event-Camera Dataset; MVSEC; TED-talks; VoxCeleb,LPIPS; Mean Squared Error; MKR; AKD; Average PSNR (dB); AED; Model Size (M); L1,N/A,Computer Vision,"<span class=""description-source"">Source: [Deep-SloMo](https://github.com/avinashpaliwal/Deep-SloMo)</span>"
Trajectory Planning,9,rounD Dataset; nuScenes; highD Dataset; uniD Dataset; exiD Dataset; DARai; short-MetaWorld; inD Dataset; ToolBench,nuScenes; ToolBench,Collision-3s; Collision-2s; Win rate; L2-Avg; Collision-1s; Collision-Avg; L2-2s; L2-3s; L2-1s,Sokoban,Playing Games,Trajectory planning for industrial robots consists of moving the tool center point from point A to point B while avoiding body collisions over time.  Trajectory planning is sometimes referred to as mo...
Defect Detection,9,ISP-AD; ARMBench; Regressors-Regressions Dataset; DAGM2007; Deep PCB; CodeXGLUE; KolektorSDD2; Honeycombs in Concrete; KolektorSDD,N/A,N/A,N/A,N/A,N/A
Vision-Language Navigation,9,ReALFRED; TEACh; BnB; XL-R2R; ReaSCAN; Talk the Walk; R2R; PInNED; SDN,Room2Room,spl,Vision-Language-Action,Robots; Computer Vision,"Vision-language navigation (VLN) is the task of navigating an embodied agent to carry out natural language instructions inside real 3D environments.    <span style=""color:grey; opacity: 0.6"">( Image c..."
Dense Video Captioning,9,ViTT; Sieve & Swap - HowTo100M (Cooking); VidChapters-7M; Vript; ActivityNet Captions; YouCook; MTL-AQA; LongVALE; YouCook2,ViTT; ActivityNet Captions; YouCook2; VidChapters-7M,BLEU4; BLEU-3; DIV-2; DIV-1; Precision; F1; SODA; RE-4; METEOR; CIDEr,Zero-shot dense video captioning,Computer Vision,"Most natural videos contain numerous events. For example, in a video of a “man playing a piano”, the video might also contain “another man dancing” or “a crowd clapping”. The task of dense video capti..."
Saliency Prediction,9,CapMIT1003; AViMoS; MSU Video Saliency Prediction; iSUN; CAT2000; Salient-KITTI; SALICON; UIEB; SUIM,MIT300; SALICON; CAT2000; SALECI,KL; sAUC; IG; SIM; AUC-Judd; CC; NSS; KLD; AUC,Aerial Video Saliency Prediction; Few-Shot Transfer Learning for Saliency Prediction,Computer Vision,A saliency map is a model that predicts eye fixations on a visual scene. Saliency prediction is informed by the human visual attention mechanism and predicts the possibility of the human eyes to stay ...
Blind Super-Resolution,9,BSD; Set14; Manga109; Urban100; KID-F; Set5; DRealSR; BSD100; DIV2KRK,N/A,N/A,N/A,N/A,N/A
Intrusion Detection,9,UNSW-NB15; CIC; CICIDS2017; 20NewsGroups; IoT Network Intrusion Dataset; IoT ENVIRONMENT DATASET; Kitsune Network Attack Dataset; EDGE-IIOTSET; ARINC 429 Voltage Data,UNSW-NB15; CICIDS2017; 20NewsGroups; ^(#$!@#$)(()))******; CIC-DDoS; CIC-DoS,Accuracy (%); 0..5sec; Actions Top-1 (S2); Recall (Macro Avg); Precision (Macro Avg); F1 Score (Macro Avg); AUC,Network Intrusion Detection,Miscellaneous; Natural Language Processing,"**Intrusion Detection** is the process of dynamically monitoring events occurring in a computer system or network, analyzing them for signs of possible incidents and often interdicting the unauthorize..."
Medical Report Generation,9,IU X-Ray; HistGen WSI-Report Dataset; MIMIC-CXR; PathVQA; SMR IU X-Ray; MedTrinity-25M; CASIA-CXR; RaTE-NER; LLaVA-Rad MIMIC-CXR Annotations,HistGen WSI-Report Dataset; IU X-Ray; MIMIC-CXR,BLEU-3; Example-F1-14; Micro-Precision-5; BLEU-2; F1 RadGraph; Micro-Recall-5; Micro-F1-5; Example-Recall-14; BLEU-1; METEOR,N/A,Medical; Methodology,Medical report generation (MRG) is a task which focus on training AI to automatically generate professional report according the input image data. This can help clinicians make faster and more accurat...
Change Point Detection,9,TEP; SKAB; Turing Change Point Dataset; TSSB; MOSAD; Epinion; HASCD; CSTS; Labelling for Explosions and Road accidents from UCF-Crime,TEP; TSSB; SKAB,NAB (lowFP); NAB (standard); Covering; Relative Change Point Distance; NAB (LowFN),N/A,Time Series,**Change Point Detection** is concerned with the accurate detection of abrupt and significant changes in the behavior of a time series.    Change point detection is the task of finding changes in the ...
Surface Reconstruction,9,SceneNet; Stanford-ORB; P2S; VIRDO Dataset; SynFoot; ANIM; Foot3D; OmniObject3D; OMMO,N/A,N/A,N/A,N/A,N/A
Food Recognition,9,Recipe1M+; FoodX-251; ISIA Food-500; CNFOOD-241; ChineseFoodNet; Food Image Classification Dataset; KenyanFood13; Indian Food Image Dataset; CNFOOD-241-Chen,N/A,N/A,N/A,Computer Vision,N/A
Camera Localization,9,Aachen Day-Night; 12 Scenes; ConSLAM; SLAM2REF; SpaGBOL; CrossLoc Benchmark Datasets; HPS; PEnG; ZInd,Oxford RobotCar Full; Aachen Day-Night benchmark,"Mean Rotation Error; Mean Translation Error; Acc @ 1m, 5°; Acc @ 5m, 10°; Acc @ 0.5m, 2°",Cross-View Geo-Localisation; Camera Relocalization,Computer Vision,N/A
object-detection,9,Br35H :: Brain Tumor Detection 2020; Tea sickness - object detection; RF100; DiaMOS Plant; IP102; M5-Malaria Dataset; Underwater Object Detection Dataset; GenSC-6G; AODRaw,N/A,N/A,N/A,N/A,N/A
Sound Event Localization and Detection,9,BGG dataset; STARSS23; TAU-NIGENS Spatial Sound Events 2021; STARSS22; RWCP Sound Scene Database; L3DAS21; L3DAS22; PodcastFillers; TAU-NIGENS Spatial Sound Events 2020,TAU-NIGENS Spatial Sound Events 2021; RWCP Sound Scene Database; L3DAS21; PodcastFillers; STARSS22,LE-CD; ER≤20°; SELD score; LR-CD; Localization-dependent error rate (20°); location-dependent F1-score (macro); Class-dependent localization error; Class-dependent localization recall; F1≤20°; event-based F1 score,N/A,Audio,"Given multichannel audio input, a sound event detection and localization (SELD) system outputs a temporal activation track for each of the target sound classes, along with one or more corresponding sp..."
3D Medical Imaging Segmentation,9,VerSe; HaN-Seg; MRSpineSeg Challenge; KiTS19; RAD-ChestCT Dataset; SKM-TEA; EPISURG; BHSD; ATLAS v2.0,TCIA Pancreas-CT,Dice Score,Pancreas Segmentation,Medical,"3D medical imaging segmentation is the task of segmenting medical objects of interest from 3D medical imaging.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Elastic Boundary Projection fo..."
Within-Session Motor Imagery (right hand vs. feet),9,Zhou2016 MOABB; BNCI2014-002 MOABB; BNCI2014-001 MOABB; BNCI2015-001 MOABB; Weibo2014 MOABB; Schirrmeister2017 MOABB; AlexandreMotorImagery MOABB; BNCI2015-004 MOABB; PhysionetMotorImagery MOABB,N/A,N/A,N/A,N/A,N/A
Zero-Shot Transfer Image Classification,8,SUN; ImageNet-R; ImageNet-A; ObjectNet; ImageNet; ImageNet-S; Food-101; ImageNet-Sketch,SUN; ImageNet-R; ImageNet V2; ImageNet-A; CN-ImageNet V2; CN-ImageNet-A; ImageNet ReaL; ObjectNet; ImageNet-S; CN-ImageNet-Sketch,Top 5 Accuracy; Accuracy (Private); Param; Top 1 Accuracy; Accuracy (Public); Accuracy,N/A,Computer Vision,N/A
3D Object Reconstruction,8,Corn Seeds Dataset; The RBO Dataset of Articulated Objects and Interactions; BEHAVE; ShapeNet; X3D; A collection of 131 CT datasets of pieces of modeling clay containing stones; DTTD-Mobile; Parcel3D,Data3D−R2N2; BEHAVE; ShapeNet; RenderPeople,Point-to-surface distance (cm); Chamfer (cm); Chamfer Distance; Avg F1; Surface normal consistency; 3DIoU,Feature Splatting; Simulated Gaussian Manipulation; 3D Object Reconstruction From A Single Image; CAD Reconstruction,Computer Vision,Image: [Choy et al](https://arxiv.org/pdf/1604.00449v1.pdf)
Image-to-Text Retrieval,8,WHOOPS!; RSICD; Flickr30k; XM 3600; FewSOL; LAION-400M; FETA Car-Manuals; COCO (Common Objects in Context),Flickr30k; RSICD; AIC-ICC; RUC-CAS-WenLan; COCO; FETA Car-Manuals; WHOOPS!; COCO (Common Objects in Context),Recall@10; Recall@5; Image to Text Recall@1; Specificity; R@1; R@5; R@10; Recall@Sum; Recall@1,N/A,Natural Language Processing,"**Image-text retrieval** is the process of retrieving relevant images based on textual descriptions or finding corresponding textual descriptions for a given image. This task is interdisciplinary, com..."
Few-Shot Object Detection,8,LOGO-Net; BankNote-Net; FSOD; ELEVATER; CAMO-FS; COCO (Common Objects in Context); VizWiz-FewShot; LVIS,ODinW-13; ODinW-35; COCO 2017; CAMO-FS; MS-COCO (5-shot); MS-COCO (10-shot); LVIS v1.0 val; LVIS v1.0 test-dev; MS-COCO (30-shot); MS-COCO (1-shot),APc; APf; Average Score; AP50; APr; box AP; AP; AP75,Cross-Domain Few-Shot Object Detection,Computer Vision,**Few-Shot Object Detection** is a computer vision task that involves detecting objects in images with limited training data. The goal is to train a model on a few examples of each object class and th...
Layout-to-Image Generation,8,COCO-Stuff; LayoutBench; LayoutBench-COCO - Number; COCO (Common Objects in Context); LayoutBench-COCO - Combination; Visual Genome; LayoutBench-COCO - Size; LayoutBench-COCO - Position,N/A,N/A,N/A,N/A,N/A
Scene Flow Estimation,8,Middlebury 2003; FlyingThings3D; DeformingThings4D; EKubric; Argoverse 2; Spring; KITTI; VBR,Scene Flow; KITTI 2015 Scene Flow Training; Argoverse 2; Spring; KITTI 2015 Scene Flow Test,EPE Foreground Static; EPE Foreground Dynamic;  Runtime (s); D2-all; D1-all; EPE Background Static; EPE; SF-all; 1px total; Runtime (s),Self-supervised Scene Flow Estimation,Computer Vision,"Optical flow is a two-dimensional motion field in the image plane. It is the projection of the three-dimensional motion of the world. If the world is completely non-rigid, the motions of the points in..."
No-Reference Image Quality Assessment,8,CSIQ; LIVE; KADID-10k; KonIQ-10k; SPAQ; MSU NR VQA Database; UHD-IQA; TID2013,CSIQ; LIVE; 200k Short Texts for Humor Detection; KADID-10k; KonIQ-10k; SPAQ; UHD-IQA; TID2013,PLCC; 14 gestures accuracy; SRCC,Blind Image Quality Assessment; NR-IQA,Computer Vision,An Image Quality Assessment approach where no reference image information is available to the model. Sometimes referred to as Blind Image Quality Assessment (BIQA).
Video-Based Person Re-Identification,8,Airport; P-DESTRE; TVPReid; DukeMTMC-VideoReID; MARS-DL; iLIDS-VID; AG-VPReID; MARS,N/A,N/A,N/A,N/A,N/A
Semantic correspondence,8,AP-10K; FunKPoint; PF-WILLOW; SPair-71k; CUB-200-2011; PF-PASCAL; Caltech-101; AVMIT,AP-10K; PF-WILLOW; SPair-71k; CUB-200-2011; Caltech-101; PF-PASCAL,Mean PCK@0.1; Mean PCK@0.05; PCK (weak); IoU; IoU (weak); LT-ACC (weak); PCK; LT-ACC,Interspecies Facial Keypoint Transfer,Computer Vision,The task of semantic correspondence aims to establish reliable visual correspondence between different instances of the same object category.
Visual Commonsense Reasoning,8,QLEVR; ScienceQA; IconQA; GD-VCR; PolyMATH; WHOOPS!; VCR; Visual Commonsense Immorality benchmark,N/A,N/A,N/A,N/A,N/A
Seizure Detection,8,Siena Scalp EEG Database; BIDS CHB-MIT Scalp EEG Database; TUH EEG Seizure Corpus; SeizeIT1; SPaRCNet; CHB-MIT; BIDS Siena Scalp EEG Database; A dataset of neonatal EEG recordings with seizures annotations,TUH EEG Seizure Corpus; CHB-MIT,Accuracy; AUROC,N/A,Medical,"**Seizure Detection** is a binary supervised classification problem with the aim of classifying between seizure and non-seizure states of a patient.   <span class=""description-source"">Source: [ResOT: ..."
Image/Document Clustering,8,pixraw10P; iris; JAFFE; australian; pathbased; BA; Wine; warpPIE10P,pixraw10P; iris; pendigits; JAFFE; australian; BA; Wine; warpPIE10P,Accuracy (%); NMI; runtime (s),Self-Organized Clustering,Miscellaneous; Computer Vision,N/A
Anomaly Detection In Surveillance Videos,8,VFP290K; UBI-Fights; CamNuvem Dataset; VADD; ShanghaiTech; ShanghaiTech Campus; XD-Violence; UCF-Crime,N/A,N/A,N/A,N/A,N/A
Video Instance Segmentation,8,YouTube-VIS 2019; HQ-YTVIS; BDD100K; Youtube-VIS 2022 Validation; UVO; BURST; YouTube-VIS 2021; OVIS,YouTube-VIS validation; HQ-YTVIS; YouTube-VIS; Youtube-VIS 2022 Validation; OVIS validation; Youtube-VIS (trained with no video masks); YouTube-VIS 2021; BDD100K val,AP75_L; mAP_L; APmo; APho; AR10_L; AP50; AR1; AP50_L; Tube-Boundary AP; AR1_L,N/A,Computer Vision,"The goal of video instance segmentation is simultaneous detection, segmentation and tracking of instances in videos. In words, it is the first time that the image instance segmentation problem is exte..."
Click-Through Rate Prediction,8,MovieLens; TripClick; MerRec; KDD12; iPinYou; Criteo; KKBox; CBT,Avito; Company*; Book-Crossing; Amazon Dataset; MovieLens; Dianping; Bing News; iPinYou; Last.FM; KKBox,F1; Log Loss; Accuracy; LogLoss; AUC,N/A,Miscellaneous,"Click-through rate prediction is the task of predicting the likelihood that something on a website (such as an advertisement) will be clicked.    <span style=""color:grey; opacity: 0.6"">( Image credit:..."
Unsupervised Video Object Segmentation,8,MOSE; Referring Expressions for DAVIS 2016 & 2017; FBMS; BL30K; DAVIS 2016; DAVIS 2017; FBMS-59; SegTrack-v2,N/A,N/A,N/A,N/A,N/A
Video Semantic Segmentation,8,MOSE; VLOG Dataset; VSPW; CamVid; LaRS; ODMS; SegTrack-v2; Cityscapes,VSPW; CamVid; LaRS; Cityscapes val; Multispectral Video Semantic Segmentation,F1; Q; μ; Mean IoU; mIoU,Camera shot segmentation,Computer Vision,The goal of video semantic segmentation is to assign a predefined class to each pixel in all frames of a video. This requires the model not only to predict accurate segmentation masks but also to ensu...
Weakly-Supervised Semantic Segmentation,8,ADE20K; SEN12MS; ScribbleKITTI; ACDC Scribbles; CheXlocalize; PASCAL VOC 2012 test; Cityscapes; ScribbleSup,N/A,N/A,N/A,N/A,N/A
3D Absolute Human Pose Estimation,8,Relative Human; TotalCapture; Human3.6M; [[Talk!!Person]]How do I get a person on Expedia?; SportsPose; [[Free@cancellation]]Is Expedia really free cancellation?; HPS; InfiniteRep,Human3.6M; Surreal; Total Capture,PA-MPJPE; MPJPE; Average MPJPE (mm); MRPE,3D Face Animation; Text-to-Face Generation; Image to 3D; 3D Human Shape Estimation,Robots; Computer Vision; Knowledge Base,"This task aims to solve absolute (camera-centric not root-relative) 3D human pose estimation.     <span style=""color:grey; opacity: 0.6"">( Image credit: [RootNet](https://github.com/mks0601/3DMPPE_ROO..."
Human action generation,8,NTU RGB+D; Human3.6M; NTU RGB+D 120; UESTC RGB-D; NTU RGB+D 2D; PHSPD; FLAG3D; HumanAct12,NTU RGB+D; Human3.6M; NTU RGB+D 120; UESTC RGB-D; NTU RGB+D 2D; HumanAct12; CMU Mocap,MMDs (CS); MMDs (CV); MMDa (CV); MMDs; FID; FID (CV); FID (CS); Accuracy; MMDa; Test,Action Generation,Computer Vision,"Yan et al. (2019) CSGN:    ""When the dancer is stepping, jumping and spinning on the  stage, attentions of all audiences are attracted by the streamof the fluent and graceful movements. Building a  mo..."
Retinal Vessel Segmentation,8,ROSE; DRIVE; INSPIRE-AVR (LUNet subset); SMDG; UZLF; CHASE_DB1; STARE; HRF,ROSE-1 SVC; ROSE-1 DVC; ROSE-2; DRIVE; INSPIRE-AVR (LUNet subset); UZLF; CHASE_DB1; STARE; HRF; ROSE-1 SVC-DVC,mIOU; AUC; Specificity; MCC; Average Dice (0.5*Dice_a + 0.5*Dice_v); Average Dice; Average IOU; sensitivity; Sensitivity; Accuracy,Artery/Veins Retinal Vessel Segmentation,Medical; Computer Vision,"Retinal vessel segmentation is the task of segmenting vessels in retina imagery.    <span style=""color:grey; opacity: 0.6"">( Image credit: [LadderNet](https://github.com/juntang-zhuang/LadderNet) )</s..."
Dynamic Link Prediction,8,WN18; DBLP Temporal; Reddit; Taobao (TGN Style); DGraphFin (TGN Style); Enron Emails; HeriGraph; ML25m (TGN Style),N/A,N/A,N/A,N/A,N/A
Human Interaction Recognition,8,SBU / SBU-Refine; NTU RGB+D; EPIC-SOUNDS; H2O; NTU RGB+D 120; NuiSI Dataset; H²O Interaction; UT-Interaction,NTU RGB+D; EPIC-SOUNDS; UT-Interaction; NTU RGB+D 120; UT; BIT; SBU; SBU / SBU-Refine,Accuracy (Cross-Setup); Accuracy (Cross-Subject); Accuracy (Cross-View); Accuracy (Set 2); Accuracy; Accuracy (Set 1); Top-1 accuracy %,Dense contact estimation; Mutual Gaze; One-Shot 3D Action Recognition,Computer Vision,"Human Interaction Recognition (HIR) is a field of study that involves the development of computer algorithms to detect and recognize human interactions in videos, images, or other multimedia content. ..."
Pose Prediction,8,VR Mocap Dataset for Pose/Orientation Prediction; NTU RGB+D; SportsPose; VRMocap: VR Mocap Dataset for Pose Reconstruction; Expi; G3D; InfiniteRep; Drunkard's Dataset,N/A,N/A,N/A,N/A,N/A
geo-localization,8,PDFM Embeddings; ShipSG; GTA-UAV; SpaGBOL; University-1652; PEnG; DenseUAV; CV-Cities,N/A,N/A,N/A,N/A,N/A
Image Matting,8,Agriculture-Vision; Composition-1K; Distinctions-646; withoutbg100 dataset; AM-2K; AIM-500; P3M-10k; PPM-100,Distinctions-646; Composition-1K; AM-2K; AMD; AIM-500; P3M-10k; PPM-100; Adobe Matting,MAD; Grad; Trimap; MSE; Conn; Grad.; Conn.; SAD,Semantic Image Matting,Computer Vision,"**Image Matting** is the process of accurately estimating the foreground object in images and videos. It is a very important technique in image and video editing applications, particularly in film pro..."
Colorectal Polyps Characterization,8,Kvasir-Sessile dataset; Kvasir; PolypGen; UNITOPATHO; Kvasir-Capsule; CRC; Kvasir-SEG; KvasirCapsule-SEG,N/A,N/A,N/A,N/A,N/A
Zero-shot Image Retrieval,8,Flickr30k-CNA; XTD10; ImageNet-R; X-TransferBench; simco-comco; WebLI; QUILT-1M; COCO-CN,N/A,N/A,N/A,N/A,N/A
Surgical phase recognition,8,SICS-155; GraSP; HeiChole Benchmark; GJ; Cholec80; MISAW; MM-OR; MultiBypass140,Cholec80; GraSP; MISAW; HeiChole Benchmark,F1; mAP; Acc,Online surgical phase recognition; Offline surgical phase recognition,Computer Vision,"The first 40 videos are used for training, the last 40 videos are used for testing."
Satellite Image Classification,8,HRPlanesV2; Cross-View Time Dataset (Cross-Camera Split); UV6K; Cross-View Time Dataset; SECO; DRIFT; WorldStrat; NASA Worldview,N/A,N/A,N/A,N/A,N/A
Single Image Dehazing,8,RESIDE; NH-HAZE; SMOKE; D-HAZY; I-HAZE; UIEB; SQUID; RB-Dust,N/A,N/A,N/A,N/A,N/A
3D Object Recognition,8,CY101 Dataset; Corn Seeds Dataset; SHREC; The RBO Dataset of Articulated Objects and Interactions; ModelNet; Washington RGB-D; Cube++; DOORS,N/A,N/A,N/A,N/A,N/A
Intrinsic Image Decomposition,8,IBL-NeRF; ShapeNet Intrinsic Images v2.0 Extended; FutureHouse; ShapeNet Intrinsic Images v1.0; Doc3DShade; MPI Sintel; MID Intrinsics; Hypersim,N/A,N/A,N/A,Computer Vision,"**Intrinsic Image Decomposition** is the process of separating an image into its formation components such as reflectance (albedo) and shading (illumination). Reflectance is the color of the object, i..."
Keyphrase Extraction,8,SemEval-2017 Task-10; EUROPA; Inspec; Keyphrases CS&Math Russian; NUS; KPTimes; KP20k; Krapivin,SemEval-2017 Task-10; Inspec; NUS; KPTimes; KP20k; Krapivin,Recall; F1@10,N/A,Natural Language Processing,"A classic task to extract salient phrases that best summarize a document, which essentially has two stages: candidate generation and keyphrase ranking."
Monocular Visual Odometry,8,ConSLAM; SLAM2REF; TUM monoVO; ConsInv Dataset; EndoSLAM; Bike and Car Odometer Dataset ! Speedometer OCR; VBR; Drunkard's Dataset,N/A,N/A,N/A,N/A,N/A
Ad-hoc video search,8,IACC.3; TRECVID-AVS18 (IACC.3); TRECVID-AVS17 (IACC.3); TRECVID; TRECVID-AVS16 (IACC.3); TRECVID-AVS21 (V3C1); TRECVID-AVS20 (V3C1); TRECVID-AVS19 (V3C1),TRECVID-AVS16 (IACC.3); TRECVID-AVS18 (IACC.3); TRECVID-AVS17 (IACC.3); TRECVID-AVS20 (V3C1); TRECVID-AVS19 (V3C1),infAP,N/A,Computer Vision,"The Ad-hoc search task ended a 3 year cycle from 2016-2018 with a goal to model the end user search use-case, who is searching (using textual sentence queries) for segments of video containing persons..."
Medical Image Registration,8,Full-Spectral Autofluorescence Lifetime Microscopic Images; Niramai Oncho Dataset; BreastDICOM4; OASIS; Learn2Reg; IXI; PPMI; SR-Reg,OASIS+ADIBE+ADHD200+MCIC+PPMI+HABS+HarvardGSP; IXI; SR-Reg; OASIS,Dice (Average); Dice Score; DSC; val dsc,Diffeomorphic Medical Image Registration; BIRL,Medical,"Image registration, also known as image fusion or image matching, is the process of aligning two or more images based on image appearances. **Medical Image Registration** seeks to find an optimal spat..."
Skin Lesion Classification,8,MCSI; MSK; SD-198; ISIC 2019; BCN_20000; ISIC 2020 Challenge Dataset; HAM10000; PAD-UFES-20,N/A,N/A,N/A,N/A,N/A
Brain Tumor Segmentation,8,BraTs Peds 2024; BRISC; FeTS2022; BraTS 2013; BraTS 2014; BraTS-Africa; BraTS 2015; BraTS 2017,N/A,N/A,N/A,N/A,N/A
Entity Alignment,8,DBP15K; UMVM; DBP2.0 zh-en; DBP-5L (Greek); DBP1M FR-EN; EventEA; MMKG; Weibo-Douban,FBDB15k; dbp15k fr-en; DBP2.0 zh-en; DBP1M FR-EN; dbp15k ja-en; FBYG15k; DBP15k zh-en; DICEWS-1K; YAGO-WIKI50K; DBP1M DE-EN,Hits@1; Entity Alignment (Consolidated) F1; Hit@1; dangling entity detection F1,Multi-modal Entity Alignment,Knowledge Base; Natural Language Processing,**Entity Alignment** is the task of finding entities in two knowledge bases that refer to the same real-world object. It plays a vital role in automatically integrating multiple knowledge bases.    No...
Gender Prediction,8,M-VAD Names; UIT-ViNames; BN-AuthProf; Age and Gender; AgeDB; LAGENDA; IMDB-Clean; inaGVAD,N/A,N/A,N/A,N/A,N/A
Image Forensics,8,Celeb-DF; Satire Dataset; CASIA (OSN-transmitted - Whatsapp); DSO (OSN-transmitted - Facebook); CASIA (OSN-transmitted - Facebook); NIST (OSN-transmitted - Facebook); StreetStyle; Columbia (OSN-transmitted - Facebook),N/A,N/A,N/A,Computer Vision,N/A
Colorization,8,QST; MHMD; NIR2RGB VCIP Challange Dataset; SketchyScene; SPair-71k; ImageNet ctest10k; AnimeCeleb; NCD,ImageNet val; ImageNet ctest10k,FID-5K; FID,Color Mismatch Correction; Point-interactive Image Colorization; Line Art Colorization,Computer Vision,"**Colorization** is the process of adding plausible color information to monochrome photographs or videos. Colorization is a highly undetermined problem, requiring mapping a real-valued luminance imag..."
Image Manipulation Localization,8,DIS100k; Casia V1+; CASIA (OSN-transmitted - Whatsapp); DSO (OSN-transmitted - Facebook); COVERAGE; Digital Forensics 2023 dataset - DF2023; CASIA (OSN-transmitted - Weibo); NIST (OSN-transmitted - Facebook),Columbia(Protocol-CAT); COVERAGE(Protocol-CAT); Casia V1+; CocoGlide; COVERAGE; NIST16(Protocol-CAT); CASIAv1(Protoclo-CAT); Columbia; DSO-1,Average Pixel F1(Fixed threshold); Pixel Binary F1,N/A,Computer Vision,"The task of segmenting parts of images or image parts that have been tampered with or manipulated (sometimes also referred to as doctored). This typically encompasses image splicing, copy-move, or ima..."
Product Recommendation,8,Coveo Data Challenge Dataset; WANDS; VISUELLE2.0; LSEC; Logo-2K+; OTTO Recommender Systems Dataset; EXTREME CLASSIFICATION; Exact Street2Shop,Coveo Data Challenge Dataset,F1; MRR,Context Aware Product Recommendation,Miscellaneous,N/A
Breast Cancer Detection,8,BreakHis; CMMD; CBIS-DDSM; CAMELYON16; Breast Lesion Detection in Ultrasound Videos (CVA-Net); BreastClassifications4; BCI; BRACS,N/A,N/A,N/A,N/A,N/A
Dialogue Act Classification,8,Switchboard Dialog Act Corpus; MRDA; Emotional Dialogue Acts; Doc2Dial; Switchboard-1 Corpus; CPED; EMOTyDA; SDN,N/A,N/A,N/A,N/A,N/A
Handwritten Digit Recognition,7,DigiLeTs; Digits; MNIST-MIX; MatriVasha:; Burmese Handwritten Digit Dataset (BHDD); NumtaDB; MNIST,N/A,N/A,N/A,N/A,N/A
Zero-shot Text-to-Image Retrieval,7,ILIAS; Flickr30k; COCO-Facet; XM 3600; FewSOL; COCO (Common Objects in Context); COCO-CN,N/A,N/A,N/A,N/A,N/A
Multi-Person Pose Estimation,7,CrowdPose; OCHuman; PoPArt; COCO-WholeBody; COCO (Common Objects in Context); MPII; PoseTrack,MPII Multi-Person; COCO minival; PoseTrack2017; PoseTrack21; OCHuman; PoseTrack2018; COCO-WholeBody; COCO test-dev; Multi-Person PoseTrack; COCO,AR; AOP; APL; AP75; Validation AP; mAP @0.5:0.95; FPS; keypoint AP; Test AP; AP Hard,Semi-Supervised Human Pose Estimation,Computer Vision,"Multi-person pose estimation is the task of estimating the pose of multiple people in one frame.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Human Pose Estimation with TensorFlow  ](htt..."
Unsupervised Semantic Segmentation with Language-image Pre-training,7,ADE20K; KITTI-STEP; PASCAL VOC 2007; PASCAL VOC; COCO-Stuff; COCO (Common Objects in Context); Cityscapes,N/A,N/A,N/A,N/A,N/A
Open Vocabulary Object Detection,7,MSCOCO; OVAD benchmark; Objects365; COCO (Common Objects in Context); Description Detection Dataset; OVIC Datasets; LVIS,Objects365; LVIS v1.0; MSCOCO; OpenImages-v4,AP novel-LVIS base training; AP novel-Unrestricted open-vocabulary training; AP 0.5; mask AP50,Open Vocabulary Attribute Detection,Computer Vision,Open-vocabulary detection (OVD) aims to generalize beyond the limited number of base classes labeled during the training phase. The goal is to detect novel classes defined by an unbounded  (open) voca...
Audio-Visual Speech Recognition,7,LRS2; CAS-VSR-S101; LRW; How2; CAS-VSR-W1k (LRW-1000); MISP2021; LRS3-TED,LRS3-TED; LRS2; LRW; CAS-VSR-S101,Top-1 Accuracy; Word Error Rate (WER); Test WER,Text to Speech,Speech,Audio-visual speech recognition is the task of transcribing a paired audio and visual stream into text.
Monocular 3D Object Detection,7,Mono3DRefer; Virtual KITTI 2; nuScenes; Virtual KITTI; SUN RGB-D; OPV2V; KITTI,N/A,N/A,N/A,N/A,N/A
Stereo Disparity Estimation,7,L1BSR; Middlebury 2014; GUISS dataset; Spring; SERV-CT; KITTI; VBR,KITTI 2015; Scene Flow; Middlebury 2014,EPE; D1 Error (2px); three pixel error; D1-all; one pixel error,Stereo Matching,Computer Vision,N/A
Medical Code Prediction,7,MIMIC-III; MIMIC-IV ICD-10; MIMIC-IV-ICD9-top50; MIMIC-IV-ICD10-top50; MIMIC-IV-ICD-10-full; MIMIC-IV-ICD9-full; MIMIC-IV ICD-9,MIMIC-III; MIMIC-IV ICD-10; MIMIC-IV-ICD9-top50; MIMIC-IV-ICD10-top50; MIMIC-IV-ICD-10-full; MIMIC-IV-ICD9-full; MIMIC-IV ICD-9,Precision@5; AUC (Macro); Precision@8; F1 Macro; Macro-AUC; F1 Micro; AUC Micro; R-Prec; Precision@15; F1 (macro),N/A,Medical,Context: Prediction of medical codes from clinical notes is both a practical and essential need for every healthcare delivery organization within current medical systems. Automating annotation will sa...
Zero-Shot Video Retrieval,7,ActivityNet; LSMDC; VATEX; DiDeMo; MSR-VTT; MSVD; YouCook2,ActivityNet; LSMDC; VATEX; MSR-VTT-full; DiDeMo; MSR-VTT; MSVD; YouCook2,text-to-video Mean Rank; text-to-video R@10; video-to-text R@1; video-to-text Median Rank; text-to-video Median Rank; text-to-video R@1; video-to-text R@10; text-to-video R@5; video-to-text R@5,N/A,Computer Vision,Zero-shot video retrieval is the task of retrieving relevant videos based on a query (usually in text form) without any prior training on specific examples of those videos. Unlike traditional retrieva...
Text to Video Retrieval,7,ChinaOpen-1k; Sakuga-42M; Kinetics-GEB+; Kinetics; MSVD-Indonesian; MSR-VTT; MVK,MSVD-Indonesian; MSR-VTT; Kinetics-GEB+,mAP; text-to-video R@10; R@1; text-to-video R@50; R@5; text-to-video R@1; R@10; Mean Rank; text-to-video R@5; Median Rank,Partially Relevant Video Retrieval,Computer Vision,She's gone   I can't find her anywhere   I'm looking everywhere for her  Everywhere is dark
6D Pose Estimation using RGB,7,Fraunhofer IPA Bin-Picking; T-LESS; ApolloCar3D; YCB-Video; UW Indoor Scenes (UW-IS) Occluded dataset; LM; Drunkard's Dataset,N/A,N/A,N/A,N/A,N/A
Human Pose Forecasting,7,GTA-IM Dataset; Human3.6M; AMASS; HARPER; Expi; PATS; 3DPW,N/A,N/A,N/A,N/A,N/A
Face Identification,7,MS-Celeb-1M; PetFace; LTFT; DroneSURF; MegaFace; IJB-A; IJB-B,N/A,N/A,N/A,N/A,N/A
Traffic Sign Recognition,7,TopLogo-10; Tsinghua-Tencent 100K; CURE-TSD; GTSRB; CURE-TSR; FlickrLogos-32; BelgaLogos,N/A,N/A,N/A,N/A,N/A
Motion Segmentation,7,EV-IMO; KT3DMoSeg; Hopkins155; MOD++; ApolloScape; HOI4D; KITTI'15 MSplus,Hopkins155; MTPV62; KT3DMoSeg; ApolloScape,Classification Error; Accuracy; Error,N/A,Computer Vision,"**Motion Segmentation** is an essential task in many applications in Computer Vision and Robotics, such as surveillance, action recognition and scene understanding. The classic way to state the proble..."
Facial Expression Recognition,7,FER2013; RAF-DB; MELD; Aff-Wild2; CMU-MOSEI; FER+; AffectNet,FER2013; RAF-DB; Aff-Wild2; MELD; CMU-MOSEI; FER+; AffectNet,Accuracy; Weighted Accuracy; Accuracy (7 emotion); Overall Accuracy,Zero-Shot Facial Expression Recognition; Cross-Domain Facial Expression Recognition,Computer Vision,N/A
3D Shape Modeling,7,PARIS Dataset; PartNet-Mobility; DrivAerNet; Pix3D; SynFoot; CADBench; BlendNet,Pix3D S1; Pix3D S2,mesh AP; box AP; mask AP,N/A,Computer Vision,Image: [Gkioxari et al](https://arxiv.org/pdf/1906.02739v2.pdf)
Video Denoising,7,DAVIS; SEPE 8K; I2-2000FPS; Videezy4K; CRVD; SC_burst; VideoLQ,N/A,N/A,N/A,N/A,N/A
Camouflaged Object Segmentation,7,MoCA-Mask; CAMO; Camouflaged Animal Dataset; R2C7K; COD10K; CAMO++; NC4K,MoCA-Mask; CHAMELEON; CAMO; Camouflaged Animal Dataset; PCOD_1200; COD; NC4K,Weighted F-Measure; S-Measure; weighted F-measure; mDice; MAE; mIoU; S-measure,Camouflaged Object Segmentation with a Single Task-generic Prompt,Computer Vision,"Camouflaged object segmentation (COS) or Camouflaged object detection (COD), which was originally promoted by [T.-N. Le et al.](https://www.sciencedirect.com/science/article/abs/pii/S1077314219300608)..."
Electroencephalogram (EEG),7,CWL EEG/fMRI Dataset; SEED; K-EmoCon; ICLabel; MUTLA; mEBAL; SPaRCNet,　SEED; SEED-IV; HS-SSVEP,Accuracy; Accuracy (5-fold),Eeg Decoding; EEG Denoising; Attention Score Prediction; Semanticity prediction; Noise Level Prediction,Medical; Methodology; Time Series,"**Electroencephalogram (EEG)** is a method of recording brain activity using electrophysiological indexes. When the brain is active, a large number of postsynaptic potentials generated synchronously b..."
Keyword Extraction,7,MAKED; SemEval-2017 Task-10; Inspec; Keyphrases CS&Math Russian; KPTimes; MPQA Opinion Corpus; CSL (Chinese Scientific Literature),SemEval-2017 Task-10; SemEval 2010 Task 8; Inspec,F1 score; Recall@10; Recall @ 10; Precision@10,N/A,Natural Language Processing,Keyword extraction is tasked with the automatic identification of terms that best describe the subject of a document (Source: Wikipedia).
3D Object Classification,7,CY101 Dataset; EUEN17037_Daylight_and_View_Standard_TestDataSet; ModelNet; Remote Flash LiDAR Vehicles Dataset; UR5 Tool Dataset; Cyclone Data; 3RScan,ModelNet40; 3R-Scan; ModelNet10; Remote Flash LiDAR Vehicles Dataset,Top-5 Accuracy; Top-10 Accuracy; Accuracy; mean average precision; Classification Accuracy,Generative 3D Object Classification; Cube Engraving Classification,Computer Vision,3D Object Classification is the task of predicting the class of a 3D object point cloud.  It is a voxel level prediction where each voxel is classified into a category. The popular benchmark for this ...
Camera Relocalization,7,ConSLAM; DeepLoc; SLAM2REF; Niantic Map-free Relocalization Dataset; Cambridge Landmarks; PEnG; University,N/A,N/A,camera absolute pose regression,Computer Vision,"""Camera relocalization, or image-based localization is a fundamental problem in robotics and computer vision. It refers to the process of determining camera pose from the visual scene representation a..."
Lesion Classification,7,Retinal-Lesions; MSK; BCN_20000; ISIC 2017 Task 1; ISIC 2018 Task 1; ISIC 2017 Task 2; HAM10000,N/A,N/A,N/A,N/A,N/A
Text Segmentation,7,CoNLL 2017 Shared Task - Automatically Annotated Raw Texts and Word Embeddings; Wiki5K Hebrew segmentation; CareerCoach 2022; YTSeg; SPMRL Hebrew segmentation data; UrduDoc; CoNLL,N/A,N/A,N/A,N/A,N/A
Natural Language Visual Grounding,7,Spot-the-diff; ImageCoDe; MAD; Google Refexp; ScreenSpot; ReferIt3D; METU-VIREF Dataset,ScreenSpot,Accuracy (%),N/A,Reasoning,N/A
Text Matching,7,Snopes; Composed Quora; DuLeMon; COVID-19 Twitter Chatter Dataset; PSM; MedICaT; PolitiFact,N/A,N/A,N/A,Natural Language Processing,Matching a target text to a source text based on their meaning.
Traffic Sign Detection,7,CCTSDB2021; CVL Traffic Signs Dataset; CURE-TSD; CURE-TSR; aiMotive 3D Traffic Light and Traffic Sign Dataset; CCTSDB-AUG; TT100K,CCTSDB2021; CCTSDB-AUG; GTSDB; TT100K,avg-mAP (0.1-0.5); mAP; Averaged Precision; mAP@0.5,N/A,Computer Vision,N/A
Gesture Generation,7,TED Gesture Dataset; Talking With Hands 16.2M; DVS128 Gesture; BiGe; BEAT; BEAT2; LLMafia,N/A,N/A,N/A,N/A,N/A
Hyperspectral Image Segmentation,7,Tecnalia WEEE HYPERSPECTRAL DATASET; HSIRS; Hyper Drive; HSI-Drive v2.0; Hyperspectral City; HyKo2-VIS; LIB-HSI,N/A,N/A,Hyperspectral,Computer Vision,N/A
Person Recognition,7,CN-Celeb-AV; PRW; MotionID: IMU all motions part2; MotionID: IMU all motions part3; MotionID: IMU all motions part1; MotionID: IMU specific motion; iCartoonFace,N/A,N/A,N/A,Computer Vision,N/A
3D Human Shape Estimation,7,MoVi; BEDLAM; HBW; HUMAN4D; SSP-3D; UNIPD-BPE; AGORA,N/A,N/A,N/A,N/A,N/A
Generalizable Novel View Synthesis,7,RealEstate10K; NERDS 360; ACID; Spaces; LLFF; Shiny dataset; ZJU-MoCap,N/A,N/A,N/A,N/A,N/A
Neural Rendering,7,SyntheticFur; ThermoScenes; FutureHouse; SB20; MatrixCity; BUP20; DONeRF: Evaluation Dataset,N/A,N/A,Neural Radiance Caching,Computer Vision,"Given a representation of a 3D scene of some kind (point cloud, mesh, voxels, etc.), the task is to create an algorithm that can produce photorealistic renderings of this scene from an arbitrary viewp..."
Gender Bias Detection,7,international faces; BUG; IMDB-Clean; CI-MNIST; inaGVAD; Filipino CrowS-Pairs and Filipino WinoQueer; Grep-BiasIR,N/A,N/A,N/A,Miscellaneous; Natural Language Processing,N/A
Table Recognition,7,PubTables-1M; CISOL; PubTabNet; WTW; FinTabNet; WikiTableSet; TNCR Dataset,Table Recognition Challenge test; ICDAR2013 table structure recognition; PubTabNet; WTW; Table Recognition Challenge mini-test,F1; TEDS (all samples); TEDS-Struct; F-Measure; TEDS (complex samples); TEDS (simple samples),N/A,Computer Vision,"Table recognition refers to the process of automatically identifying and extracting tabular structures from unstructured data sources such as text documents, images, or scanned documents. The goal of ..."
Fault Detection,7,Centrifugal Pump Fault Detection; Sound-based drone fault classification using multitask learning; IMS Bearing Dataset; Paderbone University Bearing Fault Benckmark; PRONTO; PRONOSTIA Bearing Dataset; Unbalance Classification Using Vibration Data,N/A,N/A,N/A,Miscellaneous,N/A
Vulnerability Detection,7,RealVul; CVEfixes; VulScribeR; Vulnerability Java Dataset; IoTvulCode; Vulnerable Verified Smart Contracts; CASTLE Benchmark,Vulnerability Java Dataset; VulScribeR,F1; F1 Score; AUC,N/A,Miscellaneous,Vulnerability detection plays a crucial role in safeguarding against these threats by identifying weaknesses and potential entry points that malicious actors could exploit. Through advanced scanning t...
Infrared And Visible Image Fusion,7,Correlated Corrupted Dataset; AWMM-100k; ThermoScenes; NIR2RGB VCIP Challange Dataset; Uncorrelated Corrupted Dataset; LLVIP; InfraParis,N/A,N/A,N/A,Computer Vision,Image fusion with paired infrared and visible images
Term Extraction,7,RuTermEval (Track 2); AWARE; IEE; RuTermEval (Track 1); PET: A new Dataset for Process Extraction from Natural Language Text; PET; RuTermEval (Track 3),SemEval 2014 Task 4 Laptop; AWARE,F1-Score,Nested Term Extraction; Nested Term Recognition,Natural Language Processing,"Term Extraction, or Automated Term Extraction (ATE), is about extraction domain-specific terms from natural language text.   For example, the sentence “We meta-analyzed mortality using random-effect m..."
MRI segmentation,7,BraTs Peds 2024; BRISC; ABIDE; SMILE-UHURA; CUTS; SKM-TEA; UPenn-GBM,N/A,N/A,Brain Tumor Classification,Computer Vision,N/A
Video Restoration,7,SEPE 8K; VRDS; Videezy4K; BS-RSC; Vident-lab; I2-2000FPS; TAPE,UVG; SEPE 8K,Average PSNR (dB),Analog Video Restoration,Computer Vision,N/A
2D Cyclist Detection,7,"How  to fix  QuickBooks Error 30159 – Causes & Fixes; Why is Allegiant phone number busy?; CIMAT-Cyclist; How to fix QuickBooks Error 6123, 0 – Causes & Solutions; [[FAQs~charge]]How much does Expedia charge for cancellation?; [Travel@Guide® ]What is the 24 hour rule for KLM?; CTCyclistDetectionDataset",N/A,N/A,N/A,N/A,N/A
Text-based Image Editing,7,GEdit-Bench-EN; NHR-Edit; ManiCups; OIR-Bench; ImgEdit-Data; PIE-Bench; ImgEdit,N/A,N/A,N/A,N/A,N/A
Image to Video Generation,7,ChronoMagic-Pro; DropletVideo-10M; ConsisID-preview-Data; OpenS2V-Eval; ChronoMagic-ProH; ChronoMagic; OpenS2V-5M,N/A,N/A,Open-Domain Subject-to-Video,Computer Vision,**Image to Video Generation** refers to the task of generating a sequence of video frames based on a single still image or a set of still images. The goal is to produce a video that is coherent and co...
Unsupervised Image Classification,6,STL-10; ObjectNet; CIFAR-10; ImageNet; SVHN; MNIST,N/A,N/A,N/A,N/A,N/A
Structured Prediction,6,WIKIOG; CFQ; WBM; ListOps; MNIST; SciREX,MNIST,Negative CLL,N/A,Methodology,"**Structured Prediction** is an area of machine learning focusing on representations of spaces with combinatorial structure, and algorithms for inference and parameter estimation over these structures..."
Image Deblurring,6,HIDE; CelebA; GoPro; ImageNet; Leishmania parasite dataset; Real Blur Dataset,HIDE; CelebA; RealBlur-J; HIDE (trained on GOPRO); GoPro; ImageNet; RealBlur-R(trained on GoPro); RealBlur-R; Real-world Dataset,LPIPS; Params (M); PSNR; FID; SSIM,Low-light Image Deblurring and Enhancement,Computer Vision,file:///var/mobile/Library/SMS/Attachments/cb/11/8A26E2FC-7464-4692-8F2A-3F1B3E23BCD5/IMG_4630.jpeg
Lightweight Face Recognition,6,IJB-C; LFW; BTS3.1; CPLFW; IJB-B; CALFW,N/A,N/A,N/A,N/A,N/A
3D Part Segmentation,6,Teeth3DS+; TomoSAM; ShapeNet; IntrA; LiDAR-MOS; Workshop Tools Dataset,N/A,N/A,N/A,N/A,N/A
Real-time Instance Segmentation,6,MEIS; MSCOCO; Multi30K; COCO (Common Objects in Context); KITTI; Cityscapes,N/A,N/A,N/A,N/A,N/A
Zero-Shot Object Detection,6,PASCAL VOC 2007; MSCOCO; RF100; ELEVATER; COCO (Common Objects in Context); LVIS,N/A,N/A,N/A,N/A,N/A
Open World Object Detection,6,COCO-OOD; PASCAL VOC 2007; COCO-Mix; UVO; COCO (Common Objects in Context); SFCHD,N/A,N/A,N/A,N/A,N/A
Talking Face Generation,6,GLips; VOCASET; CREMA-D; LRW; PASCAL VOC; AnimeCeleb,CREMA-D; LRW,LMD; FID; EmoAcc; SSIM; LSE-C,Constrained Lip-synchronization; Face  Dubbing,Computer Vision,"Talking face generation aims to synthesize a sequence of face images that correspond to given speech semantics      <span style=""color:grey; opacity: 0.6"">( Image credit: [Talking Face Generation by A..."
Stereo Depth Estimation,6,"Helvipad; GUISS dataset; Multi-Spectral Stereo Dataset  (RGB, NIR, thermal images, LiDAR, GPS/IMU); Spring; KITTI; VBR",KITTI 2015; KITTI2015; sceneflow; Spring; KITTI2012, three pixel error; Average End-Point Error; EPE; D1-all All; 1px total; three pixel error; D1-all Noc,Omnnidirectional Stereo Depth Estimation,Computer Vision,N/A
Egocentric Pose Estimation,6,GlobalEgoMocap Test Dataset; UnrealEgo; xR-EgoPose; SceneEgo; KITTI; EgoPW-Scene,N/A,N/A,N/A,N/A,N/A
Depth Prediction,6,"Matterport3D; Stanford-ORB; Coastal Inundation Maps with Floodwater Depth Values; Multi-Spectral Stereo Dataset  (RGB, NIR, thermal images, LiDAR, GPS/IMU); MagicBathyNet; KITTI",N/A,N/A,N/A,N/A,N/A
Zero-Shot Action Recognition,6,ActivityNet; THUMOS14; Charades; Kinetics; HMDB51; UCF101,ActivityNet; THUMOS' 14; Charades; Kinetics; HMDB51; UCF101; Olympics,Top-5 Accuracy; mAP; Top-5 accuracy; Top-1 Accuracy; Accuracy,N/A,Computer Vision,N/A
Few Shot Action Recognition,6,Kinetics; Kinetics-100; MOMA-LRG; Something-Something-100; HMDB51; UCF101,N/A,N/A,N/A,N/A,N/A
Scene Graph Detection,6,Home Action Genome; VRD; ImageCLEF-DA; MOMA-LRG; Haystack; Visual Genome,N/A,N/A,N/A,N/A,N/A
Open Vocabulary Semantic Segmentation,6,ISPRS Potsdam; ADE20K; iSAID; PASCAL VOC; COCO-Stuff; Cityscapes,ISPRS Potsdam; 	ADE20K-150; Cityscape-171; iSAID; ADE20K-847; SIOR; PASCAL Context-459; ADE20K-150; SOTA; PASCAL Context-59,mIoU; mIoU-; hIoU; HIoU,Zero-Guidance Segmentation,Computer Vision,Open-vocabulary semantic segmentation models aim to accurately assign a semantic label to each pixel in an image from a set of arbitrary open-vocabulary texts.
Face Sketch Synthesis,6,SKSF-A; Multi-Modal CelebA-HQ; CUHK03; FS2K; CUFSF; CUFS,N/A,N/A,N/A,N/A,N/A
Medical Named Entity Recognition,6,2010 i2b2/VA; BC2GM; Species-800; JNLPBA; RadGraph; The QUAERO French Medical Corpus,N/A,N/A,N/A,N/A,N/A
Moment Retrieval,6,Goal; MAD; QVHighlights; HiREST; LongVALE; Charades-STA,QVHighlights; Charades-STA,mAP@0.75; mAP; R@1 IoU=0.5; R@1 IoU=0.7; mAP@0.5; R@1 IoU=0.3; R@5 IoU=0.7; mIoU; R@5 IoU=0.5,Zero-shot Moment Retrieval,Computer Vision,"Moment retrieval can de defined as the task of ""localizing moments in a video given a user query"".    Description from: [QVHIGHLIGHTS: Detecting Moments and Highlights in Videos via Natural Language Q..."
Grammatical Error Detection,6,FCGEC; FCE; The Write & Improve Corpus 2024; Cleaned_Lang8; JFLEG; CoNLL,N/A,N/A,N/A,N/A,N/A
Facial Emotion Recognition,6,HEADSET; JAFFE; Thermal Face Database; MH-FED; RAVDESS; CANDOR Corpus,N/A,N/A,N/A,N/A,N/A
Multimodal Activity Recognition,6,MSRDailyActivity3D; UCSD Ped2; UT-Kinect; Home Action Genome; UTD-MHAD; MMAct,N/A,N/A,N/A,N/A,N/A
Aesthetics Quality Assessment,6,Aesthetic Visual Analysis; RPCD; Image Aesthetics dataset; CADB; AVA; AADB,N/A,N/A,N/A,N/A,N/A
Spatio-Temporal Action Localization,6,LIRIS human activities dataset; Kinetics; MultiSports; AVA; VidHOI; JRDB-Act,N/A,N/A,N/A,N/A,N/A
3D Facial Landmark Localization,6,Urban Hyperspectral Image; DAD-3DHeads; AFLW2000-3D; FER2013 Blendshapes; H3WB; 3DFAW,N/A,N/A,N/A,N/A,N/A
Chinese Named Entity Recognition,6,Resume NER; MSRA CN NER; OntoNotes 5.0; Weibo NER; CLUENER2020; OntoNotes 4.0,N/A,N/A,N/A,N/A,N/A
Weakly-Supervised Named Entity Recognition,6,ShARe/CLEF 2014: Task 2 Disorders; OntoNotes 5.0; BC5CDR; CoNLL 2003; CoNLL; CoNLL++,N/A,N/A,N/A,N/A,N/A
Body Detection,6,Clipart1k; DermSynth3D; Manga109; DCM; Watercolor2k; Comic2k,N/A,N/A,N/A,N/A,N/A
Speaker Recognition,6,CN-CELEB; MAVS; FKD; ASR-RAMC-BIGCCSC: A CHINESE CONVERSATIONAL SPEECH CORPUS; VGG-Sound; VoxCeleb1,VoxCeleb1,EER,N/A,Speech,"**Speaker Recognition** is the process of identifying or confirming the identity of a person given his speech segments.   <span class=""description-source"">Source: [Margin Matters: Towards More Discrim..."
Medical Image Generation,6,ChestX-ray14; Chest X-Ray Images (Pneumonia); Kvasir-VQA; Leishmania parasite dataset; ACDC; BCI,ChestX-ray14; Chest X-Ray Images (Pneumonia); SLIVER07; ACDC; ChestXray14 1024x1024,Frechet Inception Distance; FID,Radiologist Binary Classification,Medical,"Medical image generation is the task of synthesising new medical images.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Towards Adversarial Retinal Image Synthesis](https://arxiv.org/pdf/1..."
3D Multi-Object Tracking,6,3D-ZeF; nuScenes LiDAR only; nuScenes; MOTFront; Waymo Open Dataset; CVB,nuscenes Camera-Radar; nuScenes LiDAR only; nuScenes; nuScenes Camera Only; Waymo Open Dataset; Waymo Open Dataset: Vehicle (Online Methods),AMOTA; MOTA; MOTA/L2; FP/L2; MOTA/L1; Recall,N/A,Computer Vision,Image: [Weng et al](https://arxiv.org/pdf/1907.03961v4.pdf)
Sleep Stage Detection,6,Montreal Archive of Sleep Studies; PhysioNet Challenge 2018; SHHS; MaSS; Sleep-EDF; ISRUC-Sleep,MASS SS2; SHHS (single-channel); Sleep-EDFx (single-channel); Sleep-EDF (single-channel); Montreal Archive of Sleep Studies; MASS (single-channel); PhysioNet Challenge 2018; MASS SS3; SHHS; DODH,Macro-averaged Accuracy; Cohen's kappa; Cohen's Kappa; Accuracy; Macro-F1; Kappa; AUROC,Sleep Staging,Medical,Human Sleep Staging into W-N1-N2-N3-REM classes from multiple or single polysomnography signals
Unified Image Restoration,6,RESIDE; Synthetic Rain Datasets; AWMM-100k; BSD; GoPro; LOL,RESIDE; LOL; GoPro; BSD68 sigma25; Rain100L,Average PSNR (dB),Blind All-in-One Image Restoration,Computer Vision,Using a single model to restore inputs with different degradation types.
Image-Based Localization,6,NAVER LABS Localization Datasets; Cross-View Time Dataset (Cross-Camera Split); Cross-View Time Dataset; SpaGBOL; University-1652; VIGOR,N/A,N/A,N/A,N/A,N/A
3D Point Cloud Classification,6,Sydney Urban Objects; Teeth3DS+; ModelNet; ModelNet40-C; IntrA; ScanObjectNN,ModelNet40; Sydney Urban Objects; ModelNet40-C; IntrA; ScanObjectNN,Overall Accuracy; F1; F1 score (5-fold); OBJ-BG (OA); Mean Accuracy; OBJ-ONLY (OA); Number of params; FLOPs; Error Rate; Mean class accuracy,Zero-Shot Transfer 3D Point Cloud Classification; 3D Object Classification; Supervised Only 3D Point Cloud Classification; Few-Shot 3D Point Cloud Classification,Computer Vision,N/A
Visual Speech Recognition,6,LRS2; GLips; Watch Your Mouth: Point Clouds based Speech Recognition Dataset; CAS-VSR-W1k (LRW-1000); AV Digits Database; LRS3-TED,LRS2; LRS3-TED,Word Error Rate (WER),Lip to Speech Synthesis,Music; Speech; Computer Vision,N/A
Line Segment Detection,6,ICDAR 2021; York Urban Line Segment Database; KAIST Urban; Wireframe; WaterScenes; BN-HTRd,York Urban Dataset; wireframe dataset,sAP15; sAP10; sAP5; FH,N/A,Computer Vision,N/A
3D Face Animation,6,VOCASET; FEAFA+; PASCAL VOC; Human Optical Flow; Biwi 3D Audiovisual Corpus of Affective Communication - B3D(AC)^2; BEAT2,VOCASET; Biwi 3D Audiovisual Corpus of Affective Communication - B3D(AC)^2; BEAT2,FDD; Lip Vertex Error; MSE,Video Super-Resolution,Computer Vision; Playing Games,Image: [Cudeiro et al](https://arxiv.org/pdf/1905.03079v1.pdf)
Dictionary Learning,6,SALSA; Partial-iLIDS; Extended Yale B; FMD; Partial-REID; LoDoPaB-CT,N/A,N/A,N/A,Methodology,"**Dictionary Learning** is an important problem in multiple areas, ranging from computational neuroscience, machine learning, to computer vision and image processing. The general goal is to find a goo..."
Robust Face Recognition,6,FacesInThings; DroneSURF; MALF; UFDD; MeGlass; OCFR-LFW,N/A,N/A,N/A,N/A,N/A
Stereo Matching Hand,6,PhotoTour; Middlebury 2001; Middlebury 2005; ICubWorld; CATS; Middlebury 2006,N/A,N/A,N/A,Computer Vision,N/A
Zero-Shot Image Classification,6,Corn Seeds Dataset; TaxaBench-8k; MineralImage5k; Country211; QUILT-1M; OVIC Datasets,ICinW; ODinW; Country211,Top-1 accuracy; Average Score,Open Vocabulary Image Classification,Computer Vision,Zero-shot image classification is a technique in computer vision where a model can classify images into categories that were not present during training. This is achieved by leveraging semantic inform...
Scene Change Detection,6,PCD; ChangeVPR; ChangeSim; Unaligned-VL-CMU-CD (neighbor distance 2); MSU Shot Boundary Detection Benchmark; 3RScan,PCD; VL-CMU-CD; ChangeVPR; Semantic Scene Understanding Challenge (passive actuation & ground-truth localisation); ChangeSim; Unaligned-VL-CMU-CD (neighbor distance 2),macro F1; OMQ; avg_state_quality; F1-score; avg_fp_quality; avg_pairwise; Category mIoU; avg_spatial; F1 score; avg_label,N/A,Computer Vision,Scene change detection (SCD) refers to the task  of localizing changes and identifying change-categories given two scenes. A scene can be either an RGB (+D) image or a 3D reconstruction (point cloud)....
Video Enhancement,6,SEPE 8K; MFQE v2; Vident-lab; I2-2000FPS; BVI-DVC; LDV,MFQE v2,Incremental PSNR; Parameters(M),N/A,Computer Vision,N/A
Gaze Prediction,6,OST; OpenEDS2020; EgoMon; GOO; EyeInfo; EGO-CH-Gaze,N/A,N/A,N/A,Computer Vision,N/A
Emotion Recognition in Context,6,BoLD; KD-EmoR; Emotional Dialogue Acts; CAER; EMOTIC; CAER-Dynamic,N/A,N/A,N/A,N/A,N/A
Composed Image Retrieval (CoIR),6,WebVid-CoVR; CIRCO; CIRR; LaSCo; Fashion IQ; PatternCom,Fashion IQ; CIRR,R@1; (Recall@10+Recall@50)/2; R@5; R@50; R@10,Zero-Shot Composed Image Retrieval (ZS-CIR),Computer Vision,"**Composed Image Retrieval (CoIR)** is the task involves retrieving images from a large database based on a query composed of multiple elements, such as text, images, and sketches. The goal is to deve..."
CAD Reconstruction,6,Text2CAD; CC3D; Fusion 360 Gallery; DeepCAD; CADBench; BlendNet,N/A,N/A,N/A,N/A,N/A
Autonomous Navigation,6,RELLIS-3D; LDDRS; JRDB; IN2LAAMA; MIDGARD; Hyper Drive,N/A,N/A,Autonomous Web Navigation; Autonomous Flight (Dense Forest); Sequential Place Recognition,Reasoning; Robots; Computer Vision,"Autonomous navigation is the task of autonomously navigating a vehicle or robot to or around a location without human guidance.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Approximate L..."
Single Image Deraining,6,Synthetic Rain Datasets; RainCityscapes; AWMM-100k; Real Rain Dataset; Raindrop; HRI,N/A,N/A,N/A,N/A,N/A
Abstractive Dialogue Summarization,6,ConvoSumm; ELITR Minuting Corpus; SAMSum; SummZoo; ICSI Meeting Corpus; AMI Meeting Corpus,N/A,N/A,N/A,N/A,N/A
The Semantic Segmentation Of Remote Sensing Imagery,6,SAMRS; UV6K; DRIFT; GID; Five-Billion-Pixels; MSAW,UV6K; MSAW,F1 score; IoU (%),Lake Ice Monitoring,Computer Vision,N/A
Trajectory Modeling,6,rounD Dataset; NBA SportVU; highD Dataset; uniD Dataset; exiD Dataset; inD Dataset,NBA SportVU,1x1 NLL,N/A,Time Series,The equivalent of language modeling but for trajectories.
Video Panoptic Segmentation,6,KITTI-STEP; MM-OR; LaRS; Cityscapes-VPS; 4D-OR; VIPSeg,N/A,N/A,N/A,N/A,N/A
Video-Text Retrieval,6,VTC; SYMON; WebVid; wonderbread; Test-of-Time; Youku-mPLUG,N/A,N/A,N/A,N/A,N/A
Multiple People Tracking,6,SOMPT22; LTFT; 2024 AI City Challenge; PersonPath22; MMPTRACK; Crowd in a rally | Crowd Counting | Crowd Human,N/A,N/A,N/A,Computer Vision,N/A
Object SLAM,6,ConSLAM; SLAM2REF; TorWIC; ConsInv Dataset; YCB-Slide; CODD,N/A,N/A,N/A,N/A,N/A
Medical Object Detection,6,DeepLesion; GRAZPEDWRI-DX; RF100; ENSeg; SKM-TEA; MP-IDB,N/A,N/A,N/A,N/A,N/A
Attribute Value Extraction,6,AE-110k; Caselaw4; MAVE - Attribute: Black Tea Variety; WDC-PAVE; MAVE; OA-Mine - annotations,AE-110k; WDC-PAVE; MAVE; OA-Mine - annotations,F1-score; F1-Score,N/A,Natural Language Processing,**Attribute Value Extraction** is the task of extracting values for a given set of attributes of interest from free text input. Attribute value extraction is for example applied in the context of e-co...
Within-Session Motor Imagery (all classes),6,Zhou2016 MOABB; BNCI2014-001 MOABB; Weibo2014 MOABB; Schirrmeister2017 MOABB; AlexandreMotorImagery MOABB; PhysionetMotorImagery MOABB,N/A,N/A,N/A,N/A,N/A
Automatic Phoneme Recognition,6,VibraVox (throat microphone); VibraVox (temple vibration pickup); VibraVox (headset microphone); VibraVox (soft in-ear microphone); VibraVox (forehead accelerometer); VibraVox (rigid in-ear microphone),N/A,N/A,N/A,N/A,N/A
Unsupervised Anomaly Detection with Specified Settings -- 20% anomaly,5,STL-10; Fashion-MNIST; CIFAR-10; Cats and Dogs; MNIST,STL-10; Fashion-MNIST; Cats and Dogs; cifar10; MNIST,AUC-ROC,N/A,Computer Vision,N/A
Unsupervised Anomaly Detection with Specified Settings -- 1% anomaly,5,STL-10; Fashion-MNIST; CIFAR-10; Cats and Dogs; MNIST,STL-10; Fashion-MNIST; CIFAR-10; Cats and Dogs; MNIST,AUC-ROC,N/A,Computer Vision,N/A
Unsupervised Anomaly Detection with Specified Settings -- 0.1% anomaly,5,STL-10; Fashion-MNIST; CIFAR-10; Cats and Dogs; MNIST,STL-10; Fashion-MNIST; CIFAR-10; Cats and Dogs; MNIST,AUC-ROC,N/A,Computer Vision,N/A
Unsupervised Anomaly Detection with Specified Settings -- 10% anomaly,5,STL-10; Fashion-MNIST; CIFAR-10; Cats and Dogs; MNIST,STL-10; Fashion-MNIST; CIFAR-10; Cats and Dogs; MNIST,AUC-ROC,N/A,Computer Vision,N/A
Blind Face Restoration,5,CelebA; LFW; Wider-Test-200; WIDER; CelebA-HQ,CelebA-Test; WIDER; LFW; CelebA-HQ,LPIPS; PSNR; IDS; FID; Deg.; SSIM; NIQE,N/A,Computer Vision,"Blind face restoration aims at recovering high-quality faces from the low-quality counterparts suffering from unknown degradation, such as low-resolution, noise, blur, compression artifacts, etc. When..."
3D Face Modelling,5,THVD; FaMoS; Voxceleb-3D; LFW; Human Optical Flow,LFW; Voxceleb-3D,ARE-MR; ARE-CR; ARE-FR; 1-of-100 Accuracy; Mean ARE; ARE-ER,Facial Recognition and Modelling; Continuous Control,Medical; Computer Vision; Playing Games,N/A
Zero-Shot Cross-Modal Retrieval,5,Earth on Canvas; Flickr30k; IMPACT Patent; COCO (Common Objects in Context); ImageNet_CN,N/A,N/A,N/A,N/A,N/A
JPEG Artifact Correction,5,LIVE (Public-Domain Subjective Image Quality Database); DIV2K; Classic5; ICB; BSDS500,N/A,N/A,N/A,N/A,N/A
Stereo Image Super-Resolution,5,L1BSR; Middlebury; KITTI; VBR; Flickr1024,N/A,N/A,N/A,N/A,N/A
Unsupervised Panoptic Segmentation,5,MUSES: MUlti-SEnsor Semantic perception dataset; Waymo Open Dataset; BDD100K; KITTI; Cityscapes,MUSES: MUlti-SEnsor Semantic perception dataset; Waymo Open Dataset; COCO val2017; KITTI; BDD100K val; Cityscapes,RQ; SQ; PQ,Unsupervised Zero-Shot Panoptic Segmentation,Computer Vision,**Unsupervised Panoptic Segmentation** aims to partition an image into semantically meaningful regions and distinct object instances without training using any manually annotated images.  <span style=...
Unsupervised Monocular Depth Estimation,5,WeatherKITTI; CamlessVideosFromTheWild; KITTI-C; KITTI; Cityscapes,N/A,N/A,N/A,N/A,N/A
Self-Supervised Action Recognition,5,Kinetics; Kinetics 400; Kinetics-600; HMDB51; UCF101,N/A,N/A,N/A,N/A,N/A
Visual Relationship Detection,5,Visual Relationship Detection Dataset; VRD; TexRel; Visual Genome; Open Images V7,VRD Phrase Detection; VRD; VRD Relationship Detection; VRD Predicate Detection; Visual Genome,mR@50; mR@100; R@50; R@50 k=1; R@100,Video Visual Relation Detection; Human-Object Relationship Detection,Computer Vision,Visual relationship detection (VRD) is one newly developed computer vision task aiming to recognize relations or interactions between objects in an image. It is a further learning task after object re...
Sketch-to-Image Translation,5,SKSF-A; Scribble; SketchyCOCO; FS2K; COCO-Stuff,COCO-Stuff; Scribble; SketchyCOCO,FID-C; Accuracy; FID; Human (%),N/A,Computer Vision,N/A
Pose Transfer,5,ADE20K; DeepFashion; CelebAMask-HQ; Market-1501; iDesigner,N/A,N/A,N/A,N/A,N/A
Weakly Supervised Action Localization,5,GTEA; FineAction; ActivityNet; THUMOS14; BEOID,GTEA; FineAction; THUMOS14; THUMOS' 14; THUMOS 2014; ActivityNet-1.2; THUMOS’14; ActivityNet-1.3; BEOID,avg-mAP (0.3-0.7); mAP IOU@0.5; mAP@0.5:0.95; mAP; mAP@0.5; mAP IOU@0.95; mAP@0.1:0.5; avg-mAP (0.1-0.5); mAP@0.1:0.7; avg-mAP (0.1:0.7),N/A,Computer Vision,"In this task, the training data consists of videos with a list of activities in them without any temporal boundary annotations. However, while testing, given a video, the algorithm should recognize th..."
Oriented Object Detection,5,DOTA; EVD4UAV; DOTA 2.0; SODA-A; TimberVision,N/A,N/A,N/A,N/A,N/A
Aspect Extraction,5,YASO; Casino Reviews; SemEval-2014 Task-4; ROAST; Yelp,SemEval 2016 Task 5 Sub Task 1 Slot 2; SemEval 2015 Task 12; YASO - YELP;  SemEval 2015 Task 12; SemEval 2014 Task 4 Sub Task 1; SemEval-2014 Task-4,F1; Restaurant (F1); Mean F1 (Laptop + Restaurant); Laptop (F1),Latent Aspect Detection; Hidden Aspect Detection,Natural Language Processing,"Aspect extraction is the task of identifying and extracting terms relevant for opinion mining and sentiment analysis, for example terms for product attributes or features."
Sports Ball Detection and Tracking,5,Badminton; Basketball; Soccer; Tennis; Volleyball,N/A,N/A,N/A,N/A,N/A
Symmetry Detection,5,Symmetric Solids; Symbrain; AvaSym; NYU Symmetry Database; YCB-Video,YCB-Video,PR AUC,N/A,Computer Vision,N/A
Interactive Video Object Segmentation,5,PUMaVOS; DAVIS; MOSE; BL30K; DAVIS 2017,N/A,N/A,N/A,N/A,N/A
Monocular 3D Human Pose Estimation,5,Human3.6M; 3DOH50K; HBW; H3WB; AGORA,N/A,N/A,N/A,N/A,N/A
2D Panoptic Segmentation,5,depression interview dataset; [[Easy~refund]]How can i get a refund from expedia; ScanNet; MM-OR; 4D-OR,MM-OR; 4D-OR; ScanNetV2,VPQ; PQ,Unsupervised Panoptic Segmentation,Computer Vision,N/A
Zero-shot 3D Point Cloud Classification,5,ModelNet; OmniObject3D; ScanNet; ModelNet40 (Pretrained on ShapeNet); ScanObjectNN,N/A,N/A,N/A,N/A,N/A
Facial Inpainting,5,DREAMING Inpainting Dataset; MFSD; VGGFace2; FFHQ; CASIA-WebFace,N/A,N/A,N/A,N/A,N/A
Optic Disc Segmentation,5,ADAM; REFUGE Challenge; MESSIDOR; SMDG; STARE,N/A,N/A,N/A,N/A,N/A
Aspect Category Detection,5,AWARE; FABSA; Casino Reviews; SemEval-2014 Task-4; ROAST,AWARE; SemEval 2014 Task 4 Subtask 3; Citysearch; SemEval-2014 Task-4,Hit@5; Precision; F1-score; Average Recall; F-measure (%); NDCG; F1 score; Recall; MRR,N/A,Natural Language Processing,Aspect category detection (ACD) in sentiment analysis aims to identify the aspect categories mentioned in a sentence.
Grasp Contact Prediction,5,ContactDB; GAS; ContactPose; A Billion Ways to Grasp; GRAB,N/A,N/A,N/A,N/A,N/A
Natural Language Moment Retrieval,5,MAD; DiDeMo; ActivityNet Captions; TACoS Multi-Level Corpus; LongVALE,N/A,N/A,N/A,N/A,N/A
3D Multi-Person Pose Estimation,5,Store dataset; Panoptic; Campus & Shelf; MuPoTS-3D; AGORA,Panoptic; Shelf; MuPoTS-3D; AGORA; Campus,PCP3D; B-MVE; MPJPE; B-NMVE; 3DPCK; Mean mAP; B-NMJE; B-MPJPE; Average MPJPE (mm),3D Multi-Person Mesh Recovery; 3D Multi-Person Pose Estimation (root-relative); 3D Multi-Person Pose Estimation (absolute),Computer Vision,"This task aims to solve root-relative 3D multi-person pose estimation. No human bounding box and root joint coordinate groundtruth are used in testing time.    <span style=""color:grey; opacity: 0.6"">(..."
Face Presentation Attack Detection,5,WMCA; CASIA-SURF; OULU-NPU; SWAX; Replay-Mobile,N/A,N/A,N/A,N/A,N/A
Joint Demosaicing and Denoising,5,McMaster; Urban100; Videezy4K; BSD100; REDS,N/A,N/A,N/A,N/A,N/A
Motion Planning,5,HouseExpo; PushWorld; Motion Policy Networks; nuScenes; ThreeDWorld Transport Challenge,nuScenes,Collision; L2,N/A,Robots,"<span style=""color:grey; opacity: 0.6"">( Image credit: [Motion Planning Among Dynamic, Decision-Making Agents with Deep Reinforcement Learning](https://arxiv.org/pdf/1805.01956v1.pdf) )</span>"
3D Semantic Scene Completion,5,SSCBench; NYUv2; PRO-teXt; KITTI-360; SemanticKITTI,KITTI-360; NYUv2; SemanticKITTI; PRO-teXt,F1; mIoU; CMD; CD,3D Semantic Scene Completion from a single RGB image,Computer Vision,"This task was introduced in ""Semantic Scene Completion from a Single Depth Image"" (https://arxiv.org/abs/1611.08974) at CVPR 2017 . The target is to infer the dense 3D voxelized semantic scene from an..."
Zeroshot Video Question Answer,5,MSRVTT-QA; CausalChaos!; TGIF-QA; MSVD-QA; VCG+112K,N/A,N/A,N/A,N/A,N/A
Depression Detection,5,Well-being Dataset; Perla Dataset; SMHD; eRisk 2017; Distress Analysis Interview Corpus/Wizard-of-Oz set (DAIC-WOZ),N/A,N/A,N/A,N/A,N/A
Fine-Grained Visual Categorization,5,FoodX-251; VegFru; L-Bird; MTL-AQA; FeathersV1,N/A,N/A,N/A,N/A,N/A
RGB-D Salient Object Detection,5,ReDWeb-S; SIP; NLPR; LFSD; NJU2K,N/A,N/A,N/A,N/A,N/A
Extractive Text Summarization,5,DUC 2004; CNN/Daily Mail; GovReport; SubSumE; DebateSum,CNN / Daily Mail; DUC 2004; GovReport; DebateSum; DUC 2004 Task 1,ROUGE-1; ROUGE-2; Test ROGUE-1; Avg. Test Rouge2; Avg. Test RougeLsum; Avg. Test Rouge1; Test ROGUE-2; ROUGE-L,Reader-Aware Summarization,Natural Language Processing,"Given a document, selecting a subset of the words or sentences which best represents a summary of the document."
Stock Market Prediction,5,stocknet; Astock; Dhaka Stock Exchange Historical Data; FinSen; EDT,stocknet; Astock; S&P 500,Accuray; Precision; F1; F1-score; Average daily returns; Recall,Stock Trend Prediction; Stock Prediction; Stock Price Prediction,Time Series,N/A
Cross-Domain Few-Shot Object Detection,5,NEU-DET; UODD; Artaxor; DeepFish; DIOR,N/A,N/A,N/A,N/A,N/A
Surgical tool detection,5,HeiChole Benchmark; CholecT50; Cholec80; PWISeg; CholecTrack20,N/A,N/A,N/A,N/A,N/A
Hand-Gesture Recognition,5,Human Palm and Gloves Dataset | Human Body Parts Dataset; IPN Hand; MLGESTURE DATASET; VIVA; TIM-Tremor,N/A,N/A,N/A,N/A,N/A
Surface Normal Estimation,5,NYUv2; GRIT; 3D Ken Burns Dataset; FutureHouse; CARLA2Real,N/A,N/A,N/A,N/A,N/A
Face Parsing,5,Helen; CelebAMask-HQ; EasyPortrait; LaPa; iBugMask,N/A,N/A,N/A,N/A,N/A
Face Model,5,FaceWarehouse; Procedural Human Action Videos; MeGlass; FaceScape; HUMBI,N/A,N/A,N/A,Computer Vision,N/A
Content-Based Image Retrieval,5,Oxford5k; PS-Battles; INRIA Holidays Dataset; GPR1200; European Flood 2013 Dataset,INRIA Holidays Dataset,MAP,Drone-view target localization; Drone navigation,Computer Vision,"**Content-Based Image Retrieval** is a well studied problem in computer vision, with retrieval problems generally divided into two groups: category-level retrieval and instance-level retrieval. Given ..."
Medical Relation Extraction,5,GAD; EU-ADR; CMeIE; RadGraph; DDI,DDI extraction 2013 corpus; CMeIE,F1; Micro F1,N/A,Medical,Biomedical relation extraction is the task of detecting and classifying semantic relationships from biomedical text.
Text to Audio Retrieval,5,Clotho; Localized Narratives; WavCaps; SoundDescs; AudioCaps,SoundDescs; Clotho; Localized Narratives; AudioCaps,Text-to-audio R@5; Text-to-audio R@10; R@1; R@5; R@10; mAP@10; Text-to-audio R@1,audio moment retrieval,Audio,N/A
Video Emotion Recognition,5,CREMA-D; Ekman6; RAVDESS; CANDOR Corpus; L-SVD,N/A,N/A,N/A,N/A,N/A
3D Feature Matching,5,PartNet-Mobility; 3DMatch; DeformingThings4D; CPNet; 4DMatch,N/A,N/A,N/A,N/A,N/A
Low Resource Named Entity Recognition,5,Rare Diseases Mentions in MIMIC-III; CoNLL 2003; Broad Twitter Corpus; Few-NERD; CoNLL,N/A,N/A,N/A,N/A,N/A
Video Compression,5,SEPE 8K; YT-UGC; Deep Fakes Dataset; Vinoground; BVI-DVC,N/A,N/A,N/A,N/A,N/A
Program Synthesis,5,PSB2; SketchGraphs; xCodeEval; CONCODE; SPoC,SPoC TestP; AlgoLisp; SPoC TestW,Success rate @budget 100; Accuracy,SQL Synthesis; Program Repair; Type prediction; Value prediction; Enumerative Search,Reasoning; Computer Code,Program synthesis is the process of automatically generating a program or code snippet that satisfies a given specification or set of requirements. This can include generating code from a formal speci...
Malware Detection,5,IoT-23; BODMAS; MalNet; AutoRobust; EMBER,N/A,N/A,N/A,N/A,N/A
Human motion prediction,5,GTA-IM Dataset; MoCapAct; EMDB; NuiSI Dataset; VIENA2,N/A,N/A,Stochastic Human Motion Prediction,Computer Vision,"Action prediction is a pre-fact video understanding task, which focuses on future states, in other words, it needs to reason about future states or infer action labels before the end of action executi..."
Face Generation,5,VGGFace2 HQ; iFakeFaceDB; DFDM; FLUXSynID; Face dataset by Generated Photos,N/A,N/A,Talking Head Generation; Face Age Editing; Kinship face generation; Talking Face Generation; Facial expression generation,Computer Vision,Face generation is the task of generating (or interpolating) new faces from an existing dataset.    The state-of-the-art results for this task are located in the Image Generation parent.    <span styl...
whole slide images,5,MITOS_WSI_CMC; CAMELYON16; CryoNuSeg; LKS; PanNuke,N/A,N/A,N/A,Computer Vision,N/A
Logo Recognition,5,Indian Signboard Image Dataset | Text in Image; LOGO-Net; WiRLD_; OSLD; WiRLD,N/A,N/A,N/A,Computer Vision,N/A
3D Place Recognition,5,In-house; Oxford RobotCar Dataset; CS-Campus3D; Wild-Places; VBR,N/A,N/A,N/A,N/A,N/A
Cell Detection,5,uBench; Fluo-N2DH-GOWT1; Fluo-N2DL-HeLa; PhC-C2DH-U373; PanNuke,N/A,N/A,N/A,N/A,N/A
Spatial Relation Recognition,5,ResQ; SpatialSense Benchmark; TexRel; MRR-Benchmark; Rel3D,Rel3D,Acc,N/A,Computer Vision,N/A
Image Relighting,5,NRHints-Synthetic; Dynamic OLAT Dataset; Stanford-ORB; NRHints-RealCapture; VIDIT,N/A,N/A,N/A,N/A,N/A
Trajectory Clustering,5,rounD Dataset; highD Dataset; uniD Dataset; exiD Dataset; inD Dataset,N/A,N/A,N/A,N/A,N/A
Music Emotion Recognition,5,XMIDI; 4Q audio emotion dataset (Russell's model); VGMIDI; RAVDESS; PIAST,N/A,N/A,N/A,Music,N/A
Medical X-Ray Image Segmentation,5,A collection of X-ray projections of 131 pieces of modeling clay containing stones for machine learning-driven object detection; CheXlocalize; FracAtlas; CheXmask; ChestX-Det,N/A,N/A,N/A,N/A,N/A
3D Assembly,5,Breaking Bad; SBA; DeepCAD; RePAIR Dataset; 3D design files,DeepCAD,1-1,N/A,N/A,N/A
Brain Segmentation,5,CrossMoDA; BRATS 2021; Tc1 Mouse cerebellum atlas; CUTS; Multi-template MRI mouse brain atlas,N/A,N/A,N/A,N/A,N/A
Medical Concept Normalization,5,BB-norm-habitat; BB-norm-phenotype; BB; CoNECo; CHIP-CDN,N/A,N/A,N/A,N/A,N/A
Road Segmentation,5,ChesapeakeRSC; Polarimetric Imaging for Perception; DeepGlobe; Pothole Mix; Massachusetts Roads Dataset,ChesapeakeRSC; Massachusetts Roads Dataset; DeepGlobe,F1; APLS; DWR; IoU; mIoU,Lane Labeling,Computer Vision,Road Segmentation is a pixel wise binary classification in order to extract underlying road network. Various Heuristic and data driven models are proposed. Continuity and robustness still remains one ...
Photoplethysmography (PPG),5,UBFC-rPPG; MIMIC PERform Testing Dataset; MMSE-HR; MTHS; VitalDB,N/A,N/A,Blood pressure estimation; Photoplethysmography (PPG) heart rate estimation; Photoplethysmography (PPG) beat detection; Heart rate estimation,Medical,"**Photoplethysmography (PPG)** is a non-invasive light-based method that has been used since the 1930s for monitoring cardiovascular activity.      <span class=""description-source"">Source: [Non-contac..."
3D Anomaly Detection,5,IoT-23; NovelCraft; Real 3D-AD; How do I Contact Expedia Customer Service 24/7 hours; WALT,Real 3D-AD; Anomaly-ShapeNet10; Anomaly-ShapeNet,Object AUROC; Mean Performance of P. and O. ; Point AUROC; P-AUROC; O-AUROC,Video Anomaly Detection; Artifact Detection,Computer Vision,3D-only Anomaly Detection.  Structures out of normal distribution are detected from the 3D-only point cloud.
Photoplethysmography (PPG) heart rate estimation,5,UBFC-rPPG; MIMIC PERform Testing Dataset; MMSE-HR; BUAA-MIHR dataset; MMPD,N/A,N/A,N/A,N/A,N/A
3D Question Answering (3D-QA),5,MSQA; ScanQA; 3D MM-Vet; SQA3D; Beacon3D,N/A,N/A,N/A,N/A,N/A
Text to 3D,5,Text2CAD; StableText2Lego; CADBench; T$^3$Bench; BlendNet,T$^3$Bench,Avg,N/A,Computer Vision,Task involves generating 3D objects based on the text prompt provided to the system.
Video-Adverb Retrieval,5,ActivityNet Adverbs; MSR-VTT Adverbs; VATEX Adverbs; HowTo100M Adverbs; AIR,ActivityNet Adverbs; MSR-VTT Adverbs; VATEX Adverbs; HowTo100M Adverbs; AIR,Acc-A; mAP M; mAP W,Video-Adverb Retrieval (Unseen Compositions),Computer Vision,The bidirectional video-adverb retrieval task aims at retrieving adverbs that match an action in a video and vice versa.
Image Forgery Detection,5,DIS100k; FairFD; Digital Forensics 2023 dataset - DF2023; CASIA (OSN-transmitted - Weibo); NIST (OSN-transmitted - Facebook),N/A,N/A,N/A,Computer Vision,N/A
Cross-modal retrieval with noisy correspondence,5,COCO-Noisy; Flickr30K-Noisy; Noise of Web; CC152K; NoW,N/A,N/A,N/A,N/A,N/A
3D Generation,5,Mpm-Verse-Large; HumanRig; MeshFLeet; StableText2Lego; E.T. the Exceptional Trajectories,E.T. the Exceptional Trajectories,Classifier-F1; ClaTr-Score; FD_ClaTr,Garment sewing pattern generation,Graphs,N/A
Handwritten Mathmatical Expression Recognition,5,HME100K; CROHME 2019; CROHME 2016; CROHME 2023; CROHME 2014,HME100K; CROHME 2014; CROHME 2019; CROHME 2016,ExpRate,N/A,Computer Vision,Offline Handwritten mathematical Expression Recognition aims to convert 2D images into a 1D structured sequences(LaTeX or MathML)
Image-text Retrieval,5,LeafNet; InpaintCOCO; IMPACT Patent; Noise of Web; NoW,N/A,N/A,N/A,N/A,N/A
Multiview Clustering,4,MNIST; Fashion-MNIST; Multilingual Reuters; NUS-WIDE,N/A,N/A,N/A,N/A,N/A
Unsupervised Anomaly Detection with Specified Settings -- 30% anomaly,4,CIFAR-10; MNIST; Fashion-MNIST; STL-10,ASSIRA Cat Vs Dog; STL-10; Fashion-MNIST; CIFAR-10; MNIST,AUC-ROC,N/A,Computer Vision,N/A
Image Compressed Sensing,4,ImageNet; CelebA; Set11; CBSD68,N/A,N/A,N/A,N/A,N/A
Physical Attribute Prediction,4,FAD; CelebA; Sound of Water 50; CAR,Sound of Water 50,Mean Squared Error,N/A,Computer Vision,N/A
Multimodal Text and Image Classification,4,CUB-200-2011; QUILT-1M; Food-101; CD18,CUB-200-2011; Food-101; CD18,F-measure (%); Accuracy (%); Accuracy,image-sentence alignment; Open-World Social Event Classification,Natural Language Processing,Classification with both source Image and Text
Image Outpainting,4,COCO (Common Objects in Context); Places365; LHQ; MSCOCO,LHQC; MSCOCO; Places365-Standard,Block-FID (Up Extend); Block-FID (Left Extend); Inception score; CLIP Similarity; MSE; FID; Adversarial; Block-FID  (Right Extend); Block-FID (Down Extend); L1,N/A,Computer Vision,Predicting the visual context of an image beyond its boundary.    Image credit: [NUWA-Infinity: Autoregressive over Autoregressive Generation for Infinite Visual Synthesis](https://paperswithcode.com/...
Vehicle Pose Estimation,4,KITTI; CarFusion; VBR; ApolloCar3D,N/A,N/A,N/A,N/A,N/A
3D Object Detection From Stereo Images,4,IBISCape; KITTI; 3D-POP; 3D-ZeF,N/A,N/A,N/A,N/A,N/A
Prediction Of Occupancy Grid Maps,4,KITTI; Occ3D; nuScenes; EviLOG,Occ3D-nuScenes; nuScenes,mIoU,N/A,Computer Vision,N/A
Self-Supervised Action Recognition Linear,4,Kinetics; HMDB51; UCF101; Kinetics 400,N/A,N/A,N/A,N/A,N/A
Mortality Prediction,4,Clinical Admission Notes from MIMIC-III; MIMIC-III; MeDAL; eICU-CRD,Clinical Admission Notes from MIMIC-III; MIMIC-III,Precision; Accuracy; F1 score; Recall; AUROC,ICU Mortality,Medical,"<span style=""color:grey; opacity: 0.6"">( Image credit: [Early hospital mortality prediction using vital signals](https://arxiv.org/pdf/1803.06589v2.pdf) )</span>"
Unsupervised Image Segmentation,4,COCO-Stuff; TYC Dataset; Oxford 102 Flower; BN-HTRd,N/A,N/A,N/A,N/A,N/A
Patch Matching,4,ETH SfM; VIPeR; HPatches; PhotoSynth,Brown Dataset; HPatches,Patch Verification; Patch Matching; FPR95; Patch Retrieval,Multimodal Patch Matching,Computer Vision,N/A
Facial Action Unit Detection,4,DISFA; BP4D; CANDOR Corpus; Thermal Face Database,N/A,N/A,N/A,N/A,N/A
Clinical Concept Extraction,4,2018 n2c2 (Track 2) - Adverse Drug Events and Medication Extraction; Adverse Drug Events (ADE) Corpus; Toronto NeuroFace Dataset; 2010 i2b2/VA,2010 i2b2/VA,Exact Span F1,Clinical Information Retreival,Medical; Natural Language Processing,"Automatic extraction of clinical named entities such as clinical problems, treatments, tests and anatomical parts from clinical notes.    ( [Source](https://arxiv.org/pdf/2012.04005v1.pdf) )"
Temporal Localization,4,TUMTraffic-VideoQA; VidSTG; Charades-STA; AVE,N/A,N/A,Language-Based Temporal Localization; Temporal Defect Localization,Computer Vision,N/A
Temporal Action Proposal Generation,4,ActivityNet Captions; FineAction; ActivityNet; THUMOS14,N/A,N/A,N/A,N/A,N/A
Weakly-supervised Temporal Action Localization,4,UCF101-24; FineAction; ActivityNet; THUMOS14,UCF101-24; ActivityNet-1.3; THUMOS’14,mAP IOU@0.5; mAP; mAP IOU@0.1; mAP IOU@0.95; mAP@AVG(0.1:0.9); mAP IOU@0.8; mAP@0.2; mAP IOU@0.75; mAP IOU@0.3; mAP IOU@0.2,Weakly Supervised Temporal Action Localization,Computer Vision,Temporal Action Localization with weak supervision where only video-level labels are given for training
Abnormal Event Detection In Video,4,ShanghaiTech Campus; ShanghaiTech; UBI-Fights; UCSD Ped2,UCSD Ped2; UBI-Fights,EER; Decidability; AUC,Semi-supervised Anomaly Detection,Computer Vision,"**Abnormal Event Detection In Video** is a challenging task in computer vision, as the definition of what an abnormal event looks like depends very much on the context. For instance, a car driving by ..."
Egocentric Activity Recognition,4,EPIC-KITCHENS-55; EGOK360; EGTEA; DECADE,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Instance Segmentation,4,COCO 10% labeled data; ADE20K; BDD100K; Cityscapes,COCO 10% labeled data; ADE20K; COCO 5% labeled data; COCO 1% labeled data; Cityscapes; COCO 2% labeled data,AP; mask AP,N/A,Computer Vision,N/A
Multimodal Unsupervised Image-To-Image Translation,4,AFHQ; CATS; FFHQ-Aging; CelebA-HQ,N/A,N/A,N/A,N/A,N/A
Unsupervised Action Segmentation,4,IKEA ASM; Youtube INRIA Instructional; 50 Salads; Breakfast,N/A,N/A,N/A,N/A,N/A
Supervised Video Summarization,4,SumMe; Mr. HiSum; Query-Focused Video Summarization Dataset; TvSum,N/A,N/A,N/A,N/A,N/A
Video Salient Object Detection,4,FBMS; ViSal; SegTrack-v2; FBMS-59,N/A,N/A,N/A,N/A,N/A
Image Classification with Label Noise,4,CIFAR-10; WikiChurches; CIFAR-100; Corn Seeds Dataset,N/A,N/A,N/A,N/A,N/A
Reconstruction,4,PPMI; ADE20K; iDesigner; CelebAMask-HQ,ADE20K; CelebAMask-HQ; PPMI; iDesigner; HCP,SSIM; PSNR; runtime (s); R-FID,Single-View 3D Reconstruction; Single-Image-Based Hdr Reconstruction; 4D reconstruction; 3D Human Reconstruction,Reasoning; Computer Vision,N/A
Pose Retrieval,4,Human3.6M; MPI-INF-3DHP; SLAM2REF; HARPER,Human3.6M; MPI-INF-3DHP,Hit@10; Hit@1,N/A,Computer Vision,Retrieval of similar human poses from images or videos
Document Image Classification,4,SUT; Tobacco-3482; S-VED; RVL-CDIP,RVL-CDIP; Tobacco-3482; Noisy Bangla Numeral; SUT; Noisy Bangla Characters; AIP; Noisy MNIST; n-MNIST,Memory; Top 1 Accuracy - Verb; Accuracy; Parameters,N/A,Computer Vision,"Document image classification is the task of classifying documents based on images of their contents.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Real-Time Document Image Classification..."
3D-Aware Image Synthesis,4,CelebAMask-HQ; SynthRAD2023; 3D-Point Cloud dataset of various geometrical terrains; FFHQ,N/A,N/A,N/A,N/A,N/A
Video Polyp Segmentation,4,SUN-SEG-Easy (Unseen); SUN-SEG-Hard (Unseen); PolypGen; STARE,N/A,N/A,N/A,N/A,N/A
Unsupervised Extractive Summarization,4,FacetSum; arXiv Summarization Dataset; XWikiRef; Pubmed,N/A,N/A,N/A,N/A,N/A
Nested Mention Recognition,4,AMALGUM; GUM; ACE 2005; ACE 2004,ACE 2004; ACE 2005,F1,N/A,Natural Language Processing,Nested mention recognition is the task of correctly modeling the nested structure of mentions.
Event Argument Extraction,4,WikiEvents; LEMONADE; MAVEN-Arg; ACE 2005,N/A,N/A,N/A,N/A,N/A
Image Stitching,4,PhotoSynth; HPatches; UDIS-D; CROSS,HPatches,0..5sec,N/A,Computer Vision,"**Image Stitching** is a process of composing multiple images with narrow but overlapping fields of view to create a larger image with a wider field of view.      <span class=""description-source"">Sour..."
3D Open-Vocabulary Instance Segmentation,4,S3DIS; ScanNet200; Replica; STPLS3D,S3DIS; ScanNet200; Replica; STPLS3D,AP Tail; AP50 Novel B8/N4; mAP; AP50 Base B6/N6; AP50 Base B8/N4 ; AP50; AP Common; AP Head; AP25; AP50 Novel B6/N6,N/A,Computer Vision,Open-vocabulary 3D instance segmentation is a computer vision task that involves identifying and delineating individual objects or instances within a three-dimensional (3D) scene without prior knowled...
Cross-Domain Named Entity Recognition,4,CrossNER; CoNLL04; CoNLL; RuTermEval (Track 3),N/A,N/A,N/A,N/A,N/A
Cross-View Image-to-Image Translation,4,Ego2Top; PEnG; Dayton; Cam2BEV,N/A,N/A,N/A,N/A,N/A
Shadow Detection,4,SOBA; INS Dataset; SBU / SBU-Refine; CUHK-Shadow,SBU / SBU-Refine; CUHK-Shadow,BER,Shadow Detection And Removal,Computer Vision,N/A
Weakly supervised Semantic Segmentation,4,nuScenes; CheXlocalize; SemanticPOSS; SemanticKITTI,N/A,N/A,N/A,N/A,N/A
3D Semantic Scene Completion from a single RGB image,4,SSCBench; KITTI-360; NYUv2; SemanticKITTI,N/A,N/A,N/A,N/A,N/A
Few-Shot Semantic Segmentation,4,FewSOL; FSS-1000; PASCAL-5i; GAS,COCO-20i (10-shot); COCO-20i -> Pascal VOC (1-shot); COCO-20i (5-shot); FSS-1000; COCO-20i -> Pascal VOC (5-shot); FSS-1000 (1-shot); COCO-20i (1-shot); FSS-1000 (5-shot); Pascal5i; PASCAL-5i (5-Shot),learnable parameters (million); FB-IoU; meanIOU; Mean IoU; mIoU,Generalized Few-Shot Semantic Segmentation,Computer Vision,Few-shot semantic segmentation (FSS) learns to segment target objects in query image given few pixel-wise annotated support image.
Artery/Veins Retinal Vessel Segmentation,4,HRF; INSPIRE-AVR (LUNet subset); UZLF; LES-AV,N/A,N/A,N/A,N/A,N/A
Drone-view target localization,4,GTA-UAV; University-1652; BuckTales; CODrone,N/A,N/A,N/A,N/A,N/A
Weakly Supervised Defect Detection,4,DAGM2007; KolektorSDD2; ISP-AD; KolektorSDD,N/A,N/A,N/A,N/A,N/A
Nuclear Segmentation,4,CoNSeP; 2018 Data Science Bowl; Cell; MoNuSAC,N/A,N/A,N/A,N/A,N/A
Action Localization,4,COIN; CVB; GraSP; HACS,N/A,N/A,Unusual Activity Localization; Spatio-Temporal Action Localization; Temporal Action Localization; Action Segmentation,Computer Vision,Action Localization is finding the spatial and temporal co ordinates for an action in a video. An action localization model will identify which frame an action start and ends in video and return the x...
Sketch-Based Image Retrieval,4,"QuickDraw-Extended; ShoeV2; Quick, Draw! Dataset; Chairs",Shoes; Handbags; Chairs,R@10; R@1,On-the-Fly Sketch Based Image Retrieval,Computer Vision,N/A
Visual Storytelling,4,Visual Writing Prompts; VIST; Creative Visual Storytelling Anthology; VIST-Edit,VIST,BLEU-3; SPICE; BLEU-2; BLEU-1; METEOR; CIDEr; BLEURT; MLTD; ROUGE-L; BLEU-4,Image-guided Story Ending Generation,Natural Language Processing,"<span style=""color:grey; opacity: 0.6"">( Image credit: [No Metrics Are Perfect](https://github.com/eric-xw/AREL) )</span>"
Building change detection for remote sensing images,4,BANDON; WHU Building Dataset; LEVIR-CD; SECOND,N/A,N/A,N/A,N/A,N/A
Photo geolocation estimation,4,MM-Locate-News; GeoDE; Im2GPS; OpenStreetView-5M,N/A,N/A,N/A,N/A,N/A
Action Understanding,4,Fitness-AQA; Win-Fail Action Understanding; W-Oops; MTL-AQA,Win-Fail Action Understanding,2-Class Accuracy,N/A,Computer Vision,N/A
Multiview Learning,4,3 Sources; Multimodal PISA; MNIST Multiview Datasets; MTL-AQA,N/A,N/A,N/A,Computer Vision,N/A
3D Human Pose Tracking,4,InfiniteRep; Panoptic; UNIPD-BPE; VIDIMU: Multimodal video and IMU kinematic dataset on daily life activities using affordable devices,Panoptic,3DMOTA,Motion Synthesis,Audio,N/A
MRI Reconstruction,4,SKM-TEA; IXI; fastMRI; M4Raw,fastMRI Knee 4x; fastMRI Brain 8x; fastMRI Knee Val 8x ; IXI; fastMRI Knee 8x; fastMRI Brain 4x,Params (M); PSNR; NMSE; MSE; SSIM; DSSIM,Magnetic Resonance Fingerprinting; Quantitative MRI,Medical,"In its most basic form, MRI reconstruction consists in retrieving a complex-valued image from its under-sampled Fourier coefficients.   Besides, it can be addressed as a encoder-decoder task, in which..."
Stock Prediction,4,EDT; stocknet; Astock; Dhaka Stock Exchange Historical Data,N/A,N/A,Text-Based Stock Prediction; PAIR TRADING; Event-Driven Trading,Time Series; Natural Language Processing,N/A
Action Triplet Recognition,4,CholecT50; CholecT40; PETRAW; CholecT45,N/A,N/A,N/A,N/A,N/A
Event Coreference Resolution,4,ECB+; LegalCore; Gun Violence Corpus; MultiReQA,N/A,N/A,N/A,N/A,N/A
Change detection for remote sensing images,4,CDD Dataset (season-varying); GVLM; ChaBuD; SECOND,N/A,N/A,N/A,N/A,N/A
Face Reconstruction,4,FLUXSynID; ICT-3DHP; FaceWarehouse; FaceScape,N/A,N/A,3D Face Reconstruction,Computer Vision,"Face reconstruction is the task of recovering the facial geometry of a face from an image.    <span style=""color:grey; opacity: 0.6"">( Image credit: Microsoft [Deep3DFaceReconstruction](https://github..."
Multi-future Trajectory Prediction,4,TrajAir: A General Aviation Trajectory Dataset; JAAD; PIE; ETH,N/A,N/A,N/A,N/A,N/A
Distant Speech Recognition,4,DIRHA; DiPCo; ReVerb Challenge; BERSt,N/A,N/A,N/A,N/A,N/A
Video Alignment,4,MSU Video Alignment and Retrieval Benchmark Suite; IAW Dataset; Penn Action; N-Digit MNIST,N/A,N/A,N/A,N/A,N/A
EEG Emotion Recognition,4,MEEG; PhyMER; DEAP; SEED,N/A,N/A,N/A,N/A,N/A
Instrument Recognition,4,NSynth; OpenMIC-2018; Kvasir-Instrument; YouTube-100M,IRMAS; OpenMIC-2018; NSynth,Precision; F1-score; mean average precision; Accuracy; Recall,N/A,Audio,N/A
Word Alignment,4,Europarl ConcoDisco Dataset; MUSE; WMT 2016 News; Bangla Word Analogy,fr-en; es-en; MUSE en-pt; MUSE en-de; en-es; en-it; en-fr,P@1,N/A,Natural Language Processing,"**Word Alignment** is the task of finding the correspondence between source and target words in a pair of sentences that are translations of each other.      <span class=""description-source"">Source: [..."
Scientific Results Extraction,4,PWC Leaderboards; LinkedResults; SegmentedTables; ArxivPapers,N/A,N/A,N/A,N/A,N/A
Iris Recognition,4,UBIRIS.v2; UFPR-Periocular; CASIA-Iris-Complex; NDPSID - WACV 2019,N/A,N/A,Pupil Dilation,Medical; Computer Vision,N/A
Supervised Anomaly Detection,4,MVTecAD; CHAD; BTAD; ISP-AD,N/A,N/A,N/A,N/A,N/A
Scene Parsing,4,Stanford Background; UNDD; Cityscapes; PGDP5K,Cityscapes test; PGDP5K,mIoU; Total Accuracy,Face Parsing; Indoor Scene Reconstruction; Scene Text Recognition; Scene Labeling; Scene Graph Generation,Computer Vision,"Scene parsing is to segment and parse an image into different image regions associated with semantic categories, such as sky, road, person, and bed. [MIT Description](http://sceneparsing.csail.mit.edu..."
3D Object Retrieval,4,The RBO Dataset of Articulated Objects and Interactions; LAS&T: Large Shape & Texture Dataset; ModelNet; Visual 3D shape matching dataset,N/A,N/A,N/A,N/A,N/A
Temporal View Synthesis,4,IISc VEED-Dynamic; SceneNet RGB-D; IISc VEED; MPI Sintel,N/A,N/A,N/A,N/A,N/A
Camera shot boundary detection,4,ClipShots; SoccerNet-v2; MSU Shot Boundary Detection Benchmark; TRECVID,N/A,N/A,N/A,N/A,N/A
Indoor Monocular Depth Estimation,4,DIODE; ISOD; InSpaceType; HUMAN4D,N/A,N/A,N/A,N/A,N/A
Audio to Text Retrieval,4,SoundDescs; Clotho; Localized Narratives; AudioCaps,N/A,N/A,N/A,N/A,N/A
Multi-modal Entity Alignment,4,DBP15K; MMKG; UMVM; OpenEA Benchmark,UMVM-oea-d-w-v2; UMVM-dbp-fr-en; UMVM-oea-en-fr; UMVM-dbp-zh-en; UMVM-oea-d-w-v1; UMVM-dbp-ja-en; MMKG; UMVM-oea-en-de,Hits@1; H@1,N/A,Knowledge Base,N/A
Online Beat Tracking,4,ASAP; Ballroom; GTZAN; Rock Corpus,Ballroom; GTZAN; Rock Corpus,F1,Inference Optimization,Audio,N/A
Online Downbeat Tracking,4,ASAP; Ballroom; GTZAN; Rock Corpus,N/A,N/A,N/A,N/A,N/A
Spelling Correction,4,GitHub Typo Corpus; MCSCSet; Viwiki-Spelling; CSCD-IME,N/A,N/A,Bangla Spelling Error Correction,Natural Language Processing,Spelling correction is the task of detecting and correcting spelling mistakes.
Zero-shot Named Entity Recognition (NER),4,CrossNER; HarveyNER; WikiEvents; Broad Twitter Corpus,N/A,N/A,N/A,N/A,N/A
Image Cropping,4,Flickr Cropping Dataset; CUHK Image Cropping; GNMC; AADB,FLMS,BDE; IoU,N/A,Computer Vision,"**Image Cropping** is a common photo manipulation process, which improves the overall composition by removing unwanted regions. Image Cropping is widely used in photographic, film processing, graphic ..."
graph construction,4,CovidQA; IPRE; RoadTracer; Perfume Co-Preference Network,N/A,N/A,N/A,Graphs,N/A
Document-level Relation Extraction,4,DocRED-IE; Bc8; Re-DocRED; DWIE,DocRED-IE; Bc8; Re-DocRED; DWIE,F1; Evaluation Macro F1; Relation F1,Document-level RE with incomplete labeling,Natural Language Processing,Document-level RE aim to identify the relations of various entity pairs expressed across multiple sentences.
3D Shape Representation,4,KeypointNet; Objectron; Foot3D; Dynamic FAUST,N/A,N/A,3D Dense Shape Correspondence,Computer Vision,Image: [MeshNet](https://arxiv.org/pdf/1811.11424v1.pdf)
Hand Segmentation,4,EgoHands; HandNet; EYTH; ObMan-Ego,N/A,N/A,N/A,N/A,N/A
Landmark Recognition,4,Aerial Landmarks Recognition Dataset; Google Landmarks Dataset v2; Chest x-ray landmark dataset; ZuBuD+,"Google Landmarks Dataset v2 (recognition, testing); Google Landmarks Dataset v2 (recognition, validation)",microAP,Brain landmark detection,Computer Vision,N/A
Abuse Detection,4,CoRAL dataset; WikiConv; Hate Speech and Offensive Language; AbuseAnalyzer Dataset,N/A,N/A,Hate Speech Detection,Natural Language Processing,"Abuse detection is the task of identifying abusive behaviors, such as hate speech, offensive language, sexism and racism, in utterances from social media platforms (Source: https://arxiv.org/abs/1802...."
Multispectral Object Detection,4,MS-EVS Dataset; KAIST Multispectral Pedestrian Detection Benchmark; NII-CU MAPD; LLVIP,LLVIP; KAIST Multispectral Pedestrian Detection Benchmark; NII-CU MAPD; FLIR,mAP@0.5:0.95; mAP; AP@0.75; All Miss Rate; AP@0.5; Reasonable Miss Rate; mAP50,N/A,Computer Vision,"Only using RGB cameras for automatic outdoor scene analysis is challenging when, for example, facing insufficient illumination or adverse weather. To improve the recognition reliability, multispectral..."
Medical Image Enhancement,4,SimNICT; Brain Tumor MRI Dataset; LoDoPaB-CT; Human Protein Atlas,Brain Tumor MRI Dataset; LoDoPaB-CT; Human Protein Atlas Image,Average PSNR; SSIM,N/A,Computer Vision,Aims to improve the perceptual quality of low-quality medical images
Action Parsing,4,Home Action Genome; JerichoWorld; DARai; TAPOS,JerichoWorld,Set accuracy,N/A,Natural Language Processing,"Action parsing is the task of, given a video or still image, assigning each frame or image a label describing the action in that frame or image."
Humor Detection,4,UR-FUNNY; Multimodal Humor Dataset; Cards Against Humanity; PHINC,200k Short Texts for Humor Detection,F1-score,N/A,Natural Language Processing,Humor detection is the task of identifying comical or amusing elements.
Thermal Image Segmentation,4,"PST900; MFNet; Multi-Spectral Stereo Dataset  (RGB, NIR, thermal images, LiDAR, GPS/IMU); Charlotte-ThermalFace",N/A,N/A,N/A,N/A,N/A
Sketch Recognition,4,"AtyPict; ShoeV2; Quick, Draw! Dataset; Im4Sketch",N/A,N/A,Image to sketch recognition,Computer Vision,N/A
Cross-View Geo-Localisation,4,PEnG; StreetLearn; VIGOR; SpaGBOL,N/A,N/A,N/A,N/A,N/A
3D Scene Reconstruction,4,IBISCape; The RobotriX; ENRICH; WayveScenes101,N/A,N/A,3D Semantic Scene Completion from a single RGB image,Computer Vision,Creating 3D scene either using conventional SFM pipelines or latest deep learning approaches.
Image-based Automatic Meter Reading,4,UFPR-ADMR-v1; UFPR-ADMR-v2; UFPR-AMR; Copel-AMR,N/A,N/A,Dial Meter Reading,Computer Vision,N/A
Cross-Lingual Abstractive Summarization,4,WikiLingua; M3LS; XWikiRef; WikiMulti,N/A,N/A,N/A,N/A,N/A
Extracting Buildings In Remote Sensing Images,4,xBD; Massachusetts building dataset; MapAI Dataset; WHU Building Dataset,N/A,N/A,N/A,N/A,N/A
Heart Segmentation,4,CheXmask; MM-WHS 2017; ACDC Scribbles; Echonet-Dynamic,N/A,N/A,N/A,N/A,N/A
Action Generation,4,Two4Two; HumanAct12; PHSPD; LLMafia,N/A,N/A,N/A,Computer Vision,N/A
Malware Family Detection,4,AutoRobust; Malimg; MOTIF; MalNet,N/A,N/A,N/A,N/A,N/A
Negation Detection,4,This is not a Dataset; ShARe/CLEF 2014: Task 2 Disorders; THYME-2016; EMC Dutch Clinical Corpus,N/A,N/A,Negation Scope Resolution,Natural Language Processing,Negation detection is the task of identifying negation cues in text.
hand-object pose,4,Human Palm and Gloves Dataset | Human Body Parts Dataset; ARCTIC; DexYCB; HO-3D v2,N/A,N/A,N/A,N/A,N/A
Biomedical Information Retrieval,4,BEIR; TripClick; SuMe; NFCorpus,NFCorpus (BEIR); TREC-COVID (BEIR); BioASQ (BEIR),nDCG@10,PICO; SpO2 estimation,Medical; Natural Language Processing,N/A
Bird Audio Detection,4,TinyChirp; BIRDeep; Western Mediterranean Wetlands Birds - Version 2; Warblr,N/A,N/A,N/A,N/A,N/A
Entity Retrieval,4,BEIR; ZESHEL; Product Reviews 2017; SE-PEF,N/A,N/A,N/A,N/A,N/A
Rumour Detection,4,Sepehr_RumTel01; 1; CIDII Dataset; FTR-18,Sepehr_RumTel01; 1,0..5sec; F-Measure,N/A,Natural Language Processing,"Rumor detection is the task of identifying rumors, i.e. statements whose veracity is not quickly or ever confirmed, in utterances on social media platforms."
Semantic Image-Text Similarity,4,LAION-400M; RGZ EMU: Semantic Taxonomy; CxC; fruit-SALAD,N/A,N/A,N/A,N/A,N/A
Stock Trend Prediction,4,CSI 300 Pair Trading; EDT; Astock; TRADES-LOB,FI-2010,F1 (H50); Accuracy (H50),Stock Market Prediction,Time Series,N/A
Crack Segmentation,4,CrackVision12K; CFD; Khanhha's dataset; CRACK500,N/A,N/A,N/A,N/A,N/A
Hand Detection,4,TI1K Dataset; ThermoHands; Human Palm and Gloves Dataset | Human Body Parts Dataset; Slovo: Russian Sign Language Dataset,N/A,N/A,N/A,Computer Vision,"As an important subject in the field of computer vision, hand detection plays an important role in many tasks such as human-computer interaction, automatic driving, virtual reality and so on."
Blood Cell Detection,4,LeukemiaAttri; Raw-Microscopy and Raw-Drone; uBench; BBBC041,N/A,N/A,N/A,Medical,N/A
Referring Video Object Segmentation,4,Refer-YouTube-VOS; Long-RVOS; ReVOS; MeViS,N/A,N/A,N/A,N/A,N/A
Fake Image Detection,4,ArtiFact; DF40; TwinSynths; Corn Seeds Dataset,N/A,N/A,Fake Image Attribution; GAN image forensics,Computer Vision,"<span style=""color:grey; opacity: 0.6"">( Image credit: [FaceForensics++](https://github.com/ondyari/FaceForensics) )</span>"
Audio-Visual Synchronization,4,Acappella; VGGSound-Sparse; AVS Benchmark; AVSync15,N/A,N/A,N/A,Computer Vision; Audio,N/A
audio-visual learning,4,IS3 (Interactive-Synthetic Sound Source) Dataset; Acappella; AVMIT; GLips,N/A,N/A,N/A,N/A,N/A
Real-Time Multi-Object Tracking,4,MMPTRACK; LTFT; CholecTrack20; Argoverse-HD,N/A,N/A,N/A,N/A,N/A
Intent Recognition,4,OIR; IndirectRequests; diaforge-utc-r-0725; BIG-bench,BIG-bench,Accuracy ,Multimodal Intent Recognition,Miscellaneous,N/A
Temporal Forgery Localization,4,ForgeryNet; TVIL; LAV-DF; AV-Deepfake1M,N/A,N/A,N/A,N/A,N/A
Discourse Segmentation,4,DISRPT2019; GUM; AMALGUM; DISRPT2021,N/A,N/A,N/A,N/A,N/A
Temporal Relation Extraction,4,2012 i2b2 Temporal Relations; Catalan TimeBank 1.0; French Timebank; Vinoground,Vinoground,Group Score; Video Score; Text Score,Temporal Relation Classification,Natural Language Processing,"Temporal relation extraction systems aim to identify and classify the temporal relation between a pair of entities provided in a text. For instance, in the sentence ""Bob sent a message to Alice while ..."
UNET Segmentation,4,AbdomenCT-1K; Pothole Mix; Munich Sentinel2 Crop Segmentation; MGPFD,N/A,N/A,N/A,N/A,N/A
3D Lane Detection,4,ONCE-3DLanes; OpenLane-V2 test; OpenLane; OpenLane-V2 val,N/A,N/A,N/A,N/A,N/A
Camera Calibration,4,Cross-View Cross-Scene Multi-View Crowd Counting Dataset; CVGL; DeepSportRadar-v1; SoccerNet-GSR,N/A,N/A,N/A,Computer Vision,"Camera calibration involves estimating camera parameters(including camera intrinsics and extrinsics) to infer geometric features from captured sequences, which is crucial for computer vision and robot..."
Robust Speech Recognition,4,FSC-P2; Speech Robust Bench; DiPCo; Google Speech Commands - Musan,N/A,N/A,N/A,N/A,N/A
Optical Charater Recogntion,4,MatriVasha:; Oximeter Image Dataset | Medical Device Reading; Indian Number Plates Dataset | Vehicle Number Plates | English OCR Detection; Bike and Car Odometer Dataset ! Speedometer OCR,N/A,N/A,Bangla Text Detection,Natural Language Processing,N/A
Image Defocus Deblurring,4,DPD (Dual-view); Motion Blurred and Defocused Dataset; SDD; RealDOF,N/A,N/A,N/A,N/A,N/A
3D Facial Expression Recognition,4,"4,458 People - 3D Facial Expressions Recognition Data; FER2013 Blendshapes; Florence 4D; HEADSET",N/A,N/A,N/A,N/A,N/A
Cell Entity Annotation,4,BiodivTab; WikiTables-TURL; WikipediaGS; Tough Tables,N/A,N/A,N/A,N/A,N/A
Person Retrieval,4,Occluded-PoseTrack-ReID; RSTPReid; ITCPR dataset; TVPReid,SoftBioSearch,Average IOU,N/A,Computer Vision,N/A
Lesion Detection,4,Breast Lesion Detection in Ultrasound Videos (CVA-Net); Duke Breast Cancer MRI; AutoPET; HECKTOR,N/A,N/A,N/A,N/A,N/A
Dynamic Facial Expression Recognition,4,DFEW; FERV39k; MEAD; MAFW,N/A,N/A,N/A,N/A,N/A
Point Tracking,4,Perception Test; TAP-Vid; TAPVid-3D: A Benchmark for Tracking Any Point in 3D; PointOdyssey,TAP-Vid-RGB-Stacking; TAP-Vid-Kinetics; TAP-Vid; Perception Test; TAP-Vid-DAVIS; TAP-Vid-Kinetics-First; PointOdyssey; TAP-Vid-DAVIS-First,Average Jaccard; MTE; Average PCK; Survival; δ; Occlusion Accuracy,N/A,Computer Vision,"Point Tracking, often referred to as Tracking any Point (TAP) involves acquiring, focusing on, and continuously tracking specific target point/points across video frames. The system identifies the tar..."
Financial Relation Extraction,4,Financial Dynamic Knowledge Graph; FinArg; FinRED; REFinD,N/A,N/A,N/A,N/A,N/A
Social Navigation,4,HabiCrowd; Social-HM3D; Vid2RealHRI online video and results dataset; Social-MP3D,N/A,N/A,N/A,N/A,N/A
Synthetic Speech Detection,4,ShiftySpeech; DEEP-VOICE: DeepFake Voice Recognition; SONICS; Synthetic Speech Attribution,N/A,N/A,N/A,N/A,N/A
medical image detection,4,GRAZPEDWRI-DX; Heel Dataset; OCT5k; MP-IDB,N/A,N/A,N/A,N/A,N/A
Spectral Reconstruction,4,CAVE; Real HSI; KAIST; ARAD-1K,N/A,N/A,N/A,N/A,N/A
Event-based Object Segmentation,4,DDD17-SEG; RGBE-SEG; DSEC-SEG; MVSEC-SEG,DDD17-SEG; RGBE-SEG; DSEC-SEG; MVSEC-SEG,mIoU,N/A,N/A,N/A
Deformable Object Manipulation,4,DeformPAM-Dataset; VIRDO Dataset; Branched Deformable Linear Objects (BDLOs) Dataset; Deformable Linear Objects  (DLOs) Dataset,N/A,N/A,N/A,Robots,N/A
2D Tiny Object Detection,4,((Refund~option))What is the refundable option on Expedia?; Bone Fracture Multi-Region X-ray Dataset; Bone Fracture Multi-Region X-ray Data; SIRST-UAVB,N/A,N/A,Insulator Defect Detection,Computer Vision,N/A
Hyperspectral image analysis,4,MaNGA; Tecnalia WEEE HYPERSPECTRAL DATASET; Hyper Drive; HSIRS,complex refractive index through reflection,Pearson correlation coefficient (PCC); RMSPE,N/A,N/A,N/A
Video Editing,4,FiVE; VPBench; V2VBench; VPData,N/A,N/A,Video Temporal Consistency,Computer Vision; Graphs,N/A
Sequential Image Classification,3,CIFAR-10; MNIST; Mudestreda,N/A,N/A,N/A,N/A,N/A
Image Colorization,3,NIR2RGB VCIP Challange Dataset; ImageNet; CelebA,NIR2RGB VCIP Challange Dataset; ImageNet; CelebA,PSNR; FID; Consistency,Sketch Colorization,Computer Vision,N/A
Image Attribution,3,CUB-200-2011; VGGFace2; CelebA,CUB-200-2011; VGGFace2; CelebA,Deletion AUC score (ArcFace ResNet-101); Deletion AUC score (ResNet-101); Insertion AUC score (ArcFace ResNet-101); Insertion AUC score (ResNet-101),N/A,Computer Vision,Image attribution algorithms aim to identify important regions that are highly relevant to model decisions.
Model Compression,3,ImageNet; QNLI; GLUE,ImageNet; QNLI,Accuracy; Top-1,Neural Network Compression,Miscellaneous; Methodology,**Model Compression** is an actively pursued area of research over the last few years with the goal of deploying state-of-the-art deep networks in low-power and resource limited devices without signif...
Weakly-Supervised Object Localization,3,Tiny ImageNet; ImageNet; CUB-200-2011,N/A,N/A,N/A,N/A,N/A
Self-Supervised Image Classification,3,ImageNet; Chest X-ray images; BIOSCAN-5M,N/A,N/A,N/A,N/A,N/A
Synthetic Face Recognition,3,CPLFW; CALFW; LFW,N/A,N/A,N/A,N/A,N/A
Face Quality Assessement,3,mebeblurf; Color FERET; LFW,mebeblurf; Color FERET; LFW,Pearson Correlation; Equal Error Rate,Face Image Quality,Computer Vision,N/A
Face Anonymization,3,FDH; FDF; LFW,2019_test set; LFW,negated ID retrieval; Temporal ID consistency; ID retrieval; 10%,N/A,Computer Vision,N/A
Semi Supervised Learning for Image Captioning,3,COCO (Common Objects in Context); FlickrStyle10K; Flickr30k,COCO (Common Objects in Context); FlickrStyle10K; Flickr30k,CIDEr,Pseudo Label,Miscellaneous,N/A
mage-to-Text Retrieval,3,COCO (Common Objects in Context); MSCOCO; Flickr30k,N/A,N/A,N/A,N/A,N/A
Single-object discovery,3,COCO (Common Objects in Context); Object Discovery; PASCAL VOC,COCO_20k; Object Discovery; VOC12; VOC_all; VOC_6x2,CorLoc,N/A,Computer Vision,N/A
Multi-object discovery,3,COCO (Common Objects in Context); RF100; PASCAL VOC,COCO_20k; VOC_all; VOC12,Detection Rate,N/A,Computer Vision,N/A
Object Proposal Generation,3,COCO (Common Objects in Context); Comic2k; CocoDoom,N/A,N/A,N/A,N/A,N/A
Visual Keyword Spotting,3,LRS2; LRS3-TED; LRW,N/A,N/A,N/A,N/A,N/A
Early Action Prediction,3,Something-Something V2; NTU RGB+D; UCF101,N/A,N/A,N/A,N/A,N/A
Image Generation from Scene Graphs,3,Home Action Genome; Visual Genome; BDD100K-Subsets,N/A,N/A,N/A,N/A,N/A
Scene Graph Classification,3,HRI Simple Tasks; ImageCLEF-DA; Visual Genome,N/A,N/A,N/A,N/A,N/A
Zero-Shot Semantic Segmentation,3,COCO-Stuff; ADE20K; PASCAL VOC,COCO-Stuff; ADE20K-847; PASCAL VOC; MESS,Mean IoU; Transductive Setting hIoU; unseen mIoU; Inductive Setting hIoU,N/A,Computer Vision,N/A
Defocus Blur Detection,3,CUHK03; EBD; HistoArtifacts,SZU blur detection; EBD; CTCUG; DUT ; CUHK,MAE; Mean absolute error; IoU,N/A,Computer Vision,N/A
Action Unit Detection,3,BP4D; Aff-Wild; 4DFAB,N/A,N/A,N/A,N/A,N/A
Point-interactive Image Colorization,3,ImageNet ctest10k; CUB-200-2011; Oxford 102 Flower,N/A,N/A,N/A,N/A,N/A
Temporal Information Extraction,3,TimeBank; THYME-2016; TempEval-3,TimeBank; TempEval-3,F1 score; Temporal awareness,Temporal Tagging,Natural Language Processing,"Temporal information extraction is the identification of chunks/tokens corresponding to temporal intervals, and the extraction and determination of the temporal relations between those. The entities e..."
Partially Relevant Video Retrieval,3,ActivityNet Captions; TVR; Charades-STA,N/A,N/A,N/A,N/A,N/A
Dense Object Detection,3,DOTA; SKU110K; xView3-SAR,N/A,N/A,N/A,N/A,N/A
Audio-Visual Active Speaker Detection,3,AVA; AVA-ActiveSpeaker; VPCD,N/A,N/A,N/A,N/A,N/A
Graph Anomaly Detection,3,Yelp-Fraud; Amazon-Fraud; Yelp,N/A,N/A,N/A,N/A,N/A
Multi-Object Tracking and Segmentation,3,BDD100K; BURST; KITTI MOTS,BDD100K val; KITTI MOTS,HOTA; mMOTSA; AssA; DetA,N/A,Computer Vision,"Multiple object tracking and segmentation requires detecting, tracking, and segmenting objects belonging to a set of given classes.     (Image and definition credit: [Prototypical Cross-Attention Netw..."
Semantic Object Interaction Classification,3,Kinetics; Kinetics-700; SPACE,N/A,N/A,N/A,N/A,N/A
Event Segmentation,3,Kinetics; EgoProceL; Kinetics 400,Kinetics-400,F1,Generic Event Boundary Detection,Computer Vision,N/A
Unconditional Image Generation,3,Large Labelled Logo Dataset (L3D); ArtBench-10 (32x32); CelebA-HQ,N/A,N/A,N/A,N/A,N/A
Unsupervised Facial Landmark Detection,3,MAFL; 300W; AFLW,N/A,N/A,N/A,N/A,N/A
Unsupervised Video Summarization,3,SumMe; TvSum; OSTD,N/A,N/A,N/A,N/A,N/A
Multi-Hypotheses 3D Human Pose Estimation,3,Human3.6M; MPI-INF-3DHP; AH36M,N/A,N/A,N/A,N/A,N/A
Unsupervised Human Pose Estimation,3,Human3.6M; Tai-Chi-HD; DeepFashion,N/A,N/A,N/A,N/A,N/A
3D Face Alignment,3,FaceWarehouse; AFLW2000-3D; FaMoS,N/A,N/A,N/A,N/A,N/A
Face Hallucination,3,EDFace-Celeb-1M; KID-F; FFHQ,N/A,N/A,N/A,N/A,N/A
Colorectal Gland Segmentation:,3,Kvasir; Kvasir-SEG; STARE,CRAG; STARE,F1-score; Dice; Hausdorff Distance (mm); AUC,N/A,Medical,N/A
Lung Nodule Detection,3,LUNA; LIDC-IDRI; LUNA16,LUNA2016 FPRED; LIDC-IDRI,Sensitivity; mean average precision; AUC,Lung Nodule 3D Detection,Medical; Computer Vision,N/A
Molecular Property Prediction (1-shot)),3,Tox21; PubChem18; SIDER,N/A,N/A,N/A,N/A,N/A
Graph Property Prediction,3,OGB; QM9; PCQM4Mv2-LSC,ogbg-molpcba; ogbg-ppa; QM9; ogbg-molhiv; ogbg-code2,gap (meV); alpha (ma); Validation F1 score; Validation ROC-AUC; Validation AP; logMAE; Test AP; Number of params; Validation Accuracy; Test F1 score,SAXS regression; XRD regression; SANS regression; Neutron PDF regression; ND regression,Graphs,N/A
Prediction,3,Bone Cement Removal with Audio-Monitoring; QM9; SDWPF,QM9; Synthetic,RMSE; Edit Distance,N/A,Time Series,N/A
Supervised Text Retrieval,3,Reuters-21578; 20 Newsgroups; COVID-19 Twitter Chatter Dataset,N/A,N/A,N/A,N/A,N/A
Human Instance Segmentation,3,PeopleSansPeople; TikTok Dataset; OCHuman,OCHuman,AP,Pose-Based Human Instance Segmentation,Computer Vision,Instance segmentation is the task of detecting and delineating each distinct object of interest appearing in an image.    Image Credit: [Deep Occlusion-Aware Instance Segmentation with Overlapping BiL...
3D Room Layouts From A Single RGB Panorama,3,2D-3D-S; PanoContext; Rent3D,N/A,N/A,N/A,N/A,N/A
Unsupervised Saliency Detection,3,DUTS; ECSSD; DUT-OMRON,N/A,N/A,N/A,N/A,N/A
Image to sketch recognition,3,Im4Sketch; PACS; Sketchy,N/A,N/A,N/A,N/A,N/A
Zero Shot Skeletal Action Recognition,3,NTU RGB+D 120; NTU RGB+D; PKU-MMD,N/A,N/A,N/A,N/A,N/A
Generalized Zero Shot skeletal action recognition,3,NTU RGB+D 120; NTU RGB+D; PKU-MMD,N/A,N/A,N/A,N/A,N/A
Unsupervised Skeleton Based Action Recognition,3,NTU RGB+D 120; NTU RGB+D; PKU-MMD,N/A,N/A,N/A,N/A,N/A
Bird's-Eye View Semantic Segmentation,3,SimBEV; LandCover.ai; nuScenes,SimBEV; nuScenes; Lyft Level 5,IoU vehicle - 224x480 - Long; bicycle; pedestrian; IoU ped - 224x480 - Vis filter. - 100x100 at 0.5; IoU lane - 224x480 - 100x100 at 0.5; IoU veh - 224x480 - No vis filter - 100x50 at 0.25; motorcycle; mIoU; truck; IoU veh - 224x480 - Vis filter. - 100x100 at 0.5,N/A,Computer Vision,N/A
Skin Cancer Segmentation,3,SD-198; PH2; ISIC 2020 Challenge Dataset,N/A,N/A,N/A,N/A,N/A
4D Panoptic Segmentation,3,4D-OR; MM-OR; SemanticKITTI,SemanticKITTI,LSTQ,N/A,Computer Vision,"**4D Panoptic Segmentation** is a computer vision task that extends [video panoptic segmentation](https://paperswithcode.com/task/video-panoptic-segmentation) to point cloud sequences. That is, given ..."
Twitter Bot Detection,3,Crypto related tweets from 10.10.2020 to 3.3.2021; MGTAB; MIB Dataset,MGTAB; MIB Dataset,F1; Accuracy; Acc,N/A,Miscellaneous,Academic studies estimate that up to 15% of Twitter users are automated bot accounts [1]. The prevalence of Twitter bots coupled with the ability of some bots to give seemingly human responses has ena...
Multi-Frame Super-Resolution,3,Inter4K; PROBA-V; BurstSR,N/A,N/A,N/A,N/A,N/A
Video Emotion Detection,3,CMU-MOSEI; Ekman6; L-SVD,N/A,N/A,N/A,N/A,N/A
Continuous Affect Estimation,3,NEMO; AMIGOS; AffectNet,AffectNet; AMIGOS; 	AffectNet,PCC (Arousal); Concordance correlation coefficient (CCC); CCC (Arousal); CCC (Valence); PCC (Valence),N/A,Computer Vision,N/A
Event data classification,3,N-Caltech 101; CIFAR10-DVS; DVS128 Gesture,N-Caltech 101; CIFAR10-DVS; DVS128 Gesture,Accuracy; Accuracy (% ),N/A,Computer Vision,N/A
Open Vocabulary Action Detection,3,UCF101-24; MultiSports; JHMDB,UCF101-24; MultiSports; JHMDB,val mAP,N/A,Computer Vision,N/A
Target Sound Extraction,3,FSDSoundScapes; AudioCaps; AudioSet,AudioCaps; FSDSoundScapes; AudioSet,SI-SDRi; SDRi; SI-SNRi,Streaming Target Sound Extraction,Audio,Target Sound Extraction is the task of extracting a sound corresponding to a given class from an audio mixture. The audio mixture may contain background noise with a relatively low amplitude compared ...
Traffic Accident Detection,3,TAP; A3D; CAP-DATA,A3D; custom; SA,AUC; Average F1,Accident Anticipation,Computer Vision,N/A
Recognizing And Localizing Human Actions,3,HAR; UESTC RGB-D; Capture-24,N/A,N/A,N/A,N/A,N/A
Document-level Closed Information Extraction,3,DocRED-IE; DocRED; DWIE,N/A,N/A,N/A,N/A,N/A
Multi-tissue Nucleus Segmentation,3,Kumar; CoNSeP; PanNuke,Kumar; CoNSeP; PanNuke,Jaccard Index; Dice; Hausdorff Distance (mm); PQ,N/A,Medical,N/A
Clique Prediction,3,Arxiv HEP-TH citation graph; arXiv Astro-Ph; Arxiv GR-QC,N/A,N/A,N/A,N/A,N/A
Polyp Segmentation,3,Kvasir; Kvasir-SEG; PolypGen,N/A,N/A,N/A,N/A,N/A
Drone Pose Estimation,3,UAVA; VID Dataset; Drunkard's Dataset,N/A,N/A,N/A,N/A,N/A
Video Saliency Detection,3,AViMoS; MSU Video Saliency Prediction; DHF1K,N/A,N/A,N/A,N/A,N/A
Video Saliency Prediction,3,EgoMon; AViMoS; DHF1K,N/A,N/A,N/A,N/A,N/A
Multimodal Abstractive Text Summarization,3,M3LS; FINDSum; How2,N/A,N/A,N/A,N/A,N/A
Text based Person Retrieval,3,ICFG-PEDES; CUHK-PEDES; RSTPReid,ICFG-PEDES; CUHK-PEDES; RSTPReid,Rank-5; Rank-1; mAP; R@1; Rank-10; R@5; R@10; mINP,N/A,Computer Vision,N/A
Text-based Person Retrieval with Noisy Correspondence,3,ICFG-PEDES; CUHK-PEDES; RSTPReid,ICFG-PEDES; CUHK-PEDES; RSTPReid,Rank 1; Rank-5; mAP; Rank-1; Rank 10; Rank-10; Rank 5; mINP,N/A,Computer Vision,This is a benchmark about text-based person retrieval with noisy correspondence. All recorded values ​​are the results under 20% noise rate.
Semantic Retrieval,3,Contract Discovery; Speech Brown; Phrase-in-Context,Contract Discovery,Soft-F1,N/A,Natural Language Processing,N/A
3D Point Cloud Data Augmentation,3,ModelNet; ScanObjectNN; ModelNet40-C,N/A,N/A,N/A,N/A,N/A
Recognizing Emotion Cause in Conversations,3,EmoCause; CANDOR Corpus; RECCON,EmoCause; RECCON,F1(Pos); Top-3 Recall; F1; Top-1 Recall; Top-5 Recall; Exact Span F1; F1(Neg),Causal Emotion Entailment,Natural Language Processing,"Given an utterance U, labeled with emotion E, the task is to extract the causal spans S from the conversational history H (including utterance U) that sufficiently represent the causes of emotion E."
Text-Based Stock Prediction,3,EDT; stocknet; Astock,N/A,N/A,N/A,N/A,N/A
Audio Super-Resolution,3,DSD100; MedleyDB 2.0; VCTK,DSD100; VCTK Multi-Speaker; Piano; Voice Bank corpus (VCTK),Log-Spectral Distance; SNR,N/A,Audio,"Audio super-resolution, especially speech, refers to the process of reconstructing high-resolution music signals from their low-resolution counterparts. Essentially, it enhances the quality of a speec..."
Multi Future Trajectory Prediction,3,KITTI-trajectory-prediction; TrajAir: A General Aviation Trajectory Dataset; ETH,N/A,N/A,N/A,N/A,N/A
Grayscale Image Denoising,3,BSD; Set12; Urban100,N/A,N/A,N/A,N/A,N/A
Multi-class Anomaly Detection,3,MVTecAD; ITDD; VisA,N/A,N/A,N/A,N/A,N/A
Loop Closure Detection,3,New College; AUT-VI; KAIST Urban,N/A,N/A,N/A,N/A,N/A
Drug–drug Interaction Extraction,3,DrugBank; DDI; Drug Combination Extraction Dataset,N/A,N/A,N/A,N/A,N/A
Online Action Detection,3,FineAction; TVSeries; THUMOS14,N/A,N/A,N/A,N/A,N/A
Chord Recognition,3,JAAH; RWC; Filosax,N/A,N/A,N/A,Audio,N/A
Direction of Arrival Estimation,3,TAU-NIGENS Spatial Sound Events 2021; LOCATA; TAU-NIGENS Spatial Sound Events 2020,SOFA,Angular Error,N/A,Audio,Estimating the direction-of-arrival (DOA) of a sound source from multi-channel recordings.
Volumetric Medical Image Segmentation,3,RAOS; Radio-Freqency Ultrasound volume dataset for pre-clinical liver tumors; PROMISE12,N/A,N/A,N/A,N/A,N/A
speech-recognition,3,VoxPopuli; Common Voice; OpenSLR,N/A,N/A,N/A,N/A,N/A
Predicate Detection,3,Product Reviews 2017; CoNLL; CoNLL-2012,N/A,N/A,N/A,N/A,N/A
Semantic Role Labeling (predicted predicates),3,Product Reviews 2017; CoNLL; CoNLL-2012,N/A,N/A,N/A,N/A,N/A
Sentence segmentation,3,CoNLL 2017 Shared Task - Automatically Annotated Raw Texts and Word Embeddings; CoNLL; MASC,N/A,N/A,N/A,N/A,N/A
Binary Relation Extraction,3,BioRED; SciREX; BB,N/A,N/A,N/A,N/A,N/A
Repetitive Action Counting,3,Countix; UCFRep; RepCount,Countix; UCFRep; RepCount,MAE; RMSE; OBO; OBZ,N/A,Computer Vision,Repetitive action counting aims to count the number of repetitive actions in a video.
Gaze Target Estimation,3,GazeFollow; EyeInfo; VideoAttentionTarget,GazeFollow; VideoAttentionTarget,Average Distance; AUC; AP,N/A,Computer Vision,Gaze Target Estimation refers to predicting the image 2D gaze location of a person in the image.
3D Point Cloud Matching,3,2D-3D Match Dataset; 4DMatch; DeformingThings4D,N/A,N/A,N/A,N/A,N/A
Reflection Removal,3,SlowFlow; PolarRR; CDR,Real20; SIR^2(Postcard); SIR^2(Objects); Nature; SIR^2(Wild),PSNR; SSIM,N/A,Computer Vision,Remove the spots from mirror and clear the picture
Definition Extraction,3,ProNCI; UJ-CS/Math/Phy; DEFT Corpus,N/A,N/A,N/A,N/A,N/A
Action Spotting,3,GolfDB; SoccerNet-v2; SoccerNet,N/A,N/A,N/A,N/A,N/A
Inverse Rendering,3,Stanford-ORB; MID Intrinsics; Hypersim,Stanford-ORB,HDR-PSNR,N/A,Computer Vision,"**Inverse Rendering** is the task of recovering the properties of a scene, such as shape, material, and lighting, from an image or a video. The goal of inverse rendering is to determine the properties..."
3D Panoptic Segmentation,3,4D-OR; MM-OR; Hypersim,N/A,N/A,N/A,N/A,N/A
Hyperspectral Semantic Segmentation,3,Hyperspectral City; HSI-Drive v2.0; HyKo2-VIS,N/A,N/A,N/A,N/A,N/A
Chinese Word Segmentation,3,CUGE; MSRA CN NER; LSICC,N/A,N/A,N/A,N/A,N/A
Spindle Detection,3,Montreal Archive of Sleep Studies; MODA dataset; MaSS,N/A,N/A,N/A,N/A,N/A
Face Clustering,3,A View From Somewhere (AVFS); MovieGraphs; EasyCom,N/A,N/A,N/A,N/A,N/A
Event-based Optical Flow,3,DSEC; MVSEC; EKubric,N/A,N/A,N/A,N/A,N/A
lidar absolute pose regression,3,ConSLAM; SLAM2REF; Oxford Radar RobotCar Dataset,vReLoc (Seq-06); Oxford Radar RobotCar (Full-8); vReLoc (Seq-07); vReLoc (Seq-14); Oxford Radar RobotCar (Full-6); vReLoc (Seq-05); Oxford Radar RobotCar (Full-9); Oxford Radar RobotCar (Full-7),Median Translation/Rotation Error (m/degree); Mean Translation/Rotation Error (m/degree),N/A,Computer Vision,N/A
Fovea Detection,3,REFUGE Challenge; IDRiD; ADAM,REFUGE Challenge; IDRiD; REFUGE; ADAM,Euclidean Distance (ED),N/A,Medical,N/A
Optic Disc Detection,3,REFUGE Challenge; IDRiD; ADAM,REFUGE Challenge; IDRiD,Euclidean Distance (ED); IoU,N/A,Medical,Region proposal for optic disc
Object Discovery,3,UVO; ShapeStacks; Infinity-MM,N/A,N/A,N/A,Computer Vision,"**Object Discovery** is the task of identifying previously unseen objects.   <span class=""description-source"">Source: [Unsupervised Object Discovery and Segmentation of RGBD-images ](https://arxiv.org..."
Visual Entailment,3,VSR; SNLI-VE; e-SNLI-VE,N/A,N/A,N/A,N/A,N/A
Spatio-Temporal Video Grounding,3,HC-STVG1; VidSTG; HC-STVG2,HC-STVG1; VidSTG; HC-STVG2,Interrogative vIoU@0.3; Declarative m_vIoU; Interrogative vIoU@0.5; Val m_vIoU; Declarative vIoU@0.5; m_vIoU; Val vIoU@0.5; vIoU@0.3; vIoU@0.5; Interrogative m_vIoU,N/A,Computer Vision,Spatio-temporal video grounding is a computer vision and natural language processing (NLP) task that involves linking textual descriptions to specific spatio-temporal regions or moments in a video. In...
3D Object Detection From Monocular Images,3,3D-POP; Waymo Open Dataset; KITTI-360,N/A,N/A,N/A,N/A,N/A
Aspect Term Extraction and Sentiment Classification,3,YASO; AWARE; Casino Reviews,N/A,N/A,N/A,N/A,N/A
Road Damage Detection,3,NPO; Pothole Mix; RDD-2020,NPO,mIoU,N/A,Computer Vision,"Road damage detection is the task of detecting damage in roads.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Road Damage Detection And Classification In Smartphone Captured Images Using ..."
Texture Synthesis,3,CBTex; QST; 3D-FUTURE,N/A,N/A,N/A,Computer Vision,"The fundamental goal of example-based **Texture Synthesis** is to generate a texture, usually larger than the input, that faithfully captures all the visual characteristics of the exemplar, yet is nei..."
Breast Cancer Histology Image Classification,3,BCI; BreakHis; ICIAR 2018 Grand Challenge on Breast Cancer Histology Images,ICIAR 2018 Grand Challenge on Breast Cancer Histology Images; BreakHis,Accuracy (%); Accuracy (Inter-Patient); 1:1 Accuracy; Accuracy (% ),Breast Cancer Detection; Breast Cancer Histology Image Classification (20% labels),Computer Vision; Knowledge Base,N/A
Medical Image Retrieval,3,BreakHis; BreastRates4; BreastDICOM4,N/A,N/A,N/A,N/A,N/A
Pedestrian Trajectory Prediction,3,JAAD; PIE; Euro-PVI,N/A,N/A,N/A,N/A,N/A
Face Morphing Attack Detection,3,SMDD; FRLL-Morphs; FLUXSynID,N/A,N/A,N/A,N/A,N/A
Voice Query Recognition,3,Fluent Speech Commands; TAT; Banglish,Banglish,Accuracy (%),N/A,Speech,N/A
Surgical Gesture Recognition,3,MultiBypass140; MISAW; PETRAW,N/A,N/A,N/A,Medical,N/A
Affordance Detection,3,Visual Affordance Learning; PADv2; 3D AffordanceNet,3D AffordanceNet Partial View; 3D AffordanceNet Rotate SO(3); 3D AffordanceNet Rotate z; 3D AffordanceNet,AIOU; mAP,N/A,Computer Vision,"Affordance detection refers to identifying the potential action possibilities of objects in an image, which is an important ability for robot perception and manipulation.    Image source: [Object-Base..."
Human-Object-interaction motion tracking,3,MPHOI-72; BEHAVE; Win-Fail Action Understanding,N/A,N/A,N/A,N/A,N/A
Cardiac Segmentation,3,CAMUS; ACDC Scribbles; Echonet-Dynamic,N/A,N/A,N/A,N/A,N/A
Weakly supervised segmentation,3,MARIDA; CheXlocalize; ACDC Scribbles,N/A,N/A,N/A,Computer Vision,N/A
Sign Language Production,3,How2Sign; LSFB Datasets; Mediapi-RGB,N/A,N/A,N/A,Natural Language Processing,"Sign Language Production (SLP) is the automatically translation from spoken language sentences into sign language sequences. Whilst Sign language Translation translates from sign to text, SLP is the o..."
Texture Image Retrieval,3,LAS&T: Large Shape & Texture Dataset; ISOD; ElBa,N/A,N/A,N/A,N/A,N/A
Supervised Defect Detection,3,SensumSODF; KolektorSDD2; ISP-AD,N/A,N/A,N/A,N/A,N/A
Argument Retrieval,3,BEIR; FinArg; Webis-Touché-2020,N/A,N/A,N/A,N/A,N/A
Personality Trait Recognition,3,SynthPAI; CPED; Essays,SynthPAI; Essays,Precision; Average accuracy in %; Accuracy; F-Measure; Recall,N/A,Computer Vision,N/A
Semantic Image Similarity,3,NIGHTS; CxC; fruit-SALAD,N/A,N/A,N/A,N/A,N/A
Histopathological Image Classification,3,BCNB; Chaoyang; DiagSet,N/A,N/A,N/A,Medical,N/A
Radar Object Detection,3,CRUW; RaDelft; BAAI-VANJEE,N/A,N/A,N/A,Robots,The radar object detection (ROD) task aims to classify and localize the objects in 3D purely from radar's radio frequency (RF) images.
Activity Prediction,3,Home Action Genome; InfiniteRep; MODIS AOD (imputed),ActEV,mAP,Cyber Attack Detection; motion prediction; Sequential skip prediction,Time Series; Methodology; Computer Vision,Predict human activities in videos
Satire Detection,3,YesBut; SaRoCo; CIDII Dataset,N/A,N/A,N/A,N/A,N/A
Segmentation Of Remote Sensing Imagery,3,CaBuAr; Five-Billion-Pixels; GID,N/A,N/A,Lake Ice Monitoring,Computer Vision,N/A
3D Point Cloud Reconstruction,3,IBISCape; HUMAN4D; Boombox,N/A,N/A,3D Point Cloud Classification,Computer Vision,Encoding and reconstruction of 3D point clouds.
Review Generation,3,MOPRD; ReviewRobot Dataset; ICLR Database,N/A,N/A,N/A,Natural Language Processing,N/A
Photo Retouching,3,NILUT; MIT-Adobe FiveK; IntHarmony,MIT-Adobe 5k; MIT-Adobe 5k (1080p); MIT-Adobe 5k (480p),PSNR; SSIM,N/A,Computer Vision,N/A
Stock Price Prediction,3,EDT; Astock; TRADES-LOB,N/A,N/A,N/A,N/A,N/A
Semantic Frame Parsing,3,MixATIS; MixSNIPS; ProSLU,N/A,N/A,N/A,N/A,N/A
Multilingual Named Entity Recognition,3,UNER v1; WikiNEuRal; XFUND,N/A,N/A,N/A,N/A,N/A
Video Matting,3,PhotoMatte85; VideoMatting108; VideoMatte240K,N/A,N/A,N/A,Computer Vision,Image credit: [https://arxiv.org/pdf/2012.07810v1.pdf](https://arxiv.org/pdf/2012.07810v1.pdf)
Camera Auto-Calibration,3,CVGL Camera Calibration Dataset; BrnoCompSpeed; OV,N/A,N/A,N/A,Computer Vision,N/A
Thermal Infrared Pedestrian Detection,3,NII-CU MAPD; InfraParis; LLVIP,N/A,N/A,N/A,N/A,N/A
Reading Order Detection,3,opendataset; ROOR; ReadingBank,ROOR; ReadingBank,Segment-level F1; Average Relative Distance (ARD); Average Page-level BLEU,N/A,Natural Language Processing,Reading order detection aims to capture the word sequence which can be naturally comprehended by human readers from visually-rich documents. It is a fundamental task for visually-rich document underst...
Scientific Concept Extraction,3,SemOpenAlex; SemEval-2021 Task-11; unarXive,N/A,N/A,N/A,N/A,N/A
3D Geometry Perception,3,OmniObject3D; FEE Corridor; EUEN17037_Daylight_and_View_Standard_TestDataSet,N/A,N/A,N/A,N/A,N/A
2D Semantic Segmentation task 3 (25 classes),3,IBISCape; CaDIS; ((Claim~in-easy~way))How to make a claim against Expedia?,CaDIS,Mean IoU (test); Mean IoU (val),Document Enhancement,Computer Vision,N/A
Aspect-Category-Opinion-Sentiment Quadruple Extraction,3,Restaurant-ACOS; DiaASQ; Laptop-ACOS,Restaurant-ACOS; Laptop-ACOS,F1,Conversational Sentiment Quadruple Extraction,Natural Language Processing,Aspect-Category-Opinion-Sentiment (ACOS) Quadruple Extraction is the task with the goal to extract all aspect-category-opinion-sentiment quadruples in a review sentence. ( and provide full support for...
Multiobjective Optimization,3,RSM-based multi-objective optimization using desirability functions; Database of axial impact simulations of the crash box; Deep Sea Treasure Pareto-Front,N/A,N/A,Crashworthiness optimisation,Methodology,"Multi-objective optimization (also known as multi-objective programming, vector optimization, multicriteria optimization, multiattribute optimization or Pareto optimization) is an area of multiple cri..."
3D Shape Retrieval,3,LAS&T: Large Shape & Texture Dataset; HUMAN4D; SketchyVR,N/A,N/A,N/A,N/A,N/A
Parameter Prediction,3,DeepNets-1M; DrivAerNet; MODIS AOD (imputed),CIFAR10,Classification Accuracy (Deep); Classification Accuracy (Dense); Classification Accuracy (ID-test); Classification Accuracy (BN-free); Classification Accuracy (ResNet-50); Classification Accuracy (ViT); Classification Accuracy (Wide),N/A,Miscellaneous,N/A
Unfairness Detection,3,FairFD; Multilingual Terms of Service; DemogPairs,N/A,N/A,N/A,N/A,N/A
Extractive Summarization,3,Bengali Curated News Summary Dataset; OpenDebateEvidence; SubSumE,N/A,N/A,N/A,N/A,N/A
Key Point Matching,3,FewSOL; ENRICH; ArgKP-2021,N/A,N/A,N/A,N/A,N/A
Portrait Segmentation,3,PP-HumanSeg14K; P3M-10k; EasyPortrait,N/A,N/A,N/A,N/A,N/A
Underwater Image Restoration,3,LSUI; MVK; Underwater Object Detection Dataset,N/A,N/A,N/A,N/A,N/A
Transparent Object Depth Estimation,3,SuperCaustics; TransProteus; TransCG,N/A,N/A,N/A,N/A,N/A
Atrial Fibrillation Detection,3,AF Classification from a Short Single Lead ECG Recording - The PhysioNet Computing in Cardiology Challenge 2017; IRIDIA-AF; CPSC2021,N/A,N/A,N/A,N/A,N/A
Joint Event and Temporal Relation Extraction,3,2012 i2b2 Temporal Relations; Spanish TimeBank 1.0; TimeBankPT,TB-Dense,Event Detection F-score,N/A,N/A,N/A
Aspect Category Polarity,3,FABSA; AWARE; Casino Reviews,AWARE,Accuracy (%),N/A,Natural Language Processing,N/A
Speech Denoising,3,WHAMR_ext; mDRT; EmoSpeech,LRS2+VGGSound; LRS3+VGGSound,CSIG; COVL; STOI; CBAK; PESQ,N/A,Speech,Obtain the clean speech of the target speaker by suppressing the background noise. Recent representative github platform [ClearerVoice-Studio](https://github.com/modelscope/ClearerVoice-Studio)
Inductive Link Prediction,3,ILPC22-Large; Wikidata5M-SI; ILPC22-Small,N/A,N/A,N/A,N/A,N/A
Video Domain Adapation,3,ActorShift; RoCoG-v2; Human-Animal-Cartoon,N/A,N/A,N/A,N/A,N/A
Line Detection,3,UrduDoc; NKL; SEL,NKL; SEL,AUC_F; F_measure (EA); HIoU,N/A,Computer Vision,N/A
Speech Extraction,3,WHAMR_ext; WSJ0-2mix-extr; MC_GRID,N/A,N/A,N/A,N/A,N/A
Animal Action Recognition,3,CVB; Animal Kingdom; LoTE-Animal,N/A,N/A,cow identification,Computer Vision,"Cross-species (intra-class, inter-class) action recognition"
Multi-Animal Tracking with identification,3,CVB; 3D-POP; Animals-10,N/A,N/A,N/A,N/A,N/A
Zero-shot Text Retrieval,3,Flickr30k-CNA; simco-comco; WebLI,N/A,N/A,N/A,N/A,N/A
Point Cloud Segmentation,3,WaterScenes; WildScenes; PointCloud-C,PointCloud-C,mean Corruption Error (mCE),N/A,Computer Vision,"3D point cloud segmentation is the process of  classifying point clouds into multiple homogeneous regions, the  points in the same region will have the same properties. The  segmentation is challengin..."
Audio Emotion Recognition,3,Soundscape Attributes Translation Project (SATP) Dataset; ARAUS; nEMO,N/A,N/A,N/A,N/A,N/A
HDR Reconstruction,3,SI-HDR; MSU HDR Video Reconstruction Benchmark; HDRT,N/A,N/A,Multi-Exposure Image Fusion,Computer Vision,N/A
Dialect Identification,3,ArSarcasm; FreCDo; ArSarcasm-v2,N/A,N/A,N/A,N/A,N/A
Blind Image Quality Assessment,3,PIQ23; MSU NR VQA Database; UHD-IQA,N/A,N/A,N/A,N/A,N/A
Photoplethysmography (PPG) beat detection,3,MIMIC PERform Testing Dataset; MMSE-HR; UBFC-rPPG,N/A,N/A,N/A,N/A,N/A
3D Volumetric Reconstruction,3,NMC Li-ion Battery Cathode Energies and Charge Densities; Ethylene Carbonate Molecular Dynamics; HIV (Human Immunodeficiency Virus),N/A,N/A,N/A,N/A,N/A
Multimodal Intent Recognition,3,MIntRec; PhotoChat; MMDialog,N/A,N/A,N/A,N/A,N/A
Extractive Question-Answering,3,CodeQueries; HR Extractive Question Answering; M2QA,N/A,N/A,N/A,N/A,N/A
Crop Yield Prediction,3,SICKLE; CropAndWeed; SemanticSugarBeets,SICKLE; 2018 Syngenta (2016 val),RMSE; MAPE (%),N/A,Miscellaneous; Computer Vision,N/A
Low-Dose X-Ray Ct Reconstruction,3,X3D; A collection of 131 CT datasets of pieces of modeling clay containing stones; 2DeteCT,N/A,N/A,N/A,N/A,N/A
Organ Segmentation,3,Extended Task10_Colon Medical Decathlon; BTCV; CUTS,N/A,N/A,N/A,N/A,N/A
Robust 3D Semantic Segmentation,3,nuScenes-C; WOD-C; SemanticKITTI-C,N/A,N/A,N/A,N/A,N/A
Text Detection,3,UrduDoc; Media-Text; BFRD,UrduDoc,Recall; Precision,N/A,Computer Vision,Detecting the text in the image and localise it using a bounding box. The text can be in any shape and size. We need to localise all such instances of text in the entire image along with bounding box ...
Zero Shot Segmentation,3,TomoSAM; Segmentation in the Wild; MatSeg,Segmentation in the Wild; ADE20K training-free zero-shot segmentation,Mean AP; mIoU,N/A,Computer Vision,N/A
Open Intent Detection,3,OOS_CG; Banking_CG; StackOverflow_CG,N/A,N/A,N/A,N/A,N/A
LLM-generated Text Detection,3,WaterBench; GenSC-6G; OUTFOX,N/A,N/A,N/A,N/A,N/A
Video-Adverb Retrieval (Unseen Compositions),3,VATEX Adverbs; ActivityNet Adverbs; MSR-VTT Adverbs,N/A,N/A,N/A,N/A,N/A
Robot Pose Estimation,3,ConSLAM; SLAM2REF; DREAM-dataset,N/A,N/A,N/A,N/A,N/A
Image to 3D,3,Coil100-Augmented; Bone Fracture Multi-Region X-ray Dataset; VBR,N/A,N/A,N/A,Computer Vision,N/A
Audio Deepfake Detection,3,FakeMusicCaps; ASVspoof 2021; SONICS,N/A,N/A,N/A,N/A,N/A
Camera Pose Estimation,3,ConSLAM; SLAM2REF; KITTI Odometry Benchmark,KITTI Odometry Benchmark,Average Rotational Error er[%]; Absolute Trajectory Error [m]; Average Translational Error et[%],Panorama Pose Estimation (N-view),Computer Vision,Camera pose estimation is a crucial task in computer vision and robotics that involves determining the position and orientation (pose) of a camera relative to a given reference frame. This task is ess...
Attribute Mining,3,AE-110k; MAVE; OA-Mine - annotations,AE-110k; MAVE; OA-Mine - annotations,F1-score,N/A,Natural Language Processing,"The attribute mining task assumes an open-world setting in which novel attributes and their values are extracted from free text.  The task is mainly studied in the e-commerce context, where new attrib..."
Noisy Semantic Image Synthesis,3,noisy-ADE20K-DS; noisy-ADE20K-Edge; noisy-ADE20K-Random,N/A,N/A,N/A,N/A,N/A
Key-value Pair Extraction,3,RFUND; RFUND-EN; SIBR,N/A,N/A,N/A,N/A,N/A
Open-Domain Subject-to-Video,3,OpenS2V-5M; CoIR; OpenS2V-Eval,N/A,N/A,N/A,N/A,N/A
"1 Image, 2*2 Stitchi",3,FQL-Driving; [[Human!!!Support]] How do I get a human at Expedia?; [FaQ-s::Expert-Guide]How do I speak to an Expedia agent?,FQL-Driving,0..5sec,Pose Estimation; Virtual Try-on; Pneumonia Detection; Image Deblurring; Paper generation,Reasoning; Robots; Time Series; Speech; Computer Vision; Audio; Knowledge Base; Music; Medical; Natural Language Processing,N/A
Image to text,3,Urdu Text Scene Images; PsOCR; CII-Bench,N/A,N/A,N/A,N/A,N/A
Medical Image Analysis,3,ICIAR 2018 Grand Challenge on Breast Cancer Histology Images; MP-IDB; Liver-US,N/A,N/A,Registration Of Sparse Clinical Images; Breast Mass Segmentation In Whole Mammograms; Nuclei Classification; Deformable Medical Image Registration; Colon Cancer Detection In Confocal Laser Microscopy Images,Medical,N/A
RGB-D Instance Segmentation,3,NYUDv2-IS; Box-IS; SUN-RGBD-IS,N/A,N/A,N/A,N/A,N/A
Antibody-antigen binding prediction,3,PECAN; Paragraph Expanded; MIPE,N/A,N/A,N/A,N/A,N/A
Sentence Retrieval,3,NitiBench; PeerQA; WangchanX-Legal-ThaiCCL-RAG,N/A,N/A,N/A,N/A,N/A
Nested Term Extraction,3,RuTermEval (Track 1); RuTermEval (Track 2); RuTermEval (Track 3),N/A,N/A,N/A,N/A,N/A
Nested Term Recognition from Flat Supervision,3,RuTermEval (Track 1); RuTermEval (Track 2); RuTermEval (Track 3),N/A,N/A,N/A,N/A,N/A
Unsupervised Image-To-Image Translation,2,MNIST; Freiburg Forest,Freiburg Forest Dataset; SVNH-to-MNIST,PSNR; Classification Accuracy,Sensor Modeling,Computer Vision,"Unsupervised image-to-image translation is the task of doing image-to-image translation without ground truth image-to-image pairings.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Unpaire..."
Superpixel Image Classification,2,MNIST; MPI FAUST Dataset,N/A,N/A,N/A,N/A,N/A
Semantic Textual Similarity within Bi-Encoder,2,MRPC; GLUE,N/A,N/A,N/A,N/A,N/A
Constituency Grammar Induction,2,PTB Diagnostic ECG Database; Penn Treebank,N/A,N/A,N/A,N/A,N/A
Unsupervised Object Localization,2,COCO (Common Objects in Context); PASCAL VOC 2007,N/A,N/A,N/A,N/A,N/A
One-Shot Object Detection,2,COCO (Common Objects in Context); SKU110K,N/A,N/A,N/A,N/A,N/A
One-Shot Instance Segmentation,2,COCO (Common Objects in Context); UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
Few Shot Open Set Object Detection,2,COCO (Common Objects in Context); MSCOCO,MSCOCO,AR_U,N/A,Computer Vision,N/A
Lip to Speech Synthesis,2,GLips; LRW,LRW,ESTOI; STOI; PESQ,Speaker-Specific Lip to Speech Synthesis,Computer Vision,"Given a silent video of a speaker, generate the corresponding speech that matches the lip movements."
Landmark-based Lipreading,2,LRS2; LRW,N/A,N/A,N/A,N/A,N/A
Birds Eye View Object Detection,2,KITTI; SILK,KITTI Cars Hard val; KITTI Pedestrian; KITTI Cars Moderate; KITTI Cyclists Moderate; KITTI Cyclist Hard val; KITTI Cars Moderate val; KITTI Cars Easy val; KITTI Pedestrian Hard; KITTI Pedestrians Easy; KITTI Pedestrian Hard val,Average Precision; mAP; AP,N/A,Computer Vision,KITTI birds eye view detection task
Monocular Cross-View Road Scene Parsing(Road),2,KITTI; Argoverse,N/A,N/A,N/A,N/A,N/A
Monocular Cross-View Road Scene Parsing(Vehicle),2,KITTI; Argoverse,N/A,N/A,N/A,N/A,N/A
Image to Point Cloud Registration,2,KITTI; VBR,N/A,N/A,N/A,N/A,N/A
Self-supervised Video Retrieval,2,HMDB51; UCF101,N/A,N/A,N/A,N/A,N/A
Length-of-Stay prediction,2,Clinical Admission Notes from MIMIC-III; MIMIC-III,Clinical Admission Notes from MIMIC-III; MIMIC-III,Accuracy (LOS>7 Days); AUROC; Accuracy (LOS>3 Days),Remaining Length of Stay,Medical,N/A
Multi-label Image Recognition with Partial Labels,2,Visual Genome; PASCAL VOC 2007,N/A,N/A,N/A,N/A,N/A
Unsupervised semantic parsing,2,Visual Genome; WebNLG,N/A,N/A,N/A,N/A,N/A
Bird Species Classification With Audio-Visual Data,2,BIRDeep; CUB-200-2011,N/A,N/A,N/A,N/A,N/A
Zero-shot Relation Triplet Extraction,2,Wiki-ZSL; FewRel,N/A,N/A,N/A,N/A,N/A
Video Story QA,2,MovieQA; CinePile: A Long Video Question Answering Dataset and Benchmark,N/A,N/A,N/A,N/A,N/A
Handwritten Chinese Text Recognition,2,ICDAR 2013; CASIA-HWDB,N/A,N/A,N/A,N/A,N/A
Distractor Generation,2,SciQ; RACE,N/A,N/A,N/A,N/A,N/A
Few Shot Temporal Action Localization,2,ActivityNet; THUMOS14,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Action Detection,2,ActivityNet; THUMOS14,N/A,N/A,N/A,N/A,N/A
Zero-Shot Action Detection,2,ActivityNet; THUMOS14,N/A,N/A,N/A,N/A,N/A
One-stage Anchor-free Oriented Object Detection,2,HRSC2016; SKU110K-R,N/A,N/A,N/A,N/A,N/A
Subjectivity Analysis,2,SUBJ; Czech Subjectivity Dataset,SUBJ; Czech Subjectivity Dataset,Accuracy,N/A,Natural Language Processing,A related task to sentiment analysis is the subjectivity analysis with the goal of labeling an opinion as either subjective or objective.
Traffic Object Detection,2,Car datasets in multiple scenes; BDD100K,N/A,N/A,N/A,N/A,N/A
Zero-Shot Human-Object Interaction Detection,2,HICO-DET; HICO,N/A,N/A,N/A,N/A,N/A
Generic Event Boundary Detection,2,Kinetics; TAPOS,N/A,N/A,N/A,N/A,N/A
Speaker-Specific Lip to Speech Synthesis,2,GRID Dataset; TIMIT,N/A,N/A,N/A,N/A,N/A
Group Activity Recognition,2,Volleyball; Collective Activity,N/A,N/A,N/A,N/A,N/A
Fine-Grained Action Detection,2,MultiSports; GTEA,N/A,N/A,N/A,N/A,N/A
Image Rescaling,2,DIV2K; Set5,N/A,N/A,N/A,N/A,N/A
3D Canonical Hand Pose Estimation,2,STB; HO-3D v2,RHP; Ego3DHands; STB,AUC,N/A,Computer Vision,Image: [Lin et al](https://arxiv.org/pdf/2006.01320v1.pdf)
3D Shape Reconstruction From A Single 2D Image,2,LLFF; ApolloCar3D,LLFF; ApolloCar3D,CLIP; A3DP,Shape from Texture,Computer Vision,Image: [Liao et al](https://arxiv.org/pdf/1811.12016v1.pdf)
3D Car Instance Understanding,2,CarFusion; ApolloCar3D,N/A,N/A,N/A,N/A,N/A
Highlight Detection,2,QVHighlights; TvSum,QVHighlights; YouTube Highlights; TvSum; arabiska,0..5sec; Hit@1; mAP,N/A,Computer Vision,https://youtu.be/pJ0auP7dbcY?si=vSiZevfJ57YUKC2q
Continual Semantic Segmentation,2,ScanNet; ADE20K,ScanNet; ADE20K; PASCAL VOC 2012,mIoU,Overlapped 5-3; Overlapped 25-25,Computer Vision,Continual learning in semantic segmentation.
Weakly-supervised 3D Human Pose Estimation,2,Human3.6M; MPI-INF-3DHP,N/A,N/A,N/A,N/A,N/A
Unsupervised 3D Human Pose Estimation,2,Human3.6M; MPI-INF-3DHP,N/A,N/A,N/A,N/A,N/A
Disguised Face Verification,2,DFW; MegaFace,N/A,N/A,N/A,N/A,N/A
Age-Invariant Face Recognition,2,MORPH; FG-NET,N/A,N/A,N/A,N/A,N/A
Dichotomous Image Segmentation,2,DIS5K; STARE,N/A,N/A,N/A,N/A,N/A
Lung Nodule Segmentation,2,LUNA; LIDC-IDRI,N/A,N/A,N/A,N/A,N/A
Graph structure learning,2,ChEMBL; Cora,N/A,N/A,N/A,N/A,N/A
Ancestor-descendant prediction,2,WN18; WN18RR,WN18RR,mAP-50%; mAP-0%; mAP-100%,N/A,Graphs,"Given two entities, make a binary prediction if they have ancestor-descendant relationship, based on existing and missing hierarchical edges in the graph."
Aspect-Based Sentiment Analysis,2,FABSA; SemEval-2014 Task-4,N/A,N/A,N/A,N/A,N/A
Aspect-oriented  Opinion Extraction,2,Casino Reviews; SemEval-2014 Task-4,N/A,N/A,N/A,N/A,N/A
Curved Text Detection,2,UrduDoc; SCUT-CTW1500,N/A,N/A,N/A,N/A,N/A
Brain Image Segmentation,2,BRISC; CREMI,N/A,N/A,N/A,N/A,N/A
Link Sign Prediction,2,Epinions; Slashdot,Epinions; Bitcoin-Alpha; Slashdot,Accuracy; Macro-F1; AUC,N/A,Graphs,N/A
Resynthesis,2,LJSpeech; LibriSpeech,N/A,N/A,N/A,N/A,N/A
One-Shot Segmentation,2,UMD-i Affrodance Dataset; Cluttered Omniglot,Cluttered Omniglot,IoU [256 distractors]; IoU [4 distractors]; IoU [32 distractors],Patient-Specific Segmentation,Computer Vision,"<span style=""color:grey; opacity: 0.6"">( Image credit: [One-Shot Learning for Semantic  Segmentation](https://arxiv.org/pdf/1709.03410v1.pdf) )</span>"
Self-supervised Skeleton-based Action Recognition,2,NTU RGB+D 120; NTU RGB+D,N/A,N/A,N/A,N/A,N/A
Zero-Shot Video-Audio Retrieval,2,YouCook2; MSR-VTT,N/A,N/A,N/A,N/A,N/A
3D Multi-Person Pose Estimation (absolute),2,Relative Human; MuPoTS-3D,MuPoTS-3D,MPJPE; 3DPCK,N/A,Computer Vision,This task aims to solve absolute 3D multi-person pose Estimation (camera-centric coordinates). No ground truth human bounding box and human root joint coordinates are used during testing stage.    <sp...
Prosody Prediction,2,Helsinki Prosody Corpus; CANDOR Corpus,N/A,N/A,N/A,N/A,N/A
Pneumonia Detection,2,Chest X-ray images; ChestX-ray14,COVID-19 CXR Dataset; Chest X-ray images; ChestX-ray14,Params; Accuracy; FLOPS; F-Score; AUROC,N/A,Medical,N/A
Table-based Fact Verification,2,InfoTabS; TabFact,TabFact,Val; Test,N/A,Natural Language Processing,Verifying facts given semi-structured data.
Cross-domain 3D Human Pose Estimation,2,MPI-INF-3DHP; 3DPW,N/A,N/A,N/A,N/A,N/A
HD semantic map learning,2,TbV Dataset; nuScenes,Argoverse2; nuScenes,Frechet AP; Chamfer AP,N/A,Computer Vision,"The goal of task is to generate map elements in a vectorized form using data from onboard sensors, e.g., RGB cameras and/or LiDARs. These map elements include but are not limited to : Road boundaries,..."
Motion Detection,2,nuScenes; KITTI'15 MSplus,nuScenes,F1 (%),N/A,Computer Vision,**Motion Detection** is a process to detect the presence of any moving entity in an area of interest. Motion Detection is of great importance due to its application in various areas such as surveillan...
satellite image super-resolution,2,PROBA-V; WorldStrat,N/A,N/A,vehicle detection,Computer Vision,N/A
Arrhythmia Detection,2,The China Physiological Signal Challenge 2018; MIT-BIH Arrhythmia Database,N/A,N/A,N/A,N/A,N/A
Weakly Supervised Action Segmentation (Transcript),2,EgoProceL; Breakfast,N/A,N/A,N/A,N/A,N/A
Multiple Object Tracking with Transformer,2,MOTChallenge; MOT20,N/A,N/A,N/A,N/A,N/A
Image Retrieval with Multi-Modal Query,2,MIT-States; Fashion IQ,MIT-States; FashionIQ; Fashion200k,Recall@10; Recall@5; Recall@1; Recall@50,Cross-Modal Retrieval; Zero-Shot Cross-Modal Retrieval; Multi-Modal Person Identification; Cross-Modal Information Retrieval,Miscellaneous,"The problem of retrieving images from a database based on a multi-modal (image- text) query. Specifically, the query text prompts some modification in the query image and the task is to retrieve image..."
Chinese Semantic Role Labeling,2,CoNLL-2009; CoNLL,N/A,N/A,N/A,N/A,N/A
Zero-shot dense video captioning,2,ViTT; YouCook2,N/A,N/A,N/A,N/A,N/A
Text-Line Extraction,2,UrduDoc; DIVA-HisDB,N/A,N/A,N/A,N/A,N/A
3D Object Reconstruction From A Single Image,2,3D-POP; BUFF,N/A,N/A,N/A,N/A,N/A
Scanpath prediction,2,FixaTons; CapMIT1003,FixaTons; CapMIT1003; Coutrot Dataset 1,Scaled time-delay embeddings; SBTDE; String-edit distance,N/A,Computer Vision,Learning to Predict Sequences of Human Fixations.
Document Enhancement,2,StainDoc; DocUNet,N/A,N/A,N/A,N/A,N/A
Camouflaged Object Segmentation with a Single Task-generic Prompt,2,CAMO; COD10K,N/A,N/A,N/A,N/A,N/A
Fine-Grained Urban Flow Inference,2,PeMS04; TaxiBJ,TaxiBJ-P2; TaxiBJ-P4; TaxiBJ-P1; TaxiBJ-P3,MAE; MSE ; MAPE; MSE,N/A,Miscellaneous,Fine-grained urban flow inference (FUFI) aims to infer the fine-grained urban flow map from the coarse-grained one.
Co-Salient Object Detection,2,CAT: Context Adjustment Training; CoSal2015,N/A,N/A,N/A,N/A,N/A
Single-object colocalization,2,Object Discovery; PASCAL VOC,N/A,N/A,N/A,N/A,N/A
Action Assessment,2,UI-PRMD; EHE,UI-PRMD; EHE; KIMORE,Prediction Accuracy,N/A,Computer Vision,N/A
Dialog Relation Extraction,2,DDRel; DialogRE,N/A,N/A,N/A,N/A,N/A
Zero-Shot Transfer 3D Point Cloud Classification,2,ModelNet; ScanObjectNN,N/A,N/A,N/A,N/A,N/A
3D Point Cloud Linear Classification,2,ModelNet; ScanObjectNN,ModelNet40; ScanObjectNN,Overall Accuracy,N/A,Computer Vision,Training a linear classifier(e.g. SVM) on the embeddings/representations of 3D point clouds. The embeddings/representations are usually trained in an unsupervised manner.
Training-free 3D Point Cloud Classification,2,ModelNet; ScanObjectNN,ModelNet40; ScanObjectNN,Parameters; Accuracy (%); Need 3D Data?,N/A,Computer Vision,Evaluation on target datasets for 3D Point Cloud Classification without any training
3D Parameter-Efficient Fine-Tuning for Classification,2,ModelNet; ScanObjectNN,N/A,N/A,N/A,N/A,N/A
Conditional Text-to-Image Synthesis,2,COCO-MIG; MIMIC-CXR,N/A,N/A,N/A,N/A,N/A
Query-Based Extractive Summarization,2,DebateSum; SubSumE,N/A,N/A,N/A,N/A,N/A
Inductive logic programming,2,Kinship; RuDaS,RuDaS,H-Score; R-Score,N/A,Methodology,N/A
Single-Image Portrait Relighting,2,Multi-PIE; Dynamic OLAT Dataset,Multi-PIE,Si-MSE; Si-L2,N/A,Computer Code,N/A
Fact-based Text Editing,2,RotoEdit; WebEdit,N/A,N/A,N/A,N/A,N/A
Multi-domain Dialogue State Tracking,2,MultiWOZ; SGD,N/A,N/A,N/A,N/A,N/A
Iris Segmentation,2,UBIRIS.v2; CASIA-Iris-Complex,N/A,N/A,N/A,N/A,N/A
Foggy Scene Segmentation,2,ACDC (Adverse Conditions Dataset with Correspondences); Foggy Cityscapes,foggy zurich; ACDC (Adverse Conditions Dataset with Correspondences); Cityscapes-to-FoggyDriving; Foggy Cityscapes,mIoU; mIoU (val),N/A,N/A,N/A
Source Free Object Detection,2,InBreast; Foggy Cityscapes,N/A,N/A,N/A,N/A,N/A
Unsupervised Few-Shot Image Classification,2,mini-Imagenet; tieredImageNet,N/A,N/A,N/A,N/A,N/A
zero-shot anomaly detection,2,MVTecAD; VisA,N/A,N/A,N/A,N/A,N/A
Scene Classification (unified classes),2,SUN RGB-D; NYUv2,N/A,N/A,N/A,N/A,N/A
Robust Semi-Supervised RGBD Semantic Segmentation,2,2D-3D-S; SUN RGB-D,N/A,N/A,N/A,N/A,N/A
Generative 3D Object Classification,2,ModelNet; Objaverse,ModelNet40; Objaverse,Objaverse (C); Objaverse (Average); ModelNet40 (C); Objaverse (I); ModelNet40 (I); ModelNet40 (Average),N/A,Computer Vision,"The task of generative 3D object classification involves prompting the model to generate the object type from its point cloud, distinguishing it from discriminative models that directly classify objec..."
Single Image Haze Removal,2,RESIDE; D-HAZY,N/A,N/A,N/A,N/A,N/A
Depth Map Super-Resolution,2,Middlebury 2005; RGB-D-D,N/A,N/A,N/A,N/A,N/A
Prediction Intervals,2,aGender; Friedman1,N/A,N/A,N/A,Miscellaneous,"A prediction interval is an estimate of an interval in which a future observation will fall, with a certain probability, given what has already been observed. Prediction intervals are often used in re..."
Zero-shot Text to Audio Retrieval,2,Clotho; AudioCaps,N/A,N/A,N/A,N/A,N/A
Diffeomorphic Medical Image Registration,2,PPMI; ACDC,N/A,N/A,N/A,N/A,N/A
Liver Segmentation,2,LiTS17; Radio-Freqency Ultrasound volume dataset for pre-clinical liver tumors,N/A,N/A,N/A,N/A,N/A
Extractive Document Summarization,2,SubSumE; CNN/Daily Mail,N/A,N/A,N/A,N/A,N/A
Sequential sentence segmentation,2,CoNLL 2017 Shared Task - Automatically Annotated Raw Texts and Word Embeddings; CoNLL,N/A,N/A,N/A,N/A,N/A
Sentence Compression,2,Sentence Compression; Google,Google Dataset,F1; CR,Unsupervised Sentence Compression,Natural Language Processing,**Sentence Compression** is the task of reducing the length of text by removing non-essential content while preserving important facts and grammaticality.
Unseen Object Instance Segmentation,2,WISDOM; OCID,N/A,N/A,N/A,N/A,N/A
Nonhomogeneous Image Dehazing,2,SMOKE; NH-HAZE,N/A,N/A,N/A,N/A,N/A
Video Forensics,2,DeeperForensics-1.0; EarthNet2021,N/A,N/A,N/A,Computer Vision,N/A
Sleep spindles detection,2,MODA dataset; DREAM,DREAMS sleep spindles,MCC,N/A,Time Series,N/A
Product Categorization,2,SCG; Atlas,N/A,N/A,N/A,N/A,N/A
Handwritten Word Segmentation,2,BanglaWriting; BN-HTRd,N/A,N/A,N/A,N/A,N/A
Atomic action recognition,2,CATER; PSI-AVA,CATER,Average-mAP,Composite action recognition,Computer Vision,N/A
Semantic Composition,2,Cryptics; Polish CDSCorpus,N/A,N/A,N/A,Natural Language Processing,Understanding the meaning of text by composing the meanings of the individual words in the text (Source: https://arxiv.org/pdf/1405.7908.pdf)
Part-aware Panoptic Segmentation,2,Cityscapes Panoptic Parts; Pascal Panoptic Parts,Cityscapes Panoptic Parts; Pascal Panoptic Parts,PartPQ,N/A,Computer Vision,Panoptic segmentation with part-aware predictions.
Single Image Desnowing,2,AWMM-100k; CSD,N/A,N/A,N/A,N/A,N/A
Fish Detection,2,Fishnet Open Images; DeepFish,N/A,N/A,N/A,N/A,N/A
Heterogeneous Face Recognition,2,DFW; MCXFACE,N/A,N/A,N/A,N/A,N/A
Diabetic Retinopathy Detection,2,Retina Benchmark; Diabetic Retinopathy Detection Dataset,Diabetic Retinopathy Debrecen Data Set,Mean Accuracy,N/A,Medical,N/A
Paper generation (Title-to-abstract),2,"PubMed Term, Abstract, Conclusion, Title Dataset; Elsevier OA CC-BY",N/A,N/A,N/A,N/A,N/A
Paper generation (abstract-to-conclusion),2,"PubMed Term, Abstract, Conclusion, Title Dataset; Elsevier OA CC-BY",N/A,N/A,N/A,N/A,N/A
Multi-View 3D Reconstruction,2,Synthetic Soccer NeRF Dataset; ETH3D,ETH3D,F1 score,N/A,Computer Vision,N/A
Face Age Editing,2,FFHQ-Aging; VGGFace2 HQ,N/A,N/A,N/A,N/A,N/A
Semantic entity labeling,2,FUNSD; EC-FUNSD,FUNSD; EC-FUNSD,F1,N/A,Natural Language Processing,"- One of Form Understanding task (Word grouping, Semantic entity labeling, Entity linking)  - Classifying entities into one of four pre-defined categories: question, answer, header and, other.    cite..."
PointGoal Navigation,2,MIDGARD; Habitat Platform,N/A,N/A,N/A,N/A,N/A
3D Shape Recognition,2,LAS&T: Large Shape & Texture Dataset; Hypersim,N/A,N/A,N/A,N/A,N/A
Image Harmonization,2,iHarmony4; FFHQH,N/A,N/A,N/A,N/A,N/A
Motion Captioning,2,HumanML3D; KIT Motion-Language,KIT Motion-Language; HumanML3D,BERTScore; BLEU-4,N/A,Computer Vision,Generating textual description for human motion.
Active Speaker Detection,2,LRS3-TED; UniTalk,LRS3-TED,Accuracy,Fraud Detection,Robots,N/A
Deception Detection,2,Mafiascum; The Mafia Dataset,N/A,N/A,Deception Detection In Videos,Miscellaneous; Computer Vision,N/A
K-complex detection,2,Montreal Archive of Sleep Studies; MaSS,N/A,N/A,N/A,N/A,N/A
Depth Aleatoric Uncertainty Estimation,2,MUAD; Mid-Air Dataset,N/A,N/A,N/A,N/A,N/A
Head Detection,2,SCUT-HEAD; MinneApple,N/A,N/A,N/A,N/A,N/A
Selection bias,2,ICLR Database; PanNuke,N/A,N/A,N/A,N/A,N/A
Thermal Infrared Object Tracking,2,PTB-TIR; RF100,N/A,N/A,N/A,N/A,N/A
Optic Cup Segmentation,2,REFUGE Challenge; SMDG,REFUGE Challenge,Dice,N/A,Medical,"Optic cup segmentation, concentric with optic disc, useful for glaucoma management (ophthalmology)"
Classify 3D Point Clouds,2,ModelNet40-C; RobustPointSet,N/A,N/A,N/A,N/A,N/A
Micro Expression Recognition,2,SAMM Long Videos; SMIC,N/A,N/A,N/A,N/A,N/A
3D dense captioning,2,ReferIt3D; ScanRefer Dataset,N/A,N/A,N/A,N/A,N/A
Protein Structure Prediction,2,CAMEO; SidechainNet,UniProtSeq; CASPSeq; CASPSimSeq; PaenSeq,Validation perplexity,Protein complex prediction; Protein Interface Prediction,Miscellaneous; Medical,Image credit: [FastFold: Reducing AlphaFold Training Time from 11 Days to 67 Hours](https://arxiv.org/pdf/2203.00854v1.pdf)
Vector Graphics,2,ColorSVG-100K; SPARE3D,N/A,N/A,N/A,N/A,N/A
self-supervised scene text recognition,2,TextSeg; TextZoom,TextSeg; TextZoom; Scene Text Recognition Benchmarks,Average Accuracy; Average PSNR (dB); SSIM; IoU (%),N/A,Computer Vision,scene text recognition task    self-supervised scene text recognition task    text segmentation task    text image super-resolution task
Counterfactual Inference,2,TimeTravel; NVD,N/A,N/A,N/A,N/A,N/A
Factual probe,2,BEAR-probe; T-REx,N/A,N/A,N/A,N/A,N/A
Semi-supervised Medical Image Segmentation,2,ACDC; MM-WHS 2017,N/A,N/A,N/A,N/A,N/A
Object Reconstruction,2,HOWS; A Large Dataset of Object Scans,N/A,N/A,3D Object Reconstruction,Computer Vision,N/A
Visual Social Relationship Recognition,2,PIPA; PISC,PIPA; PISC,Accuracy; mAP; Accuracy (domain); mAP (Coarse),N/A,Computer Vision,N/A
EMG Gesture Recognition,2,NinaPro DB2; Ninapro DB5,N/A,N/A,N/A,Medical,Electromyographic Gesture Recognition
Medical Procedure,2,Clinical Admission Notes from MIMIC-III; MedConceptsQA,Clinical Admission Notes from MIMIC-III,AUROC,N/A,Medical,Predicting medical procedures performed during a hospital admission.
Open Vocabulary Action Recognition,2,Assembly101; EPIC-KITCHENS-100,N/A,N/A,N/A,N/A,N/A
Electron Microscopy Image Segmentation,2,uBench; 3D Platelet EM,N/A,N/A,N/A,N/A,N/A
Pulmorary Vessel Segmentation,2,VESSEL12; Microscopy Image Dataset of Pulmonary Vascular Changes,N/A,N/A,Pulmonary Artery–Vein Classification,Computer Vision,N/A
Semi-supervised Anomaly Detection,2,MVTec LOCO AD; UBI-Fights,UBI-Fights,EER; Decidability; AUC,General Action Video Anomaly Detection; Physical Video Anomaly Detection,Computer Vision,N/A
Commonsense Knowledge Base Construction,2,Quasimodo; Ascent KB,N/A,N/A,N/A,Knowledge Base,N/A
Text-to-video search,2,VALUE; Win-Fail Action Understanding,N/A,N/A,N/A,Natural Language Processing,N/A
Self-Supervised Anomaly Detection,2,KolektorSDD2; ISP-AD,N/A,N/A,N/A,N/A,N/A
Hope Speech Detection,2,HopeEDI; KanHope,HopeEDI; KanHope,F1-score (Weighted); Weighted Average F1-score,Hope Speech Detection for Malayalam; Hope Speech Detection for Tamil; Hope Speech Detection for English,Natural Language Processing,"Detecting speech associated with positive, uplifting,  promise, potential, support, reassurance, suggestions, or inspiration."
Speech Synthesis - Gujarati,2,IndicTTS; ToN_IoT,N/A,N/A,N/A,N/A,N/A
Citation Prediction,2,BEIR; REFCAT,N/A,N/A,N/A,N/A,N/A
Tweet Retrieval,2,BEIR; COVID-19-TweetIDs,N/A,N/A,N/A,N/A,N/A
News Retrieval,2,BEIR; PolyNewsParallel,N/A,N/A,N/A,N/A,N/A
severity prediction,2,Burned Area Delineation from Satellite Imagery; AbuseAnalyzer Dataset,Burned Area Delineation from Satellite Imagery,RMSE,Intubation Support Prediction,Computer Vision,N/A
Anomaly Segmentation,2,RoadAnomaly21; GoodsAD,N/A,N/A,N/A,N/A,N/A
Zero-Shot Audio Retrieval,2,VocSim; AudioCaps,N/A,N/A,N/A,N/A,N/A
drone-based object tracking,2,DroneCrowd; BioDrone,N/A,N/A,N/A,Computer Vision,drone-based object tracking
Partial Point Cloud Matching,2,4DMatch; DeformingThings4D,4DMatch,NFMR; IR,N/A,Computer Vision,N/A
Span-Extraction MRC,2,RuOpinionNE; ExpMRC,N/A,N/A,N/A,N/A,N/A
Graph Reconstruction,2,NBA player performance prediction dataset; Netzschleuder,N/A,N/A,N/A,Graphs,N/A
Dialog Act Classification,2,CPED; SILICONE Benchmark,Switchboard dialogue act corpus,Accuracy (%),N/A,Natural Language Processing,N/A
Time-to-Event Prediction,2,PBC; GBSG2,N/A,N/A,N/A,Time Series,N/A
Joint Vertebrae Identification And Localization In Spinal Ct Images,2,CTSpine1K; Spinal Vertebrae Segmentation Dataset,N/A,N/A,N/A,N/A,N/A
Model extraction,2,Data Collected with Package Delivery Quadcopter Drone; UML Classes With Specs,UML Classes With Specs,Exact Match,N/A,Methodology; Adversarial,"Model extraction attacks, aka model stealing attacks, are used to extract the parameters from the target model. Ideally, the adversary will be able to steal and replicate a model that will have a very..."
Single-Image-Based Hdr Reconstruction,2,SI-HDR; NTIRE 2021 HDR,City Scene Dataset,PSNR; SSIM; HDR-VDP2 Q SCORE,Tone Mapping,Computer Vision,N/A
Sentiment Analysis (Product + User),2,UIT-ViSFD; Synthetic Product Desirability Datasets for Sentiment Analysis Testing,N/A,N/A,N/A,N/A,N/A
Indoor Scene Synthesis,2,Rent3D++; PRO-teXt,N/A,N/A,N/A,N/A,N/A
Time Series Alignment,2,MTic; Artificial signal data for signal alignment testing,N/A,N/A,N/A,Time Series,N/A
Survival Prediction,2,Survival Analysis of Heart Failure Patients; Titanic,N/A,N/A,N/A,N/A,N/A
Emotional Speech Synthesis,2,EMOVIE; EmoDB Dataset,N/A,N/A,N/A,N/A,N/A
Image Similarity Detection,2,DISC21; fruit-SALAD,DISC21 dev,with normalization; Time (ms); hardware; dimension; w/o normalization,N/A,Computer Vision,A fundamental computer vision task to determine whether a part of an image has been copied from another image.    Description from: [The 2021 Image Similarity Dataset and Challenge](https://paperswith...
Image Similarity Search,2,DISC21; fruit-SALAD,N/A,N/A,N/A,Computer Vision,Image credit: [The 2021 Image Similarity Dataset and Challenge](https://paperswithcode.com/paper/the-2021-image-similarity-dataset-and)
3D Multi-Person Mesh Recovery,2,Relative Human; AGORA,N/A,N/A,N/A,N/A,N/A
Multimodal Text Prediction,2,MultiSubs; SciGraphQA,MultiSubs,Accuracy; Word similarity,N/A,Natural Language Processing,"**Multimodal text prediction** is a type of natural language processing that involves predicting the next word or sequence of words in a sentence, given multiple modalities or types of input. In tradi..."
Active Speaker Localization,2,EasyCom; Tragic Talkers,EasyCom,ASL mAP,N/A,Audio,"Active Speaker Localization (ASL) is the process of spatially localizing an active speaker (talker) in an environment using either audio, vision or both."
Factual Visual Question Answering,2,ZS-F-VQA; ViQuAE,N/A,N/A,N/A,N/A,N/A
Image Matching,2,IMC PhotoTourism; ZEB,IMC PhotoTourism; ZEB,Mean AUC@5°; mean average accuracy @ 10,Semantic correspondence; Patch Matching; Matching Disparate Images; set matching,Computer Vision,N/A
Vehicle Speed Estimation,2,VBR; BrnoCompSpeed,BrnoCompSpeed,99-th Percentile Speed Measurement Error (km/h); Mean Speed Measurement Error (km/h); 95-th Percentile Speed Measurement Error (km/h); Median Speed Measurement Error (km/h),N/A,Computer Vision,Vehicle speed estimation is the task of detecting and tracking vehicles whose real-world speeds are then estimated. The task is usually evaluated with recall and precision of the detected vehicle trac...
Fine-Grained Vehicle Classification,2,Vehicle-Rear; UFPR-VCR Dataset,N/A,N/A,Vehicle Color Recognition,Computer Vision,N/A
QRS Complex Detection,2,MIT-BIH Arrhythmia Database; QR Code Detection YOLO,N/A,N/A,N/A,N/A,N/A
Video Visual Relation Detection,2,VidOR; ImageNet-VidVRD,N/A,N/A,N/A,N/A,N/A
Video Visual Relation Tagging,2,VidOR; ImageNet-VidVRD,N/A,N/A,N/A,N/A,N/A
Video scene graph generation,2,STAR Benchmark; ImageNet-VidVRD,ImageNet-VidVRD,Recall@50,N/A,N/A,N/A
Activity Recognition In Videos,2,InfiniteRep; DAHLIA,DogCentric,Accuracy,Activity Prediction,Time Series; Computer Vision,N/A
Low-light Pedestrian Detection,2,LoLI-Street; LLVIP,N/A,N/A,N/A,N/A,N/A
Point Cloud Quality Assessment,2,M-PCCD; WPC,SJTU-PCQA; M-PCCD; WPC,RMSE; Pearson Correlation Coefficient ; KROCC; PLCC; SROCC,N/A,N/A,"### Background  A large and dense collection of points in three-dimensional space, collected by sensors such as LiDAR, is known as a point cloud. Points in the point cloud consist of geometric propert..."
Aspect Sentiment Triplet Extraction,2,ASTE-Data-V2; MuseASTE,N/A,N/A,N/A,N/A,N/A
Document-level Event Extraction,2,ChFinAnn; LEMONADE,N/A,N/A,N/A,N/A,N/A
New Product Sales Forecasting,2,VISUELLE; VISUELLE2.0,N/A,N/A,N/A,N/A,N/A
"Classification of toxic, engaging, fact-claiming comments",2,GermEval; THAR Dataset,N/A,N/A,N/A,N/A,N/A
Zero-Shot Cross-Lingual Visual Reasoning,2,MaRVL; IGLUE,N/A,N/A,N/A,N/A,N/A
Max-Shot Cross-Lingual Visual Reasoning,2,MaRVL; IGLUE,N/A,N/A,N/A,N/A,N/A
Moving Object Detection,2,DVSMOTION20; RF100,N/A,N/A,N/A,N/A,N/A
2D Semantic Segmentation task 1 (8 classes),2,RWD-10K; CaDIS,N/A,N/A,N/A,N/A,N/A
3D Geometry Prediction,2,Molecule3D; DrivAerNet,N/A,N/A,N/A,N/A,N/A
Audio Effects Modeling,2,SignalTrain LA2A Dataset; DiffVox,N/A,N/A,Pitch control; Timbre Interpolation,Audio,"Modeling of audio effects such as reverberation, compression, distortion, etc."
3D Dense Shape Correspondence,2,SHREC'19; TOSCA,N/A,N/A,N/A,N/A,N/A
Acoustic echo cancellation,2,INTERSPEECH 2021 Acoustic Echo Cancellation Challenge; ICASSP 2021 Acoustic Echo Cancellation Challenge,N/A,N/A,N/A,Speech; Medical,N/A
Emotional Dialogue Acts,2,Emotional Dialogue Acts; CPED,N/A,N/A,N/A,N/A,N/A
Video Synchronization,2,MSU Video Alignment and Retrieval Benchmark Suite; VGGSound-Sparse,N/A,N/A,N/A,N/A,N/A
Medical Image Denoising,2,BCNB; Human Protein Atlas,FMD Confocal Fish; FMD Confocal Mice; FMD Two-Photon Mice; Dermatologist level dermoscopy skin cancer classification using different deep learning convolutional neural networks algorithms; Human Protein Atlas Image; LGG Segmentation Dataset,PSNR; Average PSNR; SSIM;  SSIM,N/A,Computer Vision,Image credit: [Learning Medical Image Denoising with Deep Dynamic Residual Attention Network](https://paperswithcode.com/paper/learning-medical-image-denoising-with-deep)
Transparent Object Detection,2,SuperCaustics; TransProteus,N/A,N/A,Transparent objects,Computer Vision,Detecting transparent objects in 2D or 3D
Seismic Detection,2,INSTANCE; Southern California Seismic Network Data,N/A,N/A,N/A,N/A,N/A
Dark Humor Detection,2,Cards Against Humanity; BIG-bench,N/A,N/A,N/A,N/A,N/A
Similarities Abstraction,2,BIG-bench; fruit-SALAD,N/A,N/A,N/A,N/A,N/A
Muscle Tendon Junction Identification,2,deepMTJ; deepMTJ_IEEEtbme,N/A,N/A,N/A,N/A,N/A
3D human pose and shape estimation,2,EgoBody; 3DOH50K,EgoBody,PA-MPJPE; PA-MPVPE; Average MPJPE (mm); MPVPE,N/A,N/A,Estimate 3D human pose and shape (e.g. SMPL) from images
Infrared image super-resolution,2,results-C; results-A,results-C; results-A,Average PSNR,N/A,Computer Vision,Aims at upsampling the IR image and create the high resolution image with help of a low resolution image.
Rgb-T Tracking,2,RGBT234; LasHeR,N/A,N/A,N/A,N/A,N/A
Type prediction,2,ManyTypes4TypeScript; Single Point Corn Yield Data,DeepTyper; ManyTypes4TypeScript; Py150,Average F1; Accuracy@5; Average Accuracy; Average Recall; Average Precision; MRR,N/A,Computer Code,N/A
Kinematic Based Workflow Recognition,2,VIDIMU: Multimodal video and IMU kinematic dataset on daily life activities using affordable devices; PETRAW,PETRAW,Average AD-Accuracy,N/A,Computer Vision,N/A
Tomographic Reconstructions,2,CBCT Walnut; 2DeteCT,N/A,N/A,N/A,N/A,N/A
COVID-19 Tracking,2,SMCOVID19-CT; CIRO experimental results,N/A,N/A,N/A,N/A,N/A
Temporal Metadata Manipulation Detection,2,Cross-View Time Dataset (Cross-Camera Split); Cross-View Time Dataset,N/A,N/A,N/A,N/A,N/A
3D Anomaly Detection and Segmentation,2,MVTEC 3D-AD; DrivAerNet,N/A,N/A,N/A,N/A,N/A
Pancreas Segmentation,2,Pancreas-CT; StepGame,N/A,N/A,N/A,N/A,N/A
Event-based Motion Estimation,2,FE108; COESOT,N/A,N/A,N/A,N/A,N/A
Claim Extraction with Stance Classification (CESC),2,IAM Dataset; Press Briefing Claim Dataset,N/A,N/A,N/A,N/A,N/A
Multiview Contextual Commonsense Inference,2,CICERO; CICEROv2,N/A,N/A,N/A,N/A,N/A
Hyperspectral Image Super-Resolution,2,Multispectral Image Database; HSIRS,N/A,N/A,N/A,N/A,N/A
Mistake Detection,2,Assembly101; ImageNet-X,N/A,N/A,Online Mistake Detection,Computer Vision,Mistakes are natural occurrences in many tasks and an opportunity for an AR assistant to provide help. Identifying such mistakes requires modelling procedural knowledge and retaining long-range sequen...
AbbreviationDetection,2,PLOD-filtered; PLOD-unfiltered,N/A,N/A,N/A,N/A,N/A
Object Categorization,2,UR5 Tool Dataset; GRIT,GRIT,Categorization (ablation); Categorization (test),N/A,Computer Vision,"Object categorization identifies which label, from a  given set, best corresponds to an image region defined by  an input image and bounding box."
Retinal OCT Disease Classification,2,OCTID; RETOUCH,N/A,N/A,N/A,N/A,N/A
Data Visualization,2,PJM(AEP); MIMI dataset,N/A,N/A,Tree Map Layout,Miscellaneous; Methodology; Graphs,N/A
Copy Detection,2,VCSL; STVD-PVCD,N/A,N/A,N/A,N/A,N/A
Audio-visual Question Answering,2,MUSIC-AVQA v2.0; MUSIC-AVQA,MUSIC-AVQA,Acc,AUDIO-VISUAL QUESTION ANSWERING (MUSIC-AVQA-v2.0),Computer Vision,N/A
Weakly-supervised Video Anomaly Detection,2,UBnormal; ShanghaiTech Campus,N/A,N/A,N/A,N/A,N/A
Burst Image Super-Resolution,2,Videezy4K; BurstSR,N/A,N/A,N/A,N/A,N/A
Multi-Oriented Scene Text Detection,2,UrduDoc; Indian Number Plates Dataset | Vehicle Number Plates | English OCR Detection,ICDAR2015,F-Measure,Natural Image Orientation Angle Detection,Computer Vision,N/A
Transparent objects,2,Transparent Object Images | Indoor Object Dataset; TRansPose,N/A,N/A,N/A,N/A,N/A
Dialogue Safety Prediction,2,rt-inod-jailbreaking; ProsocialDialog,N/A,N/A,N/A,N/A,N/A
Amodal Instance Segmentation,2,MOViD-Amodal; WALT,N/A,N/A,N/A,N/A,N/A
Variable Detection,2,SV-Ident; SILD,N/A,N/A,N/A,N/A,N/A
Spam detection,2,Traditional and Context-specific Spam Twitter; ViSpamReviews,Traditional and Context-specific Spam Twitter,Avg F1,Traditional Spam Detection; Context-specific Spam Detection,Natural Language Processing,N/A
Brain Tumor Classification,2,BRISC; Brain Tumor MRI Dataset,N/A,N/A,N/A,N/A,N/A
Motion Generation,2,HumanML3D; NuiSI Dataset,N/A,N/A,N/A,N/A,N/A
Electrocardiography (ECG),2,MIMIC PERform Testing Dataset; MIMIC-IV-ECG,N/A,N/A,Arrhythmia Detection; Heartbeat Classification; ECG Classification; ECG Wave Delineation; Myocardial infarction detection,Medical; Methodology,N/A
Burst Image Reconstruction,2,SC_burst; Videezy4K,N/A,N/A,N/A,N/A,N/A
Training-free Object Counting,2,FSC147; Omnicount-191,N/A,N/A,N/A,N/A,N/A
Skin Lesion Segmentation,2,ISIC2016; University of Waterloo skin cancer database,N/A,N/A,N/A,N/A,N/A
Ad-Hoc Information Retrieval,2,Robust04; SciRepEval,TREC Robust04,P@20; nDCG@20; MAP,Document Ranking,Natural Language Processing,Ad-hoc information retrieval refers to the task of returning information resources related to a user query formulated in natural language.
Semantic Communication,2,MVX; Europarl,Europarl,0..5sec,N/A,N/A,N/A
Drug Response Prediction,2,GDSC; DrugBank,N/A,N/A,N/A,N/A,N/A
Twitter Event Detection,2,Crypto related tweets from 10.10.2020 to 3.3.2021; Brazilian Protest,Events2012 - Oct 11 to Oct 17,Number of Events; Duplicate Event Rate (DERate); Precision,N/A,Natural Language Processing,"Detection of worldwide events from categories like Sports, Politics, Entertainment, Science & Technology, etc. by analyzing Twitter Tweets."
Punctuation Restoration,2,Turkish Punctuation Restoration; LEPISZCZE,N/A,N/A,N/A,Natural Language Processing,Punctuation Restoration
Zero-shot 3D classification,2,OmniObject3D; Objaverse,N/A,N/A,N/A,N/A,N/A
3D Object Captioning,2,Objaverse; TUMTraffic-VideoQA,Objaverse,Precision;  Sentence-BERT; GPT-4; SimCSE; Hallucination; Correctness,N/A,Computer Vision,"3D object captioning involves generating a natural language description of an object, given its point cloud representation."
Emotional Intelligence,2,Cards Against Humanity; EQ-Bench,EQ-Bench,EQ-Bench Score,Ruin Names; SNARKS; Dark Humor Detection,Natural Language Processing,"Emotional Intelligence (EI) is a measure of ""The ability to monitor one’s own and others’ feelings, to discriminate among them, and to use this information to guide one’s thinking and action."" (Salove..."
Depth-aware Video Panoptic Segmentation,2,SemKITTI-DVPS; Cityscapes-DVPS,N/A,N/A,N/A,N/A,N/A
Connectivity Estimation,2,iV2V and iV2I+; Berlin V2X,N/A,N/A,N/A,Graphs,N/A
Audio Synthesis,2,mDRT; Trinity Speech-Gesture Dataset,N/A,N/A,N/A,N/A,N/A
Semi-supervised Medical Image Classification,2,OLIVES Dataset; LymphoMNIST,N/A,N/A,N/A,N/A,N/A
Fake Image Attribution,2,GANGen-Detection; ArtiFact,N/A,N/A,N/A,N/A,N/A
Group Anomaly Detection,2,GADformer Trajectory Datasets; CHAD,N/A,N/A,N/A,N/A,N/A
legal outcome extraction,2,Caselaw4; Reglamento_Aeronautico_Colombiano_2024,N/A,N/A,N/A,N/A,N/A
Hyperspectral Image Denoising,2,Hyper Drive; ICVL-HSI,N/A,N/A,N/A,N/A,N/A
Video-to-image Affordance Grounding,2,OPRA; EPIC-Hotspot,OPRA (28x28); EPIC-Hotspot; OPRA,AUC-J; SIM; KLD; Top-1 Action Accuracy,N/A,Computer Vision,"Given a demonstration video V and a target image I, the goal of video-to-image affordance grounding predict an affordance heatmap over the target image according to the hand-interacted region in the v..."
Robust 3D Object Detection,2,nuScenes-C; KITTI-C,N/A,N/A,N/A,N/A,N/A
audio-visual event localization,2,LongVALE; UnAV-100,UnAV-100, mAP; AP@IOU0.5,N/A,N/A,N/A
Table Retrieval,2,CompMix-IR; Statcan Dialogue Dataset,N/A,N/A,N/A,N/A,N/A
Preference Mapping,2,Pick-a-Pic; Summarize from Feedback,N/A,N/A,N/A,N/A,N/A
3D Point Cloud Interpolation,2,NL-Drive; DHB Dataset,NL-Drive; DHB Dataset,EMD; CD,Point Cloud Registration,Computer Vision,"Point cloud interpolation is a fundamental problem for 3D computer vision. Given a low temporal resolution (frame rate) point cloud sequence, the target of interpolation is to generate a smooth point ..."
Video to Text Retrieval,2,MSVD-Indonesian; Sakuga-42M,N/A,N/A,N/A,N/A,N/A
Irregular Text Recognition,2,UTRSet-Synth; UTRSet-Real,N/A,N/A,N/A,N/A,N/A
Protein Function Prediction,2,"FLIP; FLIP -- AAV, Designed vs mutant",UniProtSeq; CASPSimSeq; PaenSeq,ROUGE-L,Antibody-antigen binding prediction,Medical,"For GO terms prediction, given the specific function prediction instruction and a protein sequence, models characterize the protein functions using the GO terms presented in three different domains (c..."
Occluded Face Detection,2,HEADSET; OCFR-LFW,N/A,N/A,N/A,N/A,N/A
Video-based Generative Performance Benchmarking,2,VideoInstruct; VCG+112K,VideoInstruct,Temporal Understanding; Consistency; Correctness of Information; mean; Contextual Understanding; Detail Orientation,Video-based Generative Performance Benchmarking (Contextual Understanding); Video-based Generative Performance Benchmarking (Detail Orientation)); Video-based Generative Performance Benchmarking (Temporal Understanding); Video-based Generative Performance Benchmarking (Consistency); Video-based Generative Performance Benchmarking (Correctness of Information),Reasoning,The benchmark evaluates a generative Video Conversational Model and covers five key aspects:    - Correctness of Information  - Detailed Orientation  - Contextual Understanding  - Temporal Understandi...
Image Shadow Removal,2,INS Dataset; SD7K,INS Dataset,Average PSNR (dB),N/A,Computer Vision,Merge with the Shadow Removal
Image-guided Story Ending Generation,2,VIST-E; LSMDC-E,N/A,N/A,N/A,N/A,N/A
Zero-shot Composed Person Retrieval,2,GeneCIS; ITCPR dataset,N/A,N/A,N/A,N/A,N/A
visual instruction following,2,LLaVA-Bench; VSTaR-1M,N/A,N/A,N/A,N/A,N/A
Explanatory Visual Question Answering,2,SME; GQA-REX,GQA-REX,SPICE; Grounding; GQA-test; GQA-val; METEOR; CIDEr; ROUGE-L; BLEU-4,FS-MEVQA,Computer Vision,Explanatory Visual Question Answering (EVQA) requires answering visual questions and generating multimodal explanations for the reasoning processes.
Music Performance Rendering,2,ASAP; ATEPP,N/A,N/A,N/A,N/A,N/A
Low-light Image Deblurring and Enhancement,2,LoLI-Street; LOL-Blur,N/A,N/A,N/A,N/A,N/A
X-ray PDF regression,2,CHILI-3K; CHILI-100K,N/A,N/A,N/A,N/A,N/A
named-entity-recognition,2,NuNER; SOMD,N/A,N/A,N/A,N/A,N/A
Multispectral Image Super-resolution,2,Sen2venus; OLI2MSI,N/A,N/A,N/A,N/A,N/A
zero-shot long video question answering,2,SF20K; CinePile: A Long Video Question Answering Dataset and Benchmark,N/A,N/A,N/A,N/A,N/A
3D visual grounding,2,Beacon3D; Mono3DRefer,N/A,N/A,N/A,N/A,N/A
Counterfactual Reasoning,2,ACCORD CSQA 0-5; Vinoground,N/A,N/A,N/A,N/A,N/A
Flood Inundation Mapping,2,Coastal Inundation Maps with Floodwater Depth Values; STURM-Flood,Coastal Inundation Maps with Floodwater Depth Values,Zero detection rate; Average MAE,N/A,Miscellaneous,N/A
Articulated Object modelling,2,RealArt-6; PartNet-Mobility,N/A,N/A,N/A,N/A,N/A
Action Classification (1-shot),2,WiFall; WiGesture,N/A,N/A,N/A,N/A,N/A
Fault localization,2,Discrete-Time Modeling of Interturn Short Circuits in Interior PMSMs - Data and Models; Paderbone University Bearing Fault Benckmark,N/A,N/A,N/A,N/A,N/A
Detecting Image Manipulation,2,CASIA (OSN-transmitted - Weibo); Columbia (OSN-transmitted - Facebook),N/A,N/A,N/A,N/A,N/A
Garment Reconstruction,2,4D-DRESS; GarmentCodeData,N/A,N/A,N/A,N/A,N/A
Marine Animal Segmentation,2,RMAS; MAS3K,N/A,N/A,N/A,Computer Vision,N/A
Mirror Detection,2,MSD (Mirror Segmentation Dataset); PMD,N/A,N/A,N/A,Computer Vision,N/A
Keypoint detection and image matching,2,Niantic Map-free Relocalization Dataset; ZEB,N/A,N/A,N/A,Computer Vision,keypoint detection in retinal images followed by image registration
Causal Discovery in Video Reasoning,2,MECD; CausalChaos!,N/A,N/A,N/A,N/A,N/A
Audio Denoising,2,Biodenoising datasets; Biodenoising_validation,AV-Bench - Wooden Horse; AV-Bench - Guitar Solo; AV-Bench - Violin Yanni,NSDR,N/A,Audio,N/A
Video Deblurring,2,DAVIDE; Beam-Splitter Deblurring (BSD),N/A,N/A,N/A,N/A,N/A
Video deraining,2,Video Waterdrop Removal Dataset; VRDS,Video Waterdrop Removal Dataset; VRDS,PSNR; SSIM,N/A,Computer Vision,N/A
Music Genre Recognition,2,XMIDI; PIAST,chords; 1B Words,Accuracy; 10 Hops,N/A,Music,"Recognizing the genre (e.g. rock, pop, jazz, etc.) of a piece of music."
Text-to-Video Editing,2,V2VBench; DAVIS-Edit,N/A,N/A,N/A,N/A,N/A
Outdoor Localization,2,PEnG; SpaGBOL,N/A,N/A,N/A,Robots,N/A
Synthetic Image Detection,2,SuSy Dataset; Gap Pattern Detection,N/A,N/A,N/A,Computer Vision,Identify if the image is real or generated/manipulated by any generative models (GAN or Diffusion).
Malaria Falciparum Detection,2,M5-Malaria Dataset; MP-IDB,N/A,N/A,N/A,N/A,N/A
Text within image generation,2,TextAtlasEval; TextAtlas5M,N/A,N/A,N/A,N/A,N/A
Water3D_Long,2,Mpm-Verse-Large; MPM-Verse,N/A,N/A,N/A,N/A,N/A
Sand3D_Long,2,Mpm-Verse-Large; MPM-Verse,N/A,N/A,N/A,N/A,N/A
Plasticine3D,2,Mpm-Verse-Large; MPM-Verse,N/A,N/A,N/A,N/A,N/A
3D geometry,2,Mpm-Verse-Large; MPM-Verse,N/A,N/A,N/A,Graphs,N/A
Eyeblink detection,2,MPEblink; HUST-LEBW,MPEblink; HUST-LEBW,Avg. F1 ; Blink-AP50,N/A,Computer Vision,Localize eyeblinks in videos.
Audio-Video Question Answering (AVQA),2,MUSIC-AVQA-R; FortisAVQA,N/A,N/A,N/A,Computer Vision,N/A
Damaged Building Detection,2,mwBTFreddy; BRIGHT,N/A,N/A,N/A,Computer Vision,N/A
Foreground Segmentation,2,FCoT; UIIS10K,N/A,N/A,N/A,N/A,N/A
Image Editing,2,ImgEdit-Data; GEdit-Bench-EN,ImgEdit-Data; GEdit-Bench-EN,Replace; Background; Style; Hybrid; Action; Semantic Consistency; Adjust; Remove; Extract; Perceptual Quality,Multimodel-guided image editing; Rolling Shutter Correction; Shadow Removal; Multimodal fashion image editing; Joint Deblur and Frame Interpolation,Computer Vision,N/A
"1 Image, 2*2 Stitching",2,[[FAQs~Communication]]How Do I Communicate to Expedia?; ####How do i ask a question at Expedia?,N/A,N/A,Fake Image Detection; Image-to-Image Translation,Miscellaneous; Computer Vision,N/A
Malicious Detection,1,MNIST,N/A,N/A,N/A,N/A,N/A
Iloko Speech Recognition,1,MNIST,N/A,N/A,N/A,N/A,N/A
PD-L1 Tumor Proportion Score Prediction,1,MNIST,N/A,N/A,N/A,N/A,N/A
HairColor/Bias-conflicting,1,CelebA,N/A,N/A,N/A,N/A,N/A
HeavyMakeup/Bias-conflicting,1,CelebA,N/A,N/A,N/A,N/A,N/A
JPEG Decompression,1,ImageNet,ImageNet,FID-5K; PD; CA; IS,N/A,Computer Vision,Image credit: [Palette: Image-to-Image Diffusion Models](https://paperswithcode.com/paper/palette-image-to-image-diffusion-models)
Image Classification with Differential Privacy,1,ImageNet,N/A,N/A,N/A,N/A,N/A
Zero-Shot Transfer Image Classification (CN),1,ImageNet,N/A,N/A,N/A,N/A,N/A
Unsupervised face recognition,1,LFW,N/A,N/A,N/A,N/A,N/A
Training-free 3D Part Segmentation,1,ShapeNet,ShapeNet-Part,Need 3D Data?; mIoU; Parameters,N/A,Computer Vision,Evaluation on target datasets for 3D Part Segmentation without any training
Weakly-supervised instance segmentation,1,COCO (Common Objects in Context),COCO test-dev; PASCAL VOC 2012 val; COCO 2017 val,mAP@0.75; AP@M; mAP@0.5; mAP@0.25; AP@L; AP@75; AP@S; Average Best Overlap; AP@50; AP,N/A,Computer Vision,N/A
Image-level Supervised Instance Segmentation,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Point-Supervised Instance Segmentation,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Active Object Detection,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Box-supervised Instance Segmentation,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Activeness Detection,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Generalized Zero-Shot Object Detection,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
3D Single Object Tracking,1,KITTI,N/A,N/A,N/A,N/A,N/A
Open Set Action Recognition,1,UCF101,N/A,N/A,N/A,N/A,N/A
Multi-Label Classification Of Biomedical Texts,1,MIMIC-III,MIMIC-III; MIMIC-III ,1:3 Accuracy; Micro F1,N/A,Medical,N/A
Bidirectional Relationship Classification,1,Visual Genome,N/A,N/A,N/A,N/A,N/A
Unbiased Scene Graph Generation,1,Visual Genome,N/A,N/A,N/A,N/A,N/A
Smile Recognition,1,DISFA,N/A,N/A,N/A,N/A,N/A
Unsupervised Keypoint Estimation,1,CUB-200-2011,N/A,N/A,N/A,N/A,N/A
Image Comprehension,1,Visual7W,N/A,N/A,N/A,Computer Vision,N/A
Clinical Assertion Status Detection,1,2010 i2b2/VA,2010 i2b2/VA,Micro F1,N/A,Natural Language Processing,"Classifying the assertions made on given medical concepts as being present, absent, or possible in the patient, conditionally present in the patient under certain circumstances, hypothetically present..."
GZSL Video Classification,1,ActivityNet,N/A,N/A,N/A,N/A,N/A
ZSL Video Classification,1,ActivityNet,N/A,N/A,N/A,N/A,N/A
Cross-Part Crowd Counting,1,ShanghaiTech,N/A,N/A,N/A,N/A,N/A
Zero-Shot Single Object Tracking,1,LaSOT,N/A,N/A,N/A,N/A,N/A
Drivable Area Detection,1,BDD100K,BDD100K val,mIoU; Params (M),N/A,Computer Vision,The drivable area detection is a subset topic of object detection. The model marks the safe and legal roads for regular driving in color blocks shaped by area.
Multiple Object Track and Segmentation,1,BDD100K,N/A,N/A,N/A,N/A,N/A
Amodal Panoptic Segmentation,1,BDD100K,N/A,N/A,N/A,N/A,N/A
Affordance Recognition,1,HICO-DET,HICO-DET; HICO-DET(Unknown Concepts),Object365; Novel classes; Obj365; HICO; Novel Classes; COCO-Val2017,N/A,Computer Vision,Affordance recognition from Human-Object Interaction
Human-Object Interaction Concept Discovery,1,HICO-DET,HICO-DET,Unknown (AP),N/A,Computer Vision,"Discovering the reasonable HOI concepts/categories from known categories and their instances. Actually, it is also a matrix (verb-object matrix) complementation problem."
Phoneme Recognition,1,TIMIT,N/A,N/A,N/A,N/A,N/A
Jpeg Compression Artifact Reduction,1,DIV2K,N/A,N/A,N/A,N/A,N/A
Multiview Gait Recognition,1,CASIA-B,N/A,N/A,N/A,N/A,N/A
Occluded 3D Object Symmetry Detection,1,YCB-Video,YCB-Video,PR AUC,N/A,Computer Vision,N/A
Vehicle Key-Point and Orientation Estimation,1,ApolloCar3D,ApolloCar3D,A3DP,N/A,Computer Vision,N/A
Car Pose Estimation,1,ApolloCar3D,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Image Classification (Cold Start),1,CIFAR-10,N/A,N/A,N/A,N/A,N/A
Neural Network Compression,1,CIFAR-10,N/A,N/A,N/A,N/A,N/A
Image Classification with Human Noise,1,CIFAR-10,N/A,N/A,N/A,N/A,N/A
Supervised Image Retrieval,1,CIFAR-10,CIFAR-10,Precision@100,N/A,Computer Vision,N/A
Open Vocabulary Panoptic Segmentation,1,ADE20K,ADE20K,PQ,N/A,Computer Vision,N/A
Speech Prompted Semantic Segmentation,1,ADE20K,N/A,N/A,N/A,N/A,N/A
Sound Prompted Semantic Segmentation,1,ADE20K,N/A,N/A,N/A,N/A,N/A
Open-Vocabulary Semantic Segmentation,1,ADE20K,ADE20K-150,N/A,Open-Vocabulary Panoramic Semantic Segmentation,Computer Vision,N/A
Root Joint Localization,1,Human3.6M,N/A,N/A,N/A,N/A,N/A
3D Human Pose Estimation in Limited Data,1,Human3.6M,N/A,N/A,N/A,N/A,N/A
3D Semantic Instance Segmentation,1,ScanNet,N/A,N/A,N/A,N/A,N/A
Interactive 3D Instance Segmentation,1,ScanNet,ScanNetV2,NoC@80,Interactive 3D Instance Segmentation -Trained on Scannet40 - Evaluated on Scannet40,Computer Vision,N/A
Unsupervised 3D Semantic Segmentation,1,ScanNet,N/A,N/A,N/A,N/A,N/A
Interactive 3D Instance Segmentation -Trained on Scannet40 - Evaluated on Scannet40,1,ScanNet,N/A,N/A,N/A,N/A,N/A
Semantic Contour Prediction,1,SBD,N/A,N/A,N/A,N/A,N/A
Object Skeleton Detection,1,SK-LARGE,N/A,N/A,N/A,N/A,N/A
Hyperspectral Image Inpainting,1,Indian Pines,N/A,N/A,N/A,N/A,N/A
Semantic Part Detection,1,PASCAL-Part,N/A,N/A,N/A,N/A,N/A
Age/Bias-conflicting,1,UTKFace,N/A,N/A,N/A,N/A,N/A
Race/Bias-conflicting,1,UTKFace,N/A,N/A,N/A,N/A,N/A
Participant Intervention Comparison Outcome Extraction,1,EBM-NLP,EBM-NLP,F1,N/A,Medical,"PICO recognition is an information extraction task for identifying Participant, Intervention, Comparator, and Outcome (PICO elements) information from clinical literature."
Pose-Based Human Instance Segmentation,1,OCHuman,N/A,N/A,N/A,N/A,N/A
Geometric Matching,1,HPatches,HPatches,Average End-Point Error,N/A,Computer Vision,N/A
Class-agnostic Object Detection,1,Comic2k,N/A,N/A,N/A,N/A,N/A
Relationship Detection,1,VRD,N/A,N/A,N/A,N/A,N/A
Few-shot 3D Point Cloud Semantic Segmentation,1,S3DIS,N/A,N/A,N/A,N/A,N/A
Photo to Rest Generalization,1,PACS,N/A,N/A,N/A,N/A,N/A
Gene Interaction Prediction,1,BioGRID,BioGRID (human); BioGRID(yeast),Average Precision,N/A,Graphs,N/A
3D Multi-Person Pose Estimation (root-relative),1,MuPoTS-3D,MuPoTS-3D,MPJPE; 3DPCK; AUC,N/A,Computer Vision,This task aims to solve root-relative 3D multi-person pose estimation (person-centric coordinate system). No ground truth human bounding box and human root joint coordinates are used during testing st...
Unsupervised 3D Multi-Person Pose Estimation,1,MuPoTS-3D,N/A,N/A,N/A,N/A,N/A
General Action Video Anomaly Detection,1,Something-Something V2,N/A,N/A,N/A,N/A,N/A
One-Shot 3D Action Recognition,1,NTU RGB+D 120,N/A,N/A,N/A,N/A,N/A
Self-Supervised Human Action Recognition,1,NTU RGB+D 120,N/A,N/A,N/A,N/A,N/A
Few-Shot Skeleton-Based Action Recognition,1,NTU RGB+D 120,N/A,N/A,N/A,N/A,N/A
One-shot visual object segmentation,1,YouTube-VOS 2018,N/A,N/A,N/A,N/A,N/A
Multi-view 3D Human Pose Estimation,1,MPI-INF-3DHP,N/A,N/A,N/A,N/A,N/A
Online Vectorized HD Map Construction,1,nuScenes,nuScenes Camera Only,Average mAP,N/A,Computer Vision,N/A
Sleep Arousal Detection,1,MESA,N/A,N/A,N/A,N/A,N/A
Multimodal Sleep Stage Detection,1,Sleep-EDF,N/A,N/A,N/A,N/A,N/A
Myocardial infarction detection,1,PTB Diagnostic ECG Database,N/A,N/A,N/A,N/A,N/A
Lung Nodule 3D Classification,1,LIDC-IDRI,LIDC-IDRI,AUC,Radiomics-based Classification,Medical,N/A
Lung Nodule 3D Detection,1,LIDC-IDRI,N/A,N/A,N/A,N/A,N/A
Multi-view Subspace Clustering,1,ORL,N/A,N/A,N/A,N/A,N/A
Video-to-Video Synthesis,1,Street Scene,N/A,N/A,N/A,N/A,N/A
Real-Time 3D Semantic Segmentation,1,SemanticKITTI,N/A,N/A,N/A,N/A,N/A
Lidar Scene Completion,1,SemanticKITTI,SemanticKITTI,Voxel IoU 0.1m; Voxel IoU 0.5m; JSD 3D; Chamfer Distance; Voxel IoU 0.2m; JSD BEV,N/A,Computer Vision,Obtaining dense scene representation from a sparse lidar point cloud.
Text-to-3D-Human Generation,1,DeepFashion,SHHQ; DeepFashion,Frechet Inception Distance; Percentage of Correct Keypoints; Fashion Accuracy; CLIP Score; Depth Error,N/A,Miscellaneous,3D avatars generation from text prompts
Visual Question Answering (VQA) Split A,1,CLEVR,N/A,N/A,N/A,N/A,N/A
Visual Question Answering (VQA) Split B,1,CLEVR,N/A,N/A,N/A,N/A,N/A
Drone navigation,1,University-1652,N/A,N/A,N/A,N/A,N/A
Weakly Supervised Action Segmentation (Action Set)),1,Breakfast,N/A,N/A,N/A,N/A,N/A
Long-video Activity Recognition,1,Breakfast,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Video Action Detection,1,UCF101-24,N/A,N/A,N/A,N/A,N/A
Long Video Retrieval (Background Removed),1,YouCook2,YouCook2,OTAM R@5; OTAM R@1; OTAM R@10; Cap. Avg. R@10; DTW R@10; Cap. Avg. R@5; DTW R@5; DTW R@1; Cap. Avg. R@1,N/A,Computer Vision,Retrieve the long videos given all subtitles.
3D Shape Classification,1,Pix3D,N/A,N/A,N/A,N/A,N/A
Left Ventricle Segmentation,1,SUN09,N/A,N/A,N/A,N/A,N/A
Live Video Captioning,1,ActivityNet Captions,N/A,N/A,N/A,N/A,N/A
Complimentary Image Retrieval,1,iMaterialist,N/A,N/A,N/A,N/A,N/A
Sentence Embeddings For Biomedical Texts,1,BIOSSES,N/A,N/A,N/A,N/A,N/A
Emotion Cause Extraction,1,ECE,N/A,N/A,N/A,N/A,N/A
Semanticity prediction,1,PhyAAt,N/A,N/A,N/A,N/A,N/A
Attention Score Prediction,1,PhyAAt,N/A,N/A,N/A,N/A,N/A
Noise Level Prediction,1,PhyAAt,N/A,N/A,N/A,N/A,N/A
Method name prediction,1,CodeSearchNet,N/A,N/A,N/A,N/A,N/A
Fine-grained Action Recognition,1,MTL-AQA,N/A,N/A,N/A,N/A,N/A
Vector Graphics Animation,1,SVG-Icons8,N/A,N/A,N/A,N/A,N/A
Unsupervised Vehicle Re-Identification,1,VeRi-776,N/A,N/A,N/A,N/A,N/A
Pedestrian Image Caption,1,CUHK-PEDES,N/A,N/A,N/A,N/A,N/A
Video Corpus Moment Retrieval,1,TVR,N/A,N/A,N/A,N/A,N/A
Zero-shot 3D Point Cloud Classificationclassification,1,ScanObjectNN,N/A,N/A,N/A,N/A,N/A
Supervised Only 3D Point Cloud Classification,1,ScanObjectNN,N/A,N/A,N/A,N/A,N/A
Diabetes Prediction,1,Diabetes,Diabetes,Accuracy,N/A,Medical,N/A
Scene-Aware Dialogue,1,AVSD,AVSD,CIDEr,N/A,Computer Vision,N/A
Causal Emotion Entailment,1,RECCON,N/A,N/A,N/A,N/A,N/A
Online surgical phase recognition,1,Cholec80,N/A,N/A,N/A,N/A,N/A
Offline surgical phase recognition,1,Cholec80,N/A,N/A,N/A,N/A,N/A
Event Cross-Document Coreference Resolution,1,ECB+,N/A,N/A,N/A,N/A,N/A
Directional Hearing,1,VCTK,VCTK,SI-SDRi,Real-time Directional Hearing,Audio,Extremely low-latency audio source separation from a known direction of arrival.
Real-time Directional Hearing,1,VCTK,N/A,N/A,N/A,N/A,N/A
Accented Speech Recognition,1,VoxForge,VoxForge Indian; VoxForge European; VoxForge American-Canadian; VoxForge Commonwealth,Percentage error,Speech Synthesis,Audio,N/A
Crime Prediction,1,Foursquare,N/A,N/A,N/A,Miscellaneous,N/A
Research Performance Prediction,1,AMiner,N/A,N/A,N/A,N/A,N/A
Relationship Extraction (Distant Supervised),1,New York Times Annotated Corpus,NYT; New York Times Corpus,P@200; Average Precision; P@100; P@10%; PR AUC; P@300; P@30%; AUC,N/A,Natural Language Processing,"Relationship extraction is the task of extracting semantic relationships from a text. Extracted relationships usually  occur between two or more entities of a certain type (e.g. Person, Organisation, ..."
Chat-based Image Retrieval,1,VisDial,N/A,N/A,N/A,N/A,N/A
Few-Shot Transfer Learning for Saliency Prediction,1,SALICON,SALICON->WebpageSaliency - 5-shot ; SALICON->WebpageSaliency - 10-shot ; SALICON->WebpageSaliency - EUB; SALICON->WebpageSaliency - 1-shot,NSS; AUC; CC,Saliency Prediction,Computer Vision,Saliency prediction aims to predict important locations in a visual scene. It is a per-pixel regression task with predicted values ranging from 0 to 1.    Benefiting from deep learning research and la...
Multi-Person Pose Estimation and Tracking,1,PoseTrack,PoseTrack2018,MOTA,N/A,Computer Vision,"Joint multi-person pose estimation and tracking following the PoseTrack benchmark.   https://posetrack.net/    <span style=""color:grey; opacity: 0.6"">( Image credit: [PoseTrack](https://github.com/iqb..."
Multi-object colocalization,1,PASCAL VOC,VOC_all; VOC12,Detection Rate,N/A,Computer Vision,N/A
Plane Instance Segmentation,1,NYUv2,N/A,N/A,N/A,N/A,N/A
Zero-shot Scene Classification (unified classes),1,NYUv2,N/A,N/A,N/A,N/A,N/A
Viewpoint Estimation,1,PASCAL3D+,N/A,N/A,N/A,Computer Vision,N/A
Panoptic Segmentation (PanopticNDT instances),1,SUN RGB-D,N/A,N/A,N/A,N/A,N/A
Few-Shot 3D Point Cloud Classification,1,ModelNet,N/A,N/A,N/A,N/A,N/A
Time-interval Prediction,1,YAGO,N/A,N/A,N/A,N/A,N/A
Scene Labeling,1,Stanford Background,N/A,N/A,N/A,N/A,N/A
Instance Search,1,Oxford105k,N/A,N/A,Audio Fingerprint,Computer Vision; Audio,Visual **Instance Search** is the task of retrieving from a database of images the ones that contain an instance of a visual query. It is typically much more challenging than finding images from the d...
Matching Disparate Images,1,DispScenes,N/A,N/A,N/A,N/A,N/A
Hand Joint Reconstruction,1,HIC,N/A,N/A,N/A,N/A,N/A
Semi-Supervised RGBD Semantic Segmentation,1,2D-3D-S,N/A,N/A,N/A,N/A,N/A
Anomaly Detection in Edge Streams,1,DARPA,N/A,N/A,N/A,N/A,N/A
Offline Handwritten Chinese Character Recognition,1,CASIA-HWDB,N/A,N/A,N/A,N/A,N/A
TGIF-Action,1,TGIF-QA,N/A,N/A,N/A,N/A,N/A
Space-time Video Super-resolution,1,Vid4,Vimeo90K-Fast; Vimeo90K-Medium,PSNR; SSIM,N/A,Computer Vision,N/A
Key-Frame-based Video Super-Resolution (K = 15),1,Vid4,N/A,N/A,N/A,N/A,N/A
Federated Learning (Video Super-Resolution),1,Vid4,N/A,N/A,N/A,N/A,N/A
4-ary Relation Extraction,1,SciREX,N/A,N/A,N/A,N/A,N/A
3D Shape Generation,1,Combinatorial 3D Shape Dataset,N/A,N/A,Gesture Generation,Robots,Image: [Mo et al](https://arxiv.org/pdf/1908.00575v1.pdf)
Electromyography (EMG),1,Silent Speech EMG,N/A,N/A,EMG Gesture Recognition; EMG Signal Prediction; Medial knee JRF Prediction; Muscle Force Prediction; ALS Detection,Medical,N/A
Sequential skip prediction,1,MSSD,N/A,N/A,N/A,N/A,N/A
3D Interacting Hand Pose Estimation,1,InterHand2.6M,InterHand2.6M,MRRPE Test; MPVPE Test; MPJPE Test,Superpixels,Graphs,N/A
Melody Extraction,1,ErhuPT,N/A,N/A,N/A,Music,N/A
Micro-Expression Recognition,1,CASME II,N/A,N/A,N/A,N/A,N/A
Program induction,1,AQUA-RAT,N/A,N/A,N/A,Computer Code,Generating program code for domain-specific tasks
Word Sense Induction,1,BRWAC,SemEval 2010 WSI,AVG; F-Score; V-Measure,N/A,Natural Language Processing,"Word sense induction (WSI) is widely known as the “unsupervised version” of WSD. The problem states as: Given a target word (e.g., “cold”) and a collection of sentences (e.g., “I caught a cold”, “The ..."
Semantic Text Matching,1,CAIL2019-SCM,N/A,N/A,N/A,N/A,N/A
Composite action recognition,1,CATER,N/A,N/A,N/A,N/A,N/A
Future prediction,1,CCD,N/A,N/A,N/A,Computer Vision,N/A
Cervical Nucleus Detection,1,Cervix93 Cytology Dataset,N/A,N/A,N/A,N/A,N/A
Attribute,1,CityFlow,N/A,N/A,N/A,N/A,N/A
Cross-Lingual Information Retrieval,1,CLIRMatrix,N/A,N/A,N/A,N/A,N/A
Foot keypoint detection,1,COCO-WholeBody,N/A,N/A,N/A,N/A,N/A
multi-word expression sememe prediction,1,COS960,N/A,N/A,N/A,Natural Language Processing,Predict sememes for unannotated multi-word expressions.
Visual Crowd Analysis,1,CrowdFlow,N/A,N/A,N/A,N/A,N/A
Stereotypical Bias Analysis,1,CrowS-Pairs,CrowS-Pairs,Age; Physical Appearance; Sexual Orientation; Nationality; Gender; Religion; Disability; Race/Color; Socioeconomic status; Overall,N/A,Natural Language Processing,N/A
Student Engagement Level Detection (Four Class Video Classification),1,DAiSEE,N/A,N/A,N/A,N/A,N/A
Clothes Landmark Detection,1,DeepFashion2,N/A,N/A,N/A,N/A,N/A
Video Question Answering (Level 3),1,DramaQA,N/A,N/A,N/A,N/A,N/A
Video Question Answering (Level 4),1,DramaQA,N/A,N/A,N/A,N/A,N/A
Learning Semantic Representations,1,Edge-Map-345C,N/A,N/A,N/A,N/A,N/A
Event-Based Video Reconstruction,1,Event-Camera Dataset,N/A,N/A,N/A,N/A,N/A
intensity image denoising,1,FMD,N/A,N/A,N/A,N/A,N/A
B-Rep face segmentation,1,Fusion 360 Gallery,N/A,N/A,N/A,N/A,N/A
Interest Point Detection,1,Hollywood 3D dataset,N/A,N/A,Homography Estimation,Computer Vision,N/A
Model Selection,1,Image Caption Quality Dataset,N/A,N/A,N/A,Methodology,"Given a set of candidate models, the goal of **Model Selection** is to select the model that best approximates the observed data and captures its underlying regularities. Model Selection criteria are ..."
Cross-Lingual Semantic Textual Similarity,1,Interpretable STS,N/A,N/A,N/A,N/A,N/A
Gesture Synchronization,1,LRS3-TED,N/A,N/A,N/A,N/A,N/A
Long-tailed Object Detection,1,LVIS,N/A,N/A,N/A,N/A,N/A
Novel Object Detection,1,LVIS,N/A,N/A,N/A,N/A,N/A
Zero-Shot Instance Segmentation,1,LVIS,N/A,N/A,N/A,N/A,N/A
Entity Extraction using GAN,1,MedMentions,N/A,N/A,N/A,Natural Language Processing,N/A
Predict Future Video Frames,1,Moving Symbols,N/A,N/A,N/A,N/A,N/A
Business Taxonomy Construction,1,NEEQ Annual Reports,N/A,N/A,N/A,Miscellaneous,N/A
Node Property Prediction,1,OGB,ogbn-products; ogbn-mag; ogbn-proteins; ogbn-arxiv; ogbn-papers100M,Validation ROC-AUC; Number of params; Validation Accuracy; Test Accuracy; Ext. data; Test ROC-AUC,Research Performance Prediction; Position regression,Graphs,N/A
Link Property Prediction,1,OGB,ogbl-citation2; ogbl-ddi; ogbl-wikikg2; ogbl-biokg; ogbl-collab; ogbl-ppa,Test Hits@20; Test MRR; Validation MRR; Validation Hits@20; Test Hits@50; Validation Hits@50; Validation Hits@100; Number of params; Test Hits@100; Ext. data,N/A,Graphs,N/A
Abstract generation,1,OGB,N/A,N/A,N/A,N/A,N/A
Webcam (RGB) image classification,1,Photi-LakeIce,N/A,N/A,N/A,N/A,N/A
Optic Cup Detection,1,REFUGE Challenge,REFUGE Challenge,IoU,N/A,Medical,Region proposal for optic cup
Motion Compensation,1,RGB-DAVIS Dataset,N/A,N/A,N/A,N/A,N/A
Rice Grain Disease Detection,1,RICE,N/A,N/A,N/A,N/A,N/A
Instance Shadow Detection,1,SOBA,SOBA,Instance AP_segm; Bounding Box SOAP; Instance AP_bbox; Asso. AP_segm; Asso. AP_bbox; mask SOAP,Shadow Detection And Removal,Computer Vision,N/A
Camera shot segmentation,1,SoccerNet-v2,N/A,N/A,N/A,N/A,N/A
"Indoor Localization (3-DoF Pose: X, Y, Yaw)",1,Structured3D,N/A,N/A,N/A,N/A,N/A
Indoor Localization (6-DoF Pose),1,Structured3D,N/A,N/A,N/A,N/A,N/A
Indoor Localization (6D),1,Structured3D,N/A,N/A,N/A,N/A,N/A
2D Indoor Localization (Position + Orientation),1,Structured3D,N/A,N/A,N/A,N/A,N/A
6D Indoor Localization,1,Structured3D,N/A,N/A,N/A,N/A,N/A
Indoor Localization (6 DoF Pose),1,Structured3D,N/A,N/A,N/A,N/A,N/A
Text Effects Transfer,1,TE141K,N/A,N/A,N/A,Natural Language Processing,"Text effects transfer refers to the task of transferring typography styles (e.g., color, texture) to an input image of a text element."
Counterfactual Detection,1,TimeTravel,N/A,N/A,N/A,N/A,N/A
Fashion Synthesis,1,TPIC17,N/A,N/A,N/A,N/A,N/A
Video-to-Sound Generation,1,VGG-Sound,N/A,N/A,N/A,N/A,N/A
Predicting Patient Outcomes,1,Ward2ICU,N/A,N/A,N/A,N/A,N/A
Online Video Anomaly Detection,1,Detection of Traffic Anomaly,N/A,N/A,N/A,N/A,N/A
Hardware Aware Neural Architecture Search,1,Visual Wake Words,N/A,N/A,N/A,N/A,N/A
Initial Structure to Relaxed Energy (IS2RE),1,OC20,OC20,Energy MAE,N/A,Graphs,N/A
"Initial Structure to Relaxed Energy (IS2RE), Direct",1,OC20,N/A,N/A,N/A,N/A,N/A
Image Paragraph Captioning,1,Image Paragraph Captioning,Image Paragraph Captioning,METEOR; CIDEr; BLEU-4,N/A,Reasoning; Graphs,"Image paragraph captioning involves generating a detailed, multi-sentence description of the content of an image."
Zero-Shot Image Paragraph Captioning,1,Image Paragraph Captioning,N/A,N/A,N/A,N/A,N/A
Breast Cancer Histology Image Classification (20% labels),1,BreakHis,N/A,N/A,N/A,N/A,N/A
Multi-Instance Retrieval,1,EPIC-KITCHENS-100,N/A,N/A,N/A,N/A,N/A
Sparse Information Retrieval,1,CoNLL 2003,N/A,N/A,N/A,N/A,N/A
Video Deinterlacing,1,MSU Deinterlacer Benchmark,N/A,N/A,N/A,N/A,N/A
Unsupervised nucleus segmentation,1,MoNuSeg,N/A,N/A,N/A,N/A,N/A
Malware Type Detection,1,MalNet,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Video Classification,1,UBI-Fights,N/A,N/A,N/A,Computer Vision,N/A
Workflow Discovery,1,ABCD,ABCD,In-domain CE; In-domain EM; Cross-domain  CE; Cross-domain EM,N/A,Natural Language Processing,Workflow Discovery (WD) was introduced by [Workflow Discovery from Dialogues in the Low Data Regime](https://openreview.net/forum?id=L9othQvPks). WD aims to extract work-flows that have either implici...
Speech-to-Gesture Translation,1,PATS,N/A,N/A,N/A,Speech,N/A
Named Entity Recognition In Vietnamese,1,PhoNER COVID19,N/A,N/A,N/A,N/A,N/A
Stance Detection (US Election 2020 - Biden),1,Twitter Stance Election 2020,N/A,N/A,N/A,N/A,N/A
Stance Detection (US Election 2020 - Trump),1,Twitter Stance Election 2020,N/A,N/A,N/A,N/A,N/A
MULTI-VIEW LEARNING,1,CarFusion,N/A,N/A,Incomplete multi-view clustering,Computer Vision,"**Multi-View Learning** is a machine learning framework where data are represented by multiple distinct feature groups, and each feature group is referred to as a particular view.   <span class=""descr..."
Hope Speech Detection for English,1,HopeEDI,N/A,N/A,N/A,N/A,N/A
Hope Speech Detection for Tamil,1,HopeEDI,N/A,N/A,N/A,N/A,N/A
Hope Speech Detection for Malayalam,1,HopeEDI,N/A,N/A,N/A,N/A,N/A
Protein Fold Quality Estimation,1,CASP13 MQA,N/A,N/A,N/A,N/A,N/A
Duplicate-Question Retrieval,1,BEIR,N/A,N/A,N/A,N/A,N/A
Infinite Image Generation,1,LHQ,N/A,N/A,N/A,N/A,N/A
Perpetual View Generation,1,LHQ,LHQ,KID (first 20 steps); IS (first 20 steps); FID (first 20 steps); FID (full 100 steps); IS (full 100 steps); KID (full 100 steps),N/A,Computer Vision,**Perpetual View Generation** is the task of generating long-range novel views by flying into a given image.
Image Augmentation,1,Intel Image Classification,Intel Image Classification,Balanced Accuracy,N/A,Computer Vision,**Image Augmentation** is a data augmentation method that generates more training data from the existing training samples. Image Augmentation is especially useful in domains where training data is lim...
Human fMRI response prediction,1,Algonauts 2021,N/A,N/A,N/A,Computer Vision,"The task is: Given a) the set of videos of everyday events and b) the corresponding brain responses recorded while human participants viewed those videos, use computational models to predict brain res..."
Organ Detection,1,Hyper-Kvasir Dataset,N/A,N/A,N/A,N/A,N/A
3D Scene Graph Alignment,1,3DSSG,3DSSG,F1; Hits@1; MRR,N/A,Computer Vision,N/A
3d scene graph generation,1,3DSSG,N/A,N/A,N/A,N/A,N/A
Text to Audio/Video Retrieval,1,AudioCaps,N/A,N/A,N/A,N/A,N/A
Audio/Video to Text Retrieval,1,AudioCaps,N/A,N/A,N/A,N/A,N/A
Retrieval-augmented Few-shot In-context Audio Captioning,1,AudioCaps,N/A,N/A,N/A,N/A,N/A
Evidence Selection,1,QASPER,N/A,N/A,N/A,N/A,N/A
Multi-Grained Named Entity Recognition,1,Few-NERD,N/A,N/A,N/A,Natural Language Processing,"Multi-Grained Named Entity Recognition aims to detect and recognize entities on multiple granularities, without explicitly assuming non-overlapping or totally nested structures."
Human-Object Interaction Anticipation,1,VidHOI,VidHOI,Person-wise Top5: t=5(mAP@0.5); Person-wise Top5: t=3(mAP@0.5); Person-wise Top5: t=1(mAP@0.5),N/A,Computer Vision,"Human-Object Interaction (HOI) Anticipation is the task of predicting ""a set of interactions"" in a video that will happen in the future, which involves the i) localization of the subject (i.e., humans..."
Actionable Phrase Detection,1,Enron Emails,N/A,N/A,N/A,N/A,N/A
Earth Surface Forecasting,1,EarthNet2021,N/A,N/A,N/A,N/A,N/A
Semantic Segmentation Of Orthoimagery,1,LandCover.ai,N/A,N/A,N/A,N/A,N/A
Event-Driven Trading,1,EDT,N/A,N/A,N/A,N/A,N/A
Cloud Detection,1,Sentinel 2 manually extracted deep water spectra with high noise levels and sunglint,N/A,N/A,N/A,Computer Vision,N/A
Blood Cell Count,1,CBC,N/A,N/A,N/A,N/A,N/A
eXtreme-Video-Frame-Interpolation,1,X4K1000FPS,N/A,N/A,N/A,Computer Vision,Type of Video Frame Interpolation (VFI) that interpolates an intermediate frame on X4K1000FPS dataset containing 4K videos of 1000 fps with the extreme motion. The dataset has a wide variety of textur...
Attractiveness Estimation,1,CFD,N/A,N/A,N/A,N/A,N/A
Cold-Start Anomaly Detection,1,BANKING77-OOS,N/A,N/A,N/A,N/A,N/A
Indoor Scene Reconstruction,1,Rent3D++,N/A,N/A,Plan2Scene,Computer Vision,N/A
Plan2Scene,1,Rent3D++,N/A,N/A,N/A,N/A,N/A
Fingertip Detection,1,TI1K Dataset,N/A,N/A,N/A,Computer Vision,N/A
Shape from Texture,1,SurfaceGrid,N/A,N/A,N/A,N/A,N/A
Chemical Reaction Prediction,1,USPTO-50k,Mol-Instruction,Exact; Morgan FTS; METEOR; Validity,N/A,Medical,N/A
Single-step retrosynthesis,1,USPTO-50k,USPTO-50k,Top-10 accuracy; Top-5 accuracy; Top-50 accuracy; Top-3 accuracy; Top-20 accuracy; Top-1 accuracy,N/A,Medical,N/A
Zero-shot Cross-lingual Fact-checking,1,X-Fact,N/A,N/A,N/A,N/A,N/A
Safety Perception Recognition,1,Place Pulse 2.0,Place Pulse 2.0; Google Street Images,Accuracy; AUC,N/A,Computer Vision,City safety perception recognition
Alzheimer's Disease Detection,1,ADNI,N/A,N/A,N/A,N/A,N/A
Cubic splines Image Registration,1,ADNI,N/A,N/A,N/A,N/A,N/A
Epilepsy Prediction,1,Epilepsy seizure prediction,Epilepsy seizure prediction,1:1 Accuracy,N/A,Medical,N/A
Face Recognition (Closed-Set),1,TinyFace,N/A,N/A,N/A,N/A,N/A
Micro-gesture Recognition,1,iMiGUE,iMiGUE,Top 1 Accuracy; Top 5 Accuracy,N/A,Computer Vision,N/A
Crowdsourced Text Aggregation,1,CrowdSpeech,CrowdSpeech test-clean; CrowdSpeech test-other,Word Error Rate (WER),N/A,Natural Language Processing,"One of the most important parts of processing responses from crowd workers is **aggregation**: given several conflicting opinions, a method should extract the truth. This problem is also known as *tru..."
Counterfactual Explanation,1,ExBAN,N/A,N/A,N/A,Miscellaneous,"Returns a contrastive argument that permits to achieve the desired class, e.g.,  “to obtain this loan, you need XXX of annual  revenue instead of the current YYY”"
Key Detection,1,Giantsteps,N/A,N/A,N/A,N/A,N/A
Zero-shot Moment Retrieval,1,QVHighlights,N/A,N/A,N/A,N/A,N/A
Contour Detection,1,ICDAR 2021,N/A,N/A,N/A,Computer Vision,"Object **Contour Detection** extracts information about the object shape in images.   <span class=""description-source"">Source: [Object Contour and Edge Detection with RefineContourNet ](https://arxiv...."
Anxiety Detection,1,Well-being Dataset,Well-being Dataset,F1-score,N/A,Medical,Detect anxiety distress of human beings / animals
Kidney Function,1,HiRID,HiRID,MAE,N/A,Medical,Continuous prediction of urine production in the next 2h as an average rate in ml/kg/h. The task is predicted at irregular intervals.
Multimodal Forgery Detection,1,FakeAVCeleb,N/A,N/A,N/A,N/A,N/A
Predict clinical outcome,1,CirCor DigiScope,CirCor DigiScope,Clinical cost score (validation data); Clinical cost score; Clinical cost score (cross-val),N/A,Time Series,"A cost-based metric that considers the costs of algorithmic prescreening, expert screening, treatment, and diagnostic errors that result in late or missed treatments. This metric is further described ..."
Person-centric Visual Grounding,1,Who’s Waldo,N/A,N/A,N/A,N/A,N/A
Home Activity Monitoring,1,DAHLIA,N/A,N/A,N/A,Miscellaneous,N/A
Action Triplet Detection,1,CholecT50,N/A,N/A,N/A,N/A,N/A
Zero-Shot Out-of-Domain Detection,1,Pano3D,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Object Detection,1,COCO 10% labeled data,COCO 10% labeled data; COCO 100% labeled data; COCO 5% labeled data; COCO 1% labeled data; COCO 0.5% labeled data; COCO; COCO 2% labeled data,detector; mAP; 10%,N/A,Computer Vision,Semi-supervised object detection uses both labeled data and unlabeled data for training. It not only reduces the annotation burden for training high-performance object detectors but also further impro...
Clone Detection,1,CodeXGLUE,N/A,N/A,N/A,N/A,N/A
Role-filler Entity Extraction,1,MUC-4,MUC-4,Avg. F1,N/A,Natural Language Processing,Role-filler entity extraction task on the MUC-4 dataset.
Attribute Extraction,1,SWDE,SWDE,Avg F1,legal outcome extraction,Natural Language Processing,N/A
Video Harmonization,1,HYouTube,N/A,N/A,N/A,N/A,N/A
Skin Cancer Classification,1,ISIC 2020 Challenge Dataset,N/A,N/A,N/A,N/A,N/A
3D Object Super-Resolution,1,CAMELS Multifield Dataset,N/A,N/A,Super-Resolution,Computer Vision,"3D object super-resolution is the task of up-sampling 3D objects.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Multi-View Silhouette and Depth Decomposition for High Resolution 3D Object..."
Flowchart Grounded Dialog Response Generation,1,FloDial,N/A,N/A,N/A,N/A,N/A
Zero-Shot Flowchart Grounded Dialog Response Generation,1,FloDial,N/A,N/A,N/A,N/A,N/A
2D Semantic Segmentation task 2 (17 classes),1,CaDIS,N/A,N/A,N/A,N/A,N/A
Video-to-Shop,1,MovingFashion,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Tamil,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Kannada,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Malayalam,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Telugu,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Assamese,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Bengali,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Bodo,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Hindi,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Manipuri,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Marathi,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Rajasthani,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
3D Building Mesh Labeling,1,BuildingNet,N/A,N/A,N/A,N/A,N/A
Object State Change Classification,1,Ego4D,N/A,N/A,N/A,N/A,N/A
State Change Object Detection,1,Ego4D,Ego4D,AP50; AP75; AP,N/A,Computer Vision,N/A
Short-term Object Interaction Anticipation,1,Ego4D,Ego4D,Noun+TTC (Top5 mAP); Overall (Top5 mAP); Noun (Top5 mAP); Noun+Verb(Top5 mAP),N/A,Computer Vision,N/A
Future Hand Prediction,1,Ego4D,Ego4D,M.Disp(Left); C.Disp(Right); C.Disp(Left); M.Disp(Right); Disp(Total),N/A,Computer Vision,N/A
Long Term Action Anticipation,1,Ego4D,N/A,N/A,N/A,N/A,N/A
Depth Image Estimation,1,HUMAN4D,N/A,N/A,N/A,Computer Vision,N/A
Table Functional Analysis,1,PubTables-1M,N/A,N/A,N/A,N/A,N/A
Unsupervised Instance Segmentation,1,UVO,UVO; COCO val2017,AP50; AP; AP75,Unsupervised Zero-Shot Instance Segmentation,Computer Vision,N/A
Open-World Instance Segmentation,1,UVO,N/A,N/A,N/A,N/A,N/A
Webpage Object Detection,1,CoVA,N/A,N/A,N/A,N/A,N/A
Natural Language Landmark Navigation Instructions Generation,1,map2seq,N/A,N/A,N/A,N/A,N/A
Event Expansion,1,Scifi TV Shows,N/A,N/A,N/A,N/A,N/A
Face Transfer,1,VGGFace2 HQ,N/A,N/A,N/A,N/A,N/A
Colon Cancer Detection In Confocal Laser Microscopy Images,1,Chaoyang,N/A,N/A,N/A,N/A,N/A
Weakly Supervised Data Denoising,1,BCNB,N/A,N/A,N/A,N/A,N/A
Caustics Segmentation,1,SuperCaustics,N/A,N/A,N/A,N/A,N/A
Discourse Marker Prediction,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Global Facts,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Miscellaneous,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Abstract Algebra,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Mathematical Induction,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Medical Genetics,1,BIG-bench,BIG-bench,Accuracy,Genetic Risk Prediction,Miscellaneous; Medical,N/A
Figure Of Speech Detection,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Question Selection,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Electrical Engineering,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Ventricular fibrillation detection,1,MIT-BIH Malignant Ventricular Ectopy Database (VFDB),N/A,N/A,N/A,N/A,N/A
Localization In Video Forgery,1,ForgeryNet,N/A,N/A,N/A,N/A,N/A
Physical Video Anomaly Detection,1,PHANTOM,N/A,N/A,N/A,N/A,N/A
Connective Detection,1,DISRPT2021,N/A,N/A,N/A,N/A,N/A
text-to-3d-human,1,ASLLVD,N/A,N/A,N/A,N/A,N/A
Text-to-Face Generation,1,FFHQ-Text,N/A,N/A,N/A,Computer Vision,N/A
Seizure prediction,1,TUH EEG Seizure Corpus,Melbourne University Seizure Prediction,AUC,N/A,Medical,N/A
Zero-Shot Cross-Lingual Text-to-Image Retrieval,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Zero-Shot Cross-Lingual Image-to-Text Retrieval,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Zero-Shot Cross-Lingual Visual Natural Language Inference,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Zero-Shot Cross-Lingual Visual Question Answering,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Max-Shot Cross-Lingual Visual Natural Language Inference,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Max-Shot Cross-Lingual Visual Question Answering,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Max-Shot Cross-Lingual Text-to-Image Retrieval,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Max-Shot Cross-Lingual Image-to-Text Retrieval,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Cross-Domain Iris Presentation Attack Detection,1,NDPSID - WACV 2019,N/A,N/A,N/A,N/A,N/A
Image Categorization,1,Yeast colony morphologies,N/A,N/A,Fine-Grained Visual Categorization,Computer Vision,N/A
3D Human Dynamics,1,InfiniteRep,N/A,N/A,Portrait Animation,Audio,Image: [Zhang et al](https://openaccess.thecvf.com/content_ICCV_2019/papers/Zhang_Predicting_3D_Human_Dynamics_From_Video_ICCV_2019_paper.pdf)
Video Based Workflow Recognition,1,PETRAW,PETRAW,Average AD-Accuracy,N/A,Computer Vision,N/A
Segmentation Based Workflow Recognition,1,PETRAW,PETRAW,Average AD-Accuracy,N/A,Computer Vision,N/A
Video & Kinematic Base Workflow Recognition,1,PETRAW,PETRAW,Average AD-Accuracy,N/A,Computer Vision,N/A
"Video, Kinematic & Segmentation Base Workflow Recognition",1,PETRAW,PETRAW,Average AD-Accuracy,N/A,Computer Vision,N/A
Visual Abductive Reasoning,1,SHERLOCK,N/A,N/A,N/A,N/A,N/A
Fact Selection,1,ArgSciChat,ArgSciChat,Fact-F1,N/A,Natural Language Processing,A task where an agent should select at most two sentences from the paper as argumentative facts.
Anchor link prediction,1,Weibo-Douban,N/A,N/A,N/A,N/A,N/A
Style change detection,1,MuLD,N/A,N/A,N/A,N/A,N/A
Panorama Pose Estimation (N-view),1,ZInd,N/A,N/A,N/A,N/A,N/A
Pose Contrastive Learning,1,Fitness-AQA,N/A,N/A,N/A,Computer Vision,N/A
Motion Disentanglement,1,Fitness-AQA,N/A,N/A,N/A,Computer Vision,Disentangling irregular (anomalous) motion from regular motion.
Atom3D benchmark,1,ATOM3D,N/A,N/A,N/A,N/A,N/A
Ancient Text Restoration,1,I.PHI,I.PHI,Region (Top 1 (%)); CER (%); Top 20 (%); Date (Years); Region (Top 3 (%)); Top 1 (%),N/A,Miscellaneous,Image credit: [Restoring and attributing ancient texts using deep neural networks  ](https://paperswithcode.com/paper/restoring-and-attributing-ancient-texts-using)
Depth Anomaly Detection and Segmentation,1,MVTEC 3D-AD,N/A,N/A,N/A,N/A,N/A
RGB+3D Anomaly Detection and Segmentation,1,MVTEC 3D-AD,N/A,N/A,N/A,N/A,N/A
Behavioral Malware Detection,1,BODMAS,N/A,N/A,N/A,N/A,N/A
English Conversational Speech Recognition,1,CANDOR Corpus,N/A,N/A,N/A,N/A,N/A
Math Information Retrieval,1,ARQMath,ARQMath,NDCG; MAP; P@10; bpref,N/A,Natural Language Processing,Information Retrieval on Math Contents
Argument Pair Extraction (APE),1,RR,N/A,N/A,N/A,N/A,N/A
3D Source-Free Domain Adaptation,1,SynLiDAR,N/A,N/A,N/A,N/A,N/A
3D Unsupervised Domain Adaptation,1,SynLiDAR,N/A,N/A,N/A,N/A,N/A
Claim-Evidence Pair Extraction (CEPE),1,IAM Dataset,N/A,N/A,N/A,N/A,N/A
Reference-based Video Super-Resolution,1,RealMCVSR,N/A,N/A,N/A,N/A,N/A
Gait Recognition in the Wild,1,Gait3D,N/A,N/A,N/A,N/A,N/A
ECG QRS Detection,1,CPSC2019,N/A,N/A,N/A,N/A,N/A
Question Quality Assessment,1,60k Stack Overflow Questions,N/A,N/A,N/A,N/A,N/A
Weakly Supervised 3D Detection,1,KITTI-360,N/A,N/A,N/A,N/A,N/A
Short-observation new product sales forecasting,1,VISUELLE2.0,VISUELLE2.0,10 steps MAE; 1 step MAE,N/A,Time Series,N/A
Gallbladder Cancer Detection,1,GBCU,N/A,N/A,N/A,N/A,N/A
Classification Of Breast Cancer Histology Images,1,BCI,N/A,N/A,N/A,N/A,N/A
Object Segmentation,1,GRIT,GRIT,Segmentation (ablation); Segmentation (test),Landslide segmentation; Text-Line Extraction; Camouflaged Object Segmentation,Computer Vision,N/A
Keypoint Estimation,1,GRIT,N/A,N/A,N/A,N/A,N/A
Contact Detection,1,BEHAVE,BEHAVE,Recall; Precision,N/A,Robots,Static-friction contact detection in legged locomotion
Rolling Shutter Correction,1,BS-RSC,BS-RSC,Average PSNR (dB),N/A,N/A,Rolling Shutter Correction
LV Segmentation,1,Echonet-Dynamic,N/A,N/A,N/A,N/A,N/A
Respiratory motion forecasting,1,ExtMarker,N/A,N/A,N/A,N/A,N/A
Personality Recognition in Conversation,1,CPED,CPED,Accuracy (%); Accuracy of Openness; Accuracy of Neurotism; Accuracy of Conscientiousness; Accuracy of Agreeableness; Macro-F1; Accuracy of Extraversion,N/A,Natural Language Processing,"Given a speaker's conversation with others, it is required to recognize the speaker's personality traits through the conversation record, which includes two scenarios, (1) $1-1$ conversations: the rob..."
Personalized and Emotional Conversation,1,CPED,N/A,N/A,N/A,N/A,N/A
image smoothing,1,Real world moire pattern classification,N/A,N/A,N/A,Computer Vision,N/A
Fast Vehicle Detection,1,Autorickshaw Image Dataset | Niche Vehicle Dataset,N/A,N/A,N/A,N/A,N/A
Referring Image Matting (Expression-based),1,RefMatte,N/A,N/A,N/A,N/A,N/A
Referring Image Matting (Keyword-based),1,RefMatte,N/A,N/A,N/A,N/A,N/A
Referring Image Matting (RefMatte-RW100),1,RefMatte,N/A,N/A,N/A,N/A,N/A
Referring Image Matting (Prompt-based),1,RefMatte,N/A,N/A,N/A,N/A,N/A
Aesthetic Image Captioning,1,RPCD,N/A,N/A,N/A,N/A,N/A
Context-specific Spam Detection,1,Traditional and Context-specific Spam Twitter,N/A,N/A,N/A,N/A,N/A
Traditional Spam Detection,1,Traditional and Context-specific Spam Twitter,N/A,N/A,N/A,N/A,N/A
feature selection,1,Hotel,N/A,N/A,N/A,N/A,N/A
Fire Detection,1,895 Fire Videos Data,NIST Report of Test FR 4016,MCC; F1-Score,N/A,Time Series,Detection of fire using multi-variate time series sensor data.
Handwritten Line Segmentation,1,BN-HTRd,N/A,N/A,N/A,N/A,N/A
Markerless Motion Capture,1,RICH,N/A,N/A,N/A,N/A,N/A
Bangla Text Detection,1,Bengali.AI Handwritten Graphemes,N/A,N/A,N/A,N/A,N/A
Table Type Detection,1,T2Dv2,N/A,N/A,N/A,N/A,N/A
Weakly Supervised 3D Point Cloud Segmentation,1,ScribbleKITTI,N/A,N/A,N/A,Computer Vision,N/A
Translation mri-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Image-text Classification,1,FewSOL,N/A,N/A,Multilingual Image-Text Classification,Miscellaneous,N/A
Inverse-Tone-Mapping,1,MSU HDR Video Reconstruction Benchmark,MSU HDR Video Reconstruction Benchmark,HDR-VQM; HDR-PSNR; HDR-SSIM,inverse tone mapping,Computer Vision,N/A
Histopathological Segmentation,1,CoCaHis,N/A,N/A,N/A,N/A,N/A
image-sentence alignment,1,VALSE,N/A,N/A,N/A,N/A,N/A
Handwritten Digit Image Synthesis,1,MatriVasha:,N/A,N/A,N/A,N/A,N/A
Morphological Inflection,1,UniMorph 4.0,N/A,N/A,N/A,Natural Language Processing,"**Morphological Inflection** is the task of generating a target (inflected form) word from a source word (base form), given a morphological attribute, e.g. number, tense, and person etc. It is useful ..."
Panoptic Scene Graph Generation,1,PSG Dataset,N/A,N/A,N/A,N/A,N/A
Air Pollution Prediction,1,DEAP City Dataset,N/A,N/A,N/A,Miscellaneous,N/A
Unconditional Video Generation,1,CelebV-HQ,N/A,N/A,N/A,N/A,N/A
Face Reenactment,1,AnimeCeleb,N/A,N/A,N/A,Computer Vision,**Face Reenactment** is an emerging conditional face synthesis task that aims at fulfilling two goals simultaneously: 1) transfer a source face shape to a target face; while 2) preserve the appearance...
Predictive Process Monitoring,1,Ultra-processed Food Dataset,N/A,N/A,Prognosis,Time Series,A branch of predictive analysis that attempts to predict some future state of a business process.
3D scene Editing,1,LLFF,LLFF,CLIP,N/A,Computer Vision,N/A
3D Object Editing,1,LLFF,N/A,N/A,N/A,N/A,N/A
Extracting COVID-19 Events from Twitter,1,MOS Dataset,N/A,N/A,N/A,N/A,N/A
Multi-Labeled Relation Extraction,1,TimeBankPT,N/A,N/A,N/A,N/A,N/A
Information Cascade Popularity Prediction,1,Weibo,N/A,N/A,N/A,N/A,N/A
Grayscale Video Denoising,1,Videezy4K,N/A,N/A,N/A,N/A,N/A
Negation and Speculation Cue Detection,1,The BioScope Corpus,BioScope : Abstracts; *sem 2012 Shared Task: Sherlock Dataset,F1,N/A,Natural Language Processing,N/A
Few-shot Object Counting and Detection,1,FSC147,N/A,N/A,N/A,N/A,N/A
Out-of-Sight Trajectory Prediction,1,Vi-Fi Multi-modal Dataset,N/A,N/A,N/A,N/A,N/A
Local Color Enhancement,1,University of Waterloo skin cancer database,N/A,N/A,N/A,N/A,N/A
Cultural Vocal Bursts Intensity Prediction,1,HUME-VB,HUME-VB,Concordance correlation coefficient (CCC),N/A,Speech,to predict the intensity of 40 culture-specific emotions (10 emotions from each culture)
Vocal Bursts Intensity Prediction,1,HUME-VB,N/A,N/A,N/A,N/A,N/A
Vocal Bursts Valence Prediction,1,HUME-VB,N/A,N/A,N/A,N/A,N/A
Vocal Bursts Type Prediction,1,HUME-VB,N/A,N/A,N/A,N/A,N/A
Emotion-Cause Pair Extraction,1,"Xia and Ding, 2019",N/A,N/A,N/A,N/A,N/A
Landmark Tracking,1,MBW - Zoo Dataset,N/A,N/A,Muscle Tendon Junction Identification,Medical; Computer Vision,N/A
Unsupervised Landmark Detection,1,MBW - Zoo Dataset,MAFL Unaligned,Mean NME,N/A,Computer Vision,"The discovery of object landmarks on a set of images depicting objects of the same category, directly from raw images without using any manual annotations."
Weakly-supervised Anomaly Detection,1,ShanghaiTech Campus,N/A,N/A,N/A,N/A,N/A
Uncertainty Visualization,1,MUAD,N/A,N/A,N/A,N/A,N/A
Subject Transfer,1,iDesigner,N/A,N/A,N/A,N/A,N/A
Zero-Shot Facial Expression Recognition,1,MAFW,N/A,N/A,N/A,N/A,N/A
Gunshot Detection,1,BGG dataset,N/A,N/A,N/A,N/A,N/A
Shooter Localization,1,BGG dataset,N/A,N/A,N/A,Audio,Shooter localization based on videos.
Predicting Drug-Induced Laboratory Test Effects,1,CORRONA CERTAIN,N/A,N/A,N/A,N/A,N/A
Laminar-Turbulent Flow Localisation,1,Wind Tunnel and Flight Test Experiments,Wind Tunnel and Flight Test Experiments,Category IoU,N/A,Computer Vision,It is a segmentation task on thermographic measurement images in order to separate laminar and turbulent flow regions on flight body parts.
EEG Artifact Removal,1,CWL EEG/fMRI Dataset,N/A,N/A,N/A,N/A,N/A
Text to Image Generation,1,DiffusionDB,DiffusionDB,N/A,Text to 3D,Computer Vision; Natural Language Processing,N/A
Cancer-no cancer per image classification,1,CBIS-DDSM,N/A,N/A,N/A,N/A,N/A
Bangla Spelling Error Correction,1,DPCSpell-Bangla-SEC-Corpus,N/A,N/A,N/A,N/A,N/A
Counterfactual Planning,1,CRIPP-VQA,N/A,N/A,N/A,N/A,N/A
Streaming Target Sound Extraction,1,FSDSoundScapes,N/A,N/A,N/A,N/A,N/A
Video Defect Classification,1,QV-Pipe,N/A,N/A,N/A,N/A,N/A
Temporal Defect Localization,1,CCTV-Pipe,N/A,N/A,N/A,N/A,N/A
Mobile Periocular Recognition,1,UFPR-Periocular,N/A,N/A,N/A,N/A,N/A
Visual Text Correction,1,Dataset for Post-OCR text correction in Sanskrit,N/A,N/A,N/A,N/A,N/A
Phishing Website Detection,1,Padding Ain't Enough: Assessing the Privacy Guarantees of Encrypted DNS – Subpage-Agnostic Domain Classification Firefox,N/A,N/A,N/A,N/A,N/A
Arabic Speech Recognition,1,modified_shemo,N/A,N/A,N/A,N/A,N/A
Hyper-Relational Extraction,1,HyperRED,N/A,N/A,N/A,N/A,N/A
Event-based N-ary Relaiton Extraction,1,HyperRED,N/A,N/A,N/A,N/A,N/A
Role-based N-ary Relaiton Extraction,1,HyperRED,N/A,N/A,N/A,N/A,N/A
Hypergraph-based N-ary Relaiton Extraction,1,HyperRED,N/A,N/A,N/A,N/A,N/A
Open Vocabulary Attribute Detection,1,OVAD benchmark,N/A,N/A,N/A,N/A,N/A
Multilingual Image-Text Classification,1,GLAMI-1M,N/A,N/A,N/A,N/A,N/A
Acute Stroke Lesion Segmentation,1,ATLAS v2.0,N/A,N/A,N/A,N/A,N/A
SemEval-2022 Task 4-1 (Binary PCL Detection),1,DPM,DPM,F1-score,N/A,Natural Language Processing,N/A
SemEval-2022 Task 4-2 (Multi-label PCL Detection),1,DPM,N/A,N/A,N/A,N/A,N/A
Binary Condescension Detection,1,DPM,DPM,F1-score,N/A,Natural Language Processing,N/A
Multi-label Condescension Detection,1,DPM,DPM,Macro-F1,N/A,Natural Language Processing,N/A
One-Shot Part Segmentation of Grasp Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Cut Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Scoop Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Contain Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Wrap-Grasp Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Pound Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Support Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Grasp Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Cut Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Scoop Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Contain Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Wrap-Grasp Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Pound Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Support Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
ArzEn Speech Recognition,1,ArzEn,N/A,N/A,N/A,N/A,N/A
Partial Video Copy Detection,1,STVD-PVCD,STVD-PVCD,F1,N/A,Computer Vision,The PVCD goal is identifying and locating if one or more segments of a long testing video have been copied (transformed) from the reference videos dataset.
Hyperspectral Image-Based Fruit Ripeness Prediction,1,DeepHS Fruit v2,N/A,N/A,N/A,N/A,N/A
Grounded Situation Recognition,1,VASR,SWiG,Top-5 Verbs; Top-1 Verb & Value; Top-5 Verbs & Value; Top-1 Verb; Top-1 Verb & Grounded-Value; Top-5 Verbs & Grounded-Value,N/A,Computer Vision,"Grounded Situation Recognition aims to produce the structured image summary which describes the primary activity (verb), its relevant entities (nouns), and their bounding-box groundings."
Document Image Skew Estimation,1,DISE 2021 Dataset,DISE 2021 Dataset,Percentage correct,N/A,Computer Vision,N/A
Multi-step retrosynthesis,1,USPTO-190,USPTO-190,Success Rate (100 model calls); Success Rate (500 model calls),N/A,N/A,These leaderboards are used to track progress in Multi-step retrosynthesis.
Sleep apnea detection,1,Apnea-ECG,N/A,N/A,N/A,N/A,N/A
Line Items Extraction,1,DocILE,N/A,N/A,N/A,N/A,N/A
Sign Language Retrieval,1,CSL-Daily,N/A,N/A,N/A,N/A,N/A
Calving Front Delineation In Synthetic Aperture Radar Imagery,1,CaFFe,CaFFe,Mean Distance Error,Calving Front Delineation In Synthetic Aperture Radar Imagery With Fixed Training Amount,Computer Vision,"Delineating the calving front of a marine-terminating glacier in synthetic aperture radar (SAR) imagery. This can, for example, be done through Semantic Segmentation."
Calving Front Delineation In Synthetic Aperture Radar Imagery With Fixed Training Amount,1,CaFFe,N/A,N/A,N/A,N/A,N/A
Video Narrative Grounding,1,Video Localized Narratives,N/A,N/A,N/A,N/A,N/A
Protein Secondary Structure Prediction,1,PS4,PS4; TS115; Jpred4 blind set; CullPDB; CB513; 2019_test set; CASP12; 2017_test set,Accuracy; Q3; Q8,N/A,Medical,"Protein secondary structure prediction is a vital task in bioinformatics, aiming to determine the arrangement of amino acids in proteins, including α-helices, β-sheets, and coils. By analyzing amino a..."
Color Mismatch Correction,1,Real-World Stereo Color and Sharpness Mismatch Dataset,N/A,N/A,N/A,N/A,N/A
Self-supervised Scene Flow Estimation,1,Argoverse 2,N/A,N/A,N/A,N/A,N/A
Visual Commonsense Tests,1,WHOOPS!,N/A,N/A,N/A,N/A,N/A
Social Media Popularity Prediction,1,Ranking social media news feed,SMP Test Split,MAE; SRC,N/A,Miscellaneous; Time Series,"Social Media Popularity Prediction (SMPP) aims to predict the future popularity (e.g., clicks, views, likes, etc.) of online posts automatically via plenty of social media data from public platforms. ..."
Heterogeneous Treatment Effect Estimation,1,IHDP,N/A,N/A,N/A,N/A,N/A
Robust Camera Only 3D Object Detection,1,nuScenes-C,N/A,N/A,N/A,N/A,N/A
Automatic Cell Counting,1,MuCeD,N/A,N/A,N/A,Miscellaneous,N/A
Face Image Quality Assessment,1,PIQ23,N/A,N/A,N/A,N/A,N/A
Robust BEV Detection,1,RoboBEV,N/A,N/A,N/A,N/A,N/A
Robust BEV Map Segmentation,1,RoboBEV,N/A,N/A,N/A,N/A,N/A
Long-tail Video Object Segmentation,1,BURST,N/A,N/A,N/A,N/A,N/A
Open-World Video Segmentation,1,BURST,N/A,N/A,N/A,N/A,N/A
Multi-Person Pose forecasting,1,Expi,N/A,N/A,N/A,N/A,N/A
Catalog Extraction,1,ChCatExt,N/A,N/A,N/A,N/A,N/A
Personalized Segmentation,1,PerSeg,PerSeg,mIoU,N/A,Computer Vision,"Given a one-shot image with a reference mask, the models are required to segment the indicated target object in any other images."
Generative Visual Question Answering,1,PMC-VQA,PMC-VQA,BLEU-1,Video-based Generative Performance Benchmarking,Reasoning,Generating answers in free form to questions posed about images.
Image Deconvolution,1,Stained mice brain blood vessels. Confocal-LFM,N/A,N/A,N/A,Computer Vision,N/A
Generalized Referring Expression Segmentation,1,gRefCOCO,N/A,N/A,N/A,N/A,N/A
Open-set video tagging,1,ChinaOpen-1k,N/A,N/A,N/A,Computer Vision,"In open-set video tagging, a model is asked to tag a given video with an ad-hoc vocabulary that the model is not specifically tuned for."
Multi-View 3D Shape Retrieval,1,FaMoS,N/A,N/A,N/A,Adversarial,N/A
CT Reconstruction,1,2DeteCT,N/A,N/A,N/A,N/A,N/A
Visual Analogies,1,Im-Promptu Visual Analogy Suite,N/A,N/A,N/A,Computer Vision,N/A
3D Multi-Person Human Pose Estimation,1,UNIPD-BPE,N/A,N/A,N/A,N/A,N/A
Promoter Detection,1,GUE,GUE,MCC,N/A,Medical,N/A
Core Promoter Detection,1,GUE,GUE,MCC,N/A,Medical,N/A
Splice Site Prediction,1,GUE,GUE,MCC,N/A,Medical,N/A
Covid Variant Prediction,1,GUE,GUE,Avg F1,N/A,Medical,N/A
Epigenetic Marks Prediction,1,GUE,GUE,MCC,N/A,Medical,N/A
Transcription Factor Binding Site Prediction (Human),1,GUE,N/A,N/A,N/A,N/A,N/A
Transcription Factor Binding Site Prediction (Mouse),1,GUE,N/A,N/A,N/A,N/A,N/A
Printed Text Recognition,1,UTRSet-Real,N/A,N/A,N/A,N/A,N/A
Real-Time Visual Tracking,1,Drunkard's Dataset,N/A,N/A,N/A,N/A,N/A
Tone Mapping,1,NILUT,N/A,N/A,N/A,N/A,N/A
Structured Report Generation,1,Rad-ReStruct,N/A,N/A,N/A,N/A,N/A
Retinal OCT Layer Segmentation,1,OCTID,N/A,N/A,N/A,N/A,N/A
Video-based Generative Performance Benchmarking (Correctness of Information),1,VideoInstruct,VideoInstruct,gpt-score,N/A,N/A,"The benchmark evaluates a generative Video Conversational Model with respect to Correctness of Information.    We curate a test set based on the ActivityNet-200 dataset, featuring videos with rich, de..."
Video-based Generative Performance Benchmarking (Detail Orientation)),1,VideoInstruct,N/A,N/A,N/A,N/A,N/A
Video-based Generative Performance Benchmarking (Contextual Understanding),1,VideoInstruct,N/A,N/A,N/A,N/A,N/A
Video-based Generative Performance Benchmarking (Temporal Understanding),1,VideoInstruct,N/A,N/A,N/A,N/A,N/A
Video-based Generative Performance Benchmarking (Consistency),1,VideoInstruct,N/A,N/A,N/A,N/A,N/A
Toxic Spans Detection,1,CAD,N/A,N/A,N/A,N/A,N/A
Described Object Detection,1,Description Detection Dataset,N/A,N/A,N/A,N/A,N/A
Nuclei Classification,1,MoNuSAC,N/A,N/A,N/A,N/A,N/A
Ontology Matching,1,AISECKG,N/A,N/A,N/A,Knowledge Base,N/A
Weakly-Supervised Object Segmentation,1,CheXlocalize,N/A,N/A,N/A,N/A,N/A
Semi-supervised Change Detection,1,GVLM,N/A,N/A,N/A,N/A,N/A
Landslide segmentation,1,GVLM,N/A,N/A,N/A,N/A,N/A
Vessel Detection,1,Vessel detection Dateset,N/A,N/A,N/A,N/A,N/A
Few-Shot Camera-Adaptive Color Constancy,1,Nikon RAW Low Light,N/A,N/A,N/A,N/A,N/A
Single-shot HDR Reconstruction,1,SI-HDR,N/A,N/A,N/A,Computer Vision,"SVE-based HDR imaging, also known as single-shot HDR imaging, algorithms capture a scene with pixel-wise varying exposures in a single image and then computationally synthesize an HDR image, which ben..."
Real-time instance measurement,1,MEIS,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Human Pose Estimation,1,PoPArt,N/A,N/A,N/A,N/A,N/A
Story Visualization,1,StoryBench,N/A,N/A,N/A,N/A,N/A
Conversational Sentiment Quadruple Extraction,1,DiaASQ,N/A,N/A,N/A,N/A,N/A
Composed Video Retrieval (CoVR),1,WebVid-CoVR,N/A,N/A,N/A,N/A,N/A
Global 3D Human Pose Estimation,1,EMDB,N/A,N/A,N/A,N/A,N/A
Unsupervised Image Decomposition,1,CongNaMul,N/A,N/A,N/A,Computer Vision,N/A
Depth And Camera Motion,1,CamlessVideosFromTheWild,N/A,N/A,Face Anti-Spoofing,Computer Vision,N/A
Fracture detection,1,GRAZPEDWRI-DX,N/A,N/A,N/A,N/A,N/A
Language-Based Temporal Localization,1,VidChapters-7M,VidChapters-7M,R@10s; R1@.9,Corpus Video Moment Retrieval,Computer Vision,N/A
Video Chaptering,1,VidChapters-7M,VidChapters-7M,P@0.7; R@3s; P@3s; SODA; P@0.5; P@5s; R@0.7; R@0.5; R@5s; CIDEr,N/A,Computer Vision,Partitioning a long video timeline into semantic units and generating corresponding chapter titles.
Outcome Prediction In Multimodal Mri,1,ABIDE,N/A,N/A,N/A,N/A,N/A
Multi-Subject Fmri Data Alignment,1,ABIDE,N/A,N/A,N/A,N/A,N/A
Motion Correction In Multishot Mri,1,ABIDE,N/A,N/A,N/A,Medical,N/A
Brain Lesion Segmentation From Mri,1,ABIDE,N/A,N/A,N/A,N/A,N/A
Android Malware Detection,1,Appdroid,N/A,N/A,N/A,N/A,N/A
Image-text matching,1,CommercialAdsDataset,N/A,N/A,N/A,N/A,N/A
Propaganda detection,1,Analysing state-backed propaganda websites: a new dataset and linguistic study,N/A,N/A,Propaganda technique identification; Propaganda span identification,Natural Language Processing,N/A
Odor Descriptor Prediction,1,Multi-Labelled SMILES Odors dataset,N/A,N/A,N/A,N/A,N/A
Automatic Modulation Recognition,1,AugMod,N/A,N/A,N/A,Time Series,Automatic modulation recognition/classification identifies the modulation pattern of communication signals received from wireless or wired networks.
"Suspicous (BIRADS 4,5)-no suspicous (BIRADS 1,2,3) per image classification",1,InBreast,N/A,N/A,N/A,N/A,N/A
Analog Video Restoration,1,TAPE,N/A,N/A,N/A,N/A,N/A
Scholarly Named Entity Recognition,1,GSAP-NER,N/A,N/A,N/A,N/A,N/A
Audio-Visual Question Answering (AVQA),1,AVQA,N/A,N/A,N/A,N/A,N/A
Coronary Artery Segmentation,1,ARCADE,N/A,N/A,N/A,N/A,N/A
Stenosis Segmentation,1,ARCADE,N/A,N/A,N/A,N/A,N/A
Caller Detection,1,InfantMarmosetsVox,N/A,N/A,N/A,N/A,N/A
Image-based Recommendation Explainability,1,Tripadvisor Restaurant Reviews,N/A,N/A,N/A,N/A,N/A
Pupil Tracking,1,INI-30,N/A,N/A,N/A,N/A,N/A
Pupil Detection,1,INI-30,N/A,N/A,N/A,N/A,N/A
Intraoperative Tracking,1,CholecTrack20,N/A,N/A,N/A,N/A,N/A
Intracorporeal Tracking,1,CholecTrack20,N/A,N/A,N/A,N/A,N/A
Visibility Tracking,1,CholecTrack20,N/A,N/A,N/A,N/A,N/A
3D Point Cloud Reinforcement Learning,1,Synthetic OD Data,N/A,N/A,N/A,N/A,N/A
video narration captioning,1,Shot2Story20K,Shot2Story20K,ROUGE; METEOR; CIDEr; BLEU-4,N/A,Computer Vision,Human narration is another critical factor to understand a multi-shot video. It often provides information of the background knowledge and commentator’s view on visual events. We conduct experiments t...
Brain Visual Reconstruction,1,GOD,N/A,N/A,Brain Visual Reconstruction from fMRI,Computer Vision,"Reconstruct viewed or imagined visual stimuli, including but not limited to images and videos from brain activities patterns recorded with neuoimaging technoligies."
Brain Visual Reconstruction from fMRI,1,GOD,N/A,N/A,N/A,N/A,N/A
inverse tone mapping,1,VDS dataset: Multi exposure stack-based inverse tone mapping,VDS dataset: Multi exposure stack-based inverse tone mapping,PU21-PSNR; Kim and Kautz TMO-PSNR; HDR-VDP-2; Reinhard'TMO-PSNR; HDR-VDP-3; PU21-SSIM,Inverse-Tone-Mapping,Computer Vision,For stack based inverse tone mapping
Specular Segmentation,1,PSD Dataset,N/A,N/A,N/A,Computer Vision,Segmentation of Specular Highlights in images
Geometrical View,1,MOSAD,N/A,N/A,N/A,Computer Vision,N/A
Clothing Attribute Recognition,1,Clothing Attributes Dataset,Clothing Attributes Dataset,Accuracy,N/A,Computer Vision,Clothing attribute recognition is the task of automatically identifying and categorizing various characteristics and attributes associated with clothing items in images
Historical Color Image Dating,1,Historical Color Image Dataset,HCI,MAE; accuracy,N/A,Computer Vision,N/A
Crop Type Mapping,1,SICKLE,N/A,N/A,N/A,N/A,N/A
Sowing Date Prediction,1,SICKLE,N/A,N/A,N/A,N/A,N/A
Transplanting Date Prediction,1,SICKLE,N/A,N/A,N/A,N/A,N/A
Harvesting Date Prediction,1,SICKLE,N/A,N/A,N/A,N/A,N/A
MLLM Evaluation: Aesthetics,1,EAPD,N/A,N/A,N/A,Computer Vision,N/A
Cell Tracking,1,ALFI,N/A,N/A,N/A,N/A,N/A
Factual Inconsistency Detection in Chart Captioning,1,CHOCOLATE,CHOCOLATE-LLM; CHOCOLATE-LVLM; CHOCOLATE-FT; CHOCOLATE,Kendall's Tau-c,N/A,N/A,Detect factual inconsistency between charts and captions.
Amodal Tracking,1,TAO-Amodal,N/A,N/A,N/A,N/A,N/A
Only Connect Walls Dataset Task 1 (Grouping),1,OCW,N/A,N/A,N/A,N/A,N/A
Hierarchical Text Segmentation,1,HierText,HierText,"F-score (para., layout); F-score (word); F-score (average); F-score (text-line); F-score (stroke)",N/A,Computer Vision,"Segment strokes, words, text-lines, paragraphs (layout analysis) in images within a unified framework"
Audio Quality Assessment,1,ODAQ: Open Dataset of Audio Quality,ODAQ: Open Dataset of Audio Quality,Pearson correlation coefficient (PCC),N/A,Audio,Computational audio quality assessment aims to predict the quality of audio signals as perceived by expert listeners.
Music Quality Assessment,1,ODAQ: Open Dataset of Audio Quality,N/A,N/A,N/A,Audio,Evaluating the quality of music given noise and filtering conditions
Conversational Web Navigation,1,WebLINX,WebLINX,Text (F1); Element (IoU); Intent Match; Overall score,N/A,Natural Language Processing,The problem of conversational web navigation is described as follow: a digital agent controls a web browser and follows user instructions to solve real-world tasks in a multi-turn dialogue fashion. It...
IUPAC Name Prediction,1,IUPAC Standards Online,N/A,N/A,N/A,N/A,N/A
Medical Image Deblurring,1,Human Protein Atlas,ChexPert ; COVID-19 CT Scan; Human Protein Atlas Image; Brain MRI segmentation,Average PSNR,N/A,Computer Vision,Medical image deblurring aims to remove blurs from medical images
AUDIO-VISUAL QUESTION ANSWERING (MUSIC-AVQA-v2.0),1,MUSIC-AVQA v2.0,N/A,N/A,N/A,N/A,N/A
Artifact Detection,1,HistoArtifacts,N/A,N/A,N/A,N/A,N/A
All-day Semantic Segmentation,1,All-day CityScapes,N/A,N/A,N/A,N/A,N/A
Aspect Category Sentiment Analysis,1,FABSA,N/A,N/A,N/A,N/A,N/A
Hidden Aspect Detection,1,FABSA,N/A,N/A,N/A,N/A,N/A
Latent Aspect Detection,1,FABSA,N/A,N/A,N/A,N/A,N/A
Chemical Entity Recognition,1,Chem-FINESE,N/A,N/A,N/A,Medical,Chemical Entity Recognition (CER) is a fundamental task in biomedical text mining and Natural Language Processing (NLP). It involves the identification and classification of chemical entities in textu...
Human Detection of Deepfakes,1,GOTCHA,N/A,N/A,N/A,N/A,N/A
Aerial Scene Classification,1,GDIT,N/A,N/A,N/A,N/A,N/A
Micro-Action Recognition,1,MA-52,N/A,N/A,N/A,N/A,N/A
Python Code Synthesis,1,MMCode,N/A,N/A,N/A,N/A,N/A
Game State Reconstruction,1,SoccerNet-GSR,N/A,N/A,N/A,N/A,N/A
Clinical Section Identification,1,MedSecId,N/A,N/A,N/A,N/A,N/A
Stereo-LiDAR Fusion,1,VBR,N/A,N/A,N/A,N/A,N/A
Novel LiDAR View Synthesis,1,VBR,N/A,N/A,N/A,N/A,N/A
Visual Prompt Tuning,1,VTAB,VTAB-1k(Natural<7>); VTAB-1k(Structured<8>); FGVC; VTAB-1k(Specialized<4>),Mean Accuracy,N/A,Computer Vision,Visual Prompt Tuning(VPT) only introduces a small amount of task-specific learnable parameters into the input space while freezing the entire pre-trained Transformer backbone during downstream trainin...
Face Image Retrieval,1,BTS3.1,N/A,N/A,N/A,N/A,N/A
motion retargeting,1,MultiSenseBadminton,N/A,N/A,N/A,Computer Vision,N/A
Retrosynthesis,1,PaRoutes,Mol-Instruction,Exact; Validity; METEOR; Morgan FTS,N/A,Medical,"Retrosynthetic analysis is a pivotal synthetic methodology in organic chemistry that employs a reverse-engineering approach, initiating from the target compound and retroactively tracing potential syn..."
Mono3DVG,1,Mono3DRefer,N/A,N/A,N/A,N/A,N/A
Bathymetry prediction,1,MagicBathyNet,N/A,N/A,N/A,N/A,N/A
Procedure Step Recognition,1,IndustReal,IndustReal,F1; Delay (seconds); POS,N/A,Computer Vision,"Procedure Step Recognition (PSR) focuses on recognizing the correct completion and order of procedural steps. Unlike traditional action recognition, which lacks a measure of success for actions, PSR a..."
3D Object Detection (RoI),1,View-of-Delft,N/A,N/A,N/A,N/A,N/A
Video Captioning on MSR-VTT,1,MSRVTT-CTN,N/A,N/A,N/A,N/A,N/A
Video Captioning on MS,1,MSVD-CTN,N/A,N/A,N/A,N/A,N/A
Linear Probing Object-Level 3D Awareness,1,ImageNet3D,N/A,N/A,N/A,Computer Vision,N/A
Anomaly Instance Segmentation,1,OoDIS,N/A,N/A,N/A,N/A,N/A
Segmented Multimodal Named Entity Recognition,1,Twitter-SMNER,Twitter-SMNER,F1,N/A,N/A,N/A
Oriented Object Detctio,1,EVD4UAV,N/A,N/A,N/A,N/A,N/A
Personalized Image Generation,1,DreamBooth,DreamBooth,Overall (CP * PF); Prompt Following (PF); Concept Preservation (CP),N/A,Computer Vision,"Utilizes single or multiple images that contain the same subject or style, along with text prompt, to generate images that contain that subject as well as match the textual description. Includes finet..."
Action Classification (zero-shot),1,WiGesture,N/A,N/A,N/A,N/A,N/A
Detection of potentially void clauses,1,AGB-DE,N/A,N/A,N/A,N/A,N/A
X-ray Visual Question Answering,1,MedPromptX-VQA,"MIMIC-CXR, MIMIC-IV",F1-score,N/A,Medical,N/A
Video Style Transfer,1,StyleGallery,N/A,N/A,N/A,Computer Vision,N/A
Video Temporal Consistency,1,Temporal Logic Video (TLV) Dataset,N/A,N/A,N/A,N/A,N/A
Hand-Object Interaction Detection,1,HOI-Synth,N/A,N/A,N/A,N/A,N/A
Uncertainty-Aware Panoptic Segmentation,1,MUSES: MUlti-SEnsor Semantic perception dataset,N/A,N/A,N/A,N/A,N/A
Sequential Place Recognition,1,Wild-Places,N/A,N/A,N/A,N/A,N/A
Few-shot Instance Segmentation,1,CAMO-FS,N/A,N/A,N/A,N/A,N/A
Multi-Objective Multi-Agent Reinforcement Learning,1,MOMAland,N/A,N/A,N/A,N/A,N/A
Joint Entity and Relation Extraction on Scientific Data,1,SemOpenAlex,N/A,N/A,N/A,Natural Language Processing,N/A
Vehicle Color Recognition,1,UFPR-VCR Dataset,N/A,N/A,N/A,N/A,N/A
Fake Song Detection,1,SONICS,N/A,N/A,N/A,Music,N/A
Synthetic Song Detection,1,SONICS,N/A,N/A,N/A,Audio,N/A
Fine-Grained Image Inpainting,1,InpaintCOCO,N/A,N/A,N/A,N/A,N/A
Lifelike 3D Human Generation,1,THuman2.0 Dataset,THuman2.0 Dataset,LPIPS; PSNR; SSIM; CLIP Similarity,N/A,Computer Vision,"Generating realistic and lifelike 3D Humans from a Single RGB image input, while preserving face identity, delivering realistic texture, accurate geometry, and maintaining a valid pose of the generate..."
ENF (Electric Network Frequency) Extraction,1,WHU - Audio ENF,N/A,N/A,ENF (Electric Network Frequency) Extraction from Video,Time Series; Computer Vision,"Extraction of Electric Network Frequency, that can be done from power grid raw voltage signal or audio and video files."
ENF (Electric Network Frequency) Detection,1,WHU - Audio ENF,N/A,N/A,N/A,N/A,"Detect if into a source (for example audio, video or raw valtages) it is contained the ENF signal."
ENF (Electric Network Frequency) Extraction from Video,1,ENF moving video,N/A,N/A,N/A,N/A,N/A
Information Extraction,1,SemTabNet,SemTabNet,average Tree Similarity Score,Attribute Value Extraction; Participant Intervention Comparison Outcome Extraction; Genetic IE; Catalog Extraction; Multimodal Attribute Value Extraction,Medical; Natural Language Processing,Information extraction is the task of automatically extracting structured information from unstructured and / or semi-structured machine-readable documents and other electronically represented sources...
Singing Voice Synthesis,1,GTSinger,N/A,N/A,N/A,Music; Speech,"(Verse 1)  Sa bawat hakbang, sa bawat daan  May pangarap kang naghihintay  Westbridge ang gabay, sa iyong paglalakbay  Tungo sa kinabukasan, ng ating bayan      (Chorus)  Lagi kang kasama, sa aking pu..."
Image Stylization,1,CodeSCAN,N/A,N/A,One-Shot Face Stylization,Computer Vision,"**Image stylization** is a task that involves transforming an input image into a new image that has a different style, while preserving the content of the original image. The goal of image stylization..."
Novelty Detection,1,ADE-OoD,N/A,N/A,N/A,Natural Language Processing,Scientific Novelty Detection
Models Alignment,1,ListUltraFeedback,N/A,N/A,N/A,Knowledge Base,**Models Alignment** is the process of ensuring that multiple models used in a machine learning system are consistent with each other and aligned with the goals of the system. This involves defining c...
Political Salient Issue Orientation Detection,1,TwinViews-13k,N/A,N/A,N/A,N/A,N/A
Motion Interpolation,1,I2-2000FPS,N/A,N/A,N/A,N/A,3D human motion sequences interpolation and completion
Political Influence Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Scientific Data Usage Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Statistical Data Usage Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Data Sources Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Impacted Location Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Detecting Climate/Environmental Topics,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
News Target Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Authority Involvement Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Voice pathology detection,1,Saarbruecken Voice Database,Saarbruecken Voice Database (females); Saarbruecken Voice Database (males),UAR,N/A,Medical,N/A
Beam Prediction,1,MVX,N/A,N/A,N/A,N/A,N/A
Optimize the trajectory of UAV which plays a BS in communication system,1,MVX,N/A,N/A,N/A,Adversarial,N/A
Few-shot Video Question Answering,1,CausalChaos!,N/A,N/A,N/A,N/A,N/A
Grounded Video Question Answering,1,CausalChaos!,N/A,N/A,N/A,N/A,N/A
Violence and Weaponized Violence Detection,1,ThreatGram 101 - Extreme Telegram Data,N/A,N/A,N/A,N/A,N/A
3D Open-Vocabulary Object Detection,1,IndraEye,N/A,N/A,N/A,N/A,N/A
Anomaly Localization,1,IDE,N/A,N/A,N/A,N/A,N/A
De novo molecule generation from MS/MS spectrum,1,MassSpecGym,MassSpecGym,Top-1 Tanimoto; Top-10 Tanimoto; Top-10 Accuracy; Top-1 Accuracy; Top-1 MCES; Top-10 MCES,N/A,Miscellaneous,N/A
Molecule retrieval from MS/MS spectrum,1,MassSpecGym,MassSpecGym,Hit rate @ 20; Hit rate @ 1; MCES @ 1; Hit rate @ 5,N/A,Miscellaneous,N/A
MS/MS spectrum simulation,1,MassSpecGym,MassSpecGym,Hit Rate @ 1; Hit Rate @ 5; Hit Rate @ 20; Cosine Similarity; Jensen-Shannon Similarity,N/A,Miscellaneous,N/A
De novo molecule generation from MS/MS spectrum (bonus chemical formulae),1,MassSpecGym,MassSpecGym,Top-1 Tanimoto; Top-10 Tanimoto; Top-10 Accuracy; Top-1 Accuracy; Top-1 MCES; Top-10 MCES,N/A,Miscellaneous,N/A
MS/MS spectrum simulation (bonus chemical formulae),1,MassSpecGym,MassSpecGym,Hit Rate @ 1; Hit Rate @ 20; Hit Rate @ 5,N/A,Miscellaneous,N/A
Molecule retrieval from MS/MS spectrum (bonus chemical formulae),1,MassSpecGym,MassSpecGym,Hit rate @ 20; Hit rate @ 1; MCES @ 1; Hit rate @ 5,N/A,Miscellaneous,N/A
CSV dialect detection,1,TUD,N/A,N/A,N/A,N/A,N/A
Sports Activity Recognition,1,IM-SportingBehaviors,N/A,N/A,N/A,N/A,N/A
Production Forecasting,1,SCG,N/A,N/A,N/A,N/A,N/A
Product Relation Classification,1,SCG,N/A,N/A,N/A,N/A,N/A
Product Relation Detection,1,SCG,N/A,N/A,N/A,N/A,N/A
Open Vocabulary Image Classification,1,OVIC Datasets,N/A,N/A,N/A,N/A,N/A
Fruit-type + Maturity-state Prediction (Multi-label Classification),1,RawRipe Dataset,N/A,N/A,N/A,N/A,N/A
Railway Track Image Classification,1,Railway Track Misalignment Detection Image Dataset,N/A,N/A,N/A,N/A,N/A
ECG Denoising,1,QT-NSTDB,N/A,N/A,N/A,N/A,N/A
Synthetic Image Attribution,1,SuSy Dataset,N/A,N/A,N/A,Computer Vision,"Determine the source or origin of a generated image, such as identifying the model or tool used to create it. This information can be useful for detecting copyright infringement or for investigating d..."
Omnnidirectional Stereo Depth Estimation,1,Helvipad,N/A,N/A,N/A,N/A,N/A
Video Synopsis,1,SynoClip,N/A,N/A,N/A,N/A,N/A
Blood Detection,1,EGC-FPHFS,N/A,N/A,N/A,N/A,N/A
Few-Shot Instance Classification,1,MVTec-FS,N/A,N/A,N/A,N/A,N/A
Hyperspectral Unmixing,1,Hyper Drive,N/A,N/A,N/A,N/A,N/A
Classification Of Hyperspectral Images,1,Hyper Drive,N/A,N/A,N/A,N/A,N/A
Inductive knowledge graph completion,1,Financial Dynamic Knowledge Graph,FB15k-237-ind; Wikidata5m-ind; WN18RR-ind,Hits@10; Hits@1; Hit@1; Hit@10; Hits@3; MRR,Large Language Model,Natural Language Processing,N/A
Persuasive Writing Strategy Detection Level-1,1,Persuasive Writing Strategy,N/A,N/A,N/A,N/A,N/A
Persuasive Writing Strategy Detection Level-2,1,Persuasive Writing Strategy,N/A,N/A,N/A,N/A,N/A
Persuasive Writing Strategy Detection Level-3,1,Persuasive Writing Strategy,N/A,N/A,N/A,N/A,N/A
Persuasive Writing Strategy Detection Level-4,1,Persuasive Writing Strategy,N/A,N/A,N/A,N/A,N/A
Cross-view Referring Multi-Object Tracking,1,2024 AI City Challenge,N/A,N/A,N/A,N/A,N/A
Single-View 3D Reconstruction on ShapeNet,1,SynthEVox3D-Tiny,N/A,N/A,N/A,N/A,N/A
Music Compression,1,Cadenza Woodwind,N/A,N/A,N/A,Audio,N/A
Human-Human Interaction Recognition,1,Waldo and Wenda,N/A,N/A,N/A,N/A,N/A
Malaria Vivax Detection,1,MP-IDB,N/A,N/A,N/A,N/A,N/A
Malaria Malariae Detection,1,MP-IDB,N/A,N/A,N/A,N/A,N/A
Malaria Ovale Detection,1,MP-IDB,N/A,N/A,N/A,N/A,N/A
Handwritten Document Recognition,1,U-DIADS-Bib,N/A,N/A,N/A,N/A,N/A
ADP Prediction,1,ADP Dataset,N/A,N/A,N/A,N/A,N/A
BEV Segmentation,1,SimBEV,SimBEV,bus; car; bicycle; road; pedestrian; rider; motorcycle; mIoU; truck,N/A,Computer Vision,N/A
3D Semantic Occupancy Prediction,1,SimBEV,N/A,N/A,N/A,Computer Vision,Uses sparse LiDAR semantic labels for training and testing
Computer Vision Transduction,1,CytoImage Net Dataset,N/A,N/A,N/A,Computer Vision,Transductive learning in computer vision tasks
Image-Variation,1,Horse Racing Photo Dataset,N/A,N/A,N/A,Computer Vision,"Given an image, generate variations of the image"
Event Relation Extraction,1,MAVEN-Arg,N/A,N/A,N/A,N/A,N/A
Relation Prediction,1,MAVEN-Arg,N/A,N/A,N/A,N/A,N/A
answerability prediction,1,PeerQA,PeerQA,Macro F1,N/A,Natural Language Processing,N/A
Cross-modal place recognition,1,Street360Loc,N/A,N/A,N/A,N/A,N/A
Raw reconstruction,1,RawNIND,N/A,N/A,N/A,Computer Vision,Reconstruct RAW camera sensor readings from the corresponding sRGB images
Object Rearrangement,1,Open6DOR V2,Open6DOR V2,rot-level0; rot-level1; pos-level1; 6-DoF; rot-level2; pos-level0,N/A,Robots,N/A
WaterDrop2D,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
Sand2D,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
Goop2D,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
Elasticity3D,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
MultiMaterial2D,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
Generating 3D Point Clouds,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
Event Causality Identification,1,COLD: Causal Reasoning in Closed Daily Activities,N/A,N/A,N/A,N/A,N/A
Audio-Visual Video Captioning,1,LongVALE,N/A,N/A,N/A,N/A,N/A
Video Boundary Captioning,1,LongVALE,N/A,N/A,N/A,N/A,N/A
Surface Normals Estimation from Point Clouds,1,LiSu,N/A,N/A,N/A,Computer Vision,Parent task: 3d Point Clouds Analysis
3DGS,1,THVD,N/A,N/A,N/A,N/A,N/A
Image-to-Image Regression,1,2007 BP Anisotropic Velocity Benchmark,N/A,N/A,N/A,N/A,N/A
Safety Alignment,1,SUDO Dataset,N/A,N/A,N/A,Natural Language Processing,N/A
cross-modal alignment,1,Gaze-CIFAR-10,N/A,N/A,N/A,Computer Vision,N/A
Thermal Image Denoising,1,IRBFD,N/A,N/A,N/A,Computer Vision,N/A
Aerial Video Semantic Segmentation,1,WTA/TLA,N/A,N/A,N/A,N/A,N/A
Cyber Attack Detection,1,CAShift,N/A,N/A,N/A,Miscellaneous; Methodology,Cybersecurity attacks prediction using deep learning
Contact-rich Manipulation,1,Reactive Diffusion Policy-Dataset,N/A,N/A,N/A,N/A,N/A
Text-based Person Retrieval,1,TVPReid,N/A,N/A,N/A,Computer Vision,N/A
Disease Prediction,1,PreRAID,N/A,N/A,Disease Trajectory Forecasting; Retinal OCT Disease Classification,Medical; Computer Vision,N/A
confidence interval for traffic prediction,1,Traffic demand in NYC and CHI,N/A,N/A,N/A,Time Series,Providing confidence interval for traffic prediction
Silent Speech Recognition,1,Watch Your Mouth: Point Clouds based Speech Recognition Dataset,N/A,N/A,N/A,Speech,Interpret speech without acoustic signals
Hierarchical Text-Image Matching,1,HierarCaps,N/A,N/A,N/A,N/A,N/A
Dynamic Reconstruction,1,iPhone (Monocular Dynamic View Synthesis),iPhone (Monocular Dynamic View Synthesis),LPIPS,N/A,N/A,N/A
Vision-Language-Action,1,AutomotiveUI-Bench-4K,N/A,N/A,N/A,N/A,N/A
Dense contact estimation,1,MOW,N/A,N/A,N/A,N/A,N/A
Bone Suppression From Dual Energy Chest X-Rays,1,JSRT (negative formats),N/A,N/A,N/A,N/A,N/A
Anomaly Severity Classification (Anomaly vs. Defect),1,MVTec-AC,N/A,N/A,N/A,N/A,N/A
Video Action Detection,1,BAH,N/A,N/A,N/A,N/A,N/A
Human-Domain Subject-to-Video,1,OpenS2V-Eval,N/A,N/A,N/A,N/A,N/A
Single-Domain Subject-to-Video,1,OpenS2V-Eval,N/A,N/A,N/A,N/A,N/A
Temporal Action Segmentation,1,SICS-155,N/A,N/A,N/A,Computer Vision,Segment activities/actions temporally
Monocular 3D Object Localization,1,DTTD-Mobile,N/A,N/A,N/A,N/A,N/A
GUI Element Detection,1,CAGUI,N/A,N/A,N/A,N/A,N/A
WiFi CSI-based Image Reconstruction,1,TDMA CSI 16,N/A,N/A,N/A,N/A,N/A
Human-Object Interaction Generation,1,LLMafia,N/A,N/A,N/A,N/A,N/A
fake voice detection,1,ArVoice,ASVspoof 2019 - LA,Accuracy (%),N/A,Audio,N/A
Emotion Interpretation,1,EIBench,EIBench; EIBench (complex),Recall,N/A,Reasoning,N/A
Cross-Modal Information Retrieval,1,TED VCR,N/A,N/A,Cross-Modal Retrieval,Miscellaneous,"**Cross-Modal Information Retrieval** (CMIR) is the task of finding relevant items across different modalities. For example, given an image, find a text or vice versa. The main challenge in CMIR is kn..."
3D Architecture,1,[[contact~anytime]]How to Contact Expedia Customer Service,N/A,N/A,Denoising,Adversarial; Graphs,N/A
MRI classification,1,BRISC,N/A,N/A,N/A,N/A,N/A
Malaria Risk Exposure Prediction,0,N/A,N/A,N/A,N/A,Medical,N/A
Single-cell modeling,0,N/A,N/A,N/A,N/A,Medical,Single Cell RNA sequencing (scRNAseq) revolutionized our understanding of the fundamental of life sciences. The technology enables an unprecedented resolution to study heterogeneity in cell population...
X-Ray,0,N/A,N/A,N/A,Finding Pulmonary Nodules In Large-Scale Ct Images; Medical X-Ray Image Segmentation; Low-Dose X-Ray Ct Reconstruction; Bone Suppression From Dual Energy Chest X-Rays; Joint Vertebrae Identification And Localization In Spinal Ct Images,Medical,N/A
Facial Recognition and Modelling,0,N/A,N/A,N/A,Action Unit Detection; Face Verification; Facial Expression Recognition (FER); Face Swapping; Face Generation,Music; Computer Vision; Miscellaneous,Facial tasks in machine learning operate based on images or video frames (or other datasets) focussed on human faces.
Sketch,0,N/A,N/A,N/A,Photo-To-Caricature Translation; Drawing Pictures; Sketch Recognition; Face Sketch Synthesis,Computer Vision,N/A
Link Quality Estimation,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Image Quality Estimation,0,N/A,N/A,N/A,N/A,Computer Vision,Same as [Image Quality Assessment](https://paperswithcode.com/task/image-quality-assessment).
Image and Video Forgery Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Image Text Removal,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Document To Image Conversion,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Transform A Video Into A Comics,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Detection Of Instrumentals Musical Tracks,0,N/A,N/A,N/A,N/A,Music,N/A
Video,0,N/A,N/A,N/A,Multiple Object Tracking; Natural Language Moment Retrieval; Video Synchronization; Dynamic Region Segmentation; Video Understanding,Computer Vision,N/A
Pornography Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Typeface Completion,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Text-To-Image,0,N/A,N/A,N/A,Complex Scene Breaking and Synthesis; Story Visualization; VGSI,Computer Vision,N/A
Crowds,0,N/A,N/A,N/A,Visual Crowd Analysis; Crowd Counting; Group Detection In Crowds,Computer Vision,N/A
Visual Sentiment Prediction,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Sleep Quality,0,N/A,100 sleep nights of 8 caregivers,Accuracy,Sleep Stage Detection; Sleep Micro-event detection; Sleep apnea detection; K-complex detection; Spindle Detection,Medical,"<span style=""color:grey; opacity: 0.6"">( Image credit: [DeepSleep](https://github.com/GuanLab/DeepSleep) )</span>"
Text-Independent Speaker Recognition,0,N/A,N/A,N/A,N/A,Speech,N/A
Skin,0,N/A,N/A,N/A,Skin Lesion Classification; Skin Lesion Segmentation; Skin Lesion Identification,Medical,N/A
Clickbait Detection,0,N/A,N/A,N/A,N/A,Playing Games; Natural Language Processing,"Clickbait detection is the task of identifying clickbait, a form of false advertisement, that uses hyperlink text or a thumbnail link that is designed to attract attention and to entice users to follo..."
Frame Duplication Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Automatic Machine Learning Model Selection,0,N/A,N/A,N/A,Smart Grid Prediction,Miscellaneous; Methodology,N/A
Flooded Building Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Manner Of Articulation Detection,0,N/A,N/A,N/A,N/A,Speech,N/A
Detecting Shadows,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Motion Detection In Non-Stationary Scenes,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Multi-modal image segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Constrained Diffeomorphic Image Registration,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Audio Signal Recognition,0,N/A,N/A,N/A,Gunshot Detection,Audio,N/A
Marine Robot Navigation,0,N/A,N/A,N/A,N/A,Robots,N/A
Trademark Retrieval,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Speaking Style Synthesis,0,N/A,N/A,N/A,N/A,Speech,N/A
Yield Mapping In Apple Orchards,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Population Mapping,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Air Quality Inference,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Modeling Local Geometric Structure,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Muscular Movement Recognition,0,N/A,N/A,N/A,N/A,Medical,N/A
Dynamic Texture Recognition,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Overlapping Mention Recognition,0,N/A,N/A,N/A,N/A,Natural Language Processing,Overlapping mention recognition is the task of correctly identifying all mentions of an entity in the presence of overlapping entity mentions.
Sar Image Despeckling,0,N/A,N/A,N/A,N/A,Computer Vision,Despeckling is the task of suppressing speckle from Synthetic Aperture Radar (SAR) acquisitions.    Image credits: GRD Sentinel-1 SAR image despeckled with [SAR2SAR-GRD](https://arxiv.org/abs/2102.006...
Image Declipping,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Image Steganography,0,N/A,N/A,N/A,N/A,Computer Vision,"**Image Steganography** is the main content of information hiding. The sender conceal a secret message into a cover image, then get the container image called stego, and finish the secret message’s tr..."
Multiple Sequence Alignment,0,N/A,SABMark v1.65; BAliBASE v3; OXBench,Total Column Score,N/A,Medical,N/A
White Matter Fiber Tractography,0,N/A,N/A,N/A,N/A,Medical,N/A
Abstract Argumentation,0,N/A,N/A,N/A,N/A,Reasoning; Natural Language Processing,Identifying argumentative statements from natural language dialogs.
Optimal Motion Planning,0,N/A,N/A,N/A,N/A,Robots,N/A
Hyperspectral,0,N/A,N/A,N/A,Classification Of Hyperspectral Images; Hyperspectral Image Segmentation; Hyperspectral Unmixing; Hyperspectral Image Classification,Computer Vision,N/A
Photometric Redshift Estimation,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Vector Quantization (k-means problem),0,N/A,N/A,N/A,N/A,Miscellaneous,Given a data set $X$ of d-dimensional numeric vectors and a number $k$ find a codebook $C$ of $k$ d-dimensional vectors such that the sum of square distances of each $x \in X$ to the respective neares...
Spectral Graph Clustering,0,N/A,N/A,N/A,N/A,Graphs,N/A
Spectrum Cartography,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Hypergraph Matching,0,N/A,N/A,N/A,N/A,Graphs,N/A
Video Games,0,N/A,N/A,N/A,Dota 2; SNES Games; Real-Time Strategy Games; Starcraft II; FPS Games,Playing Games,N/A
Image Instance Retrieval,0,N/A,N/A,N/A,Amodal Instance Segmentation,Computer Vision,"**Image Instance Retrieval** is the problem of retrieving images from a database representing the same object or scene as the one depicted in a query image.   <span class=""description-source"">Source: ..."
Spectral Estimation,0,N/A,N/A,N/A,Spectral Estimation From A Single Rgb Image,Computer Vision,N/A
Metal Artifact Reduction,0,N/A,N/A,N/A,N/A,Medical,Metal artifact reduction aims to remove the artifacts introduced by metallic implants in CT images.
GAN image forensics,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Counterspeech Detection,0,N/A,Youtube counterspeech dataset,F1 score,N/A,Natural Language Processing,"Counter-speech detection is the task of detecting counter-speech, i.e., a crowd-sourced response that argues, disagrees, or presents an opposing view to extremism or hateful content on social media pl..."
Video Correspondence Flow,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Ultrasound,0,N/A,Brain Anatomy US,Dice,Brain Ventricle Localization And Segmentation In 3D Ultrasound Images,Medical,N/A
Spoof Detection,0,N/A,ASVspoof 2019 - LA; ASVspoof 2019 - PA,t-DCF; EER,Detecting Image Manipulation; Cross-Domain Iris Presentation Attack Detection; Face Presentation Attack Detection; Finger Dorsal Image Spoof Detection,Computer Vision,N/A
3D,0,N/A,N/A,N/A,Pose Estimation; Video Inpainting; 3D Shape Recognition; 3D Shape Representation; Object Detection,Methodology; Time Series; Computer Vision; Knowledge Base; Miscellaneous; Music; Playing Games,N/A
Visual Recognition,0,N/A,N/A,N/A,Fine-Grained Visual Recognition,Computer Vision,N/A
Image Recognition,0,N/A,N/A,N/A,Fine-Grained Image Recognition; Material Recognition; License Plate Recognition,Computer Vision,N/A
Shape Representation Of 3D Point Clouds,0,N/A,N/A,N/A,3D Point Cloud Reconstruction,Computer Vision,N/A
Kiss Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Natural Language Transduction,0,N/A,N/A,N/A,Lipreading,Computer Vision; Natural Language Processing,Converting one sequence into another
Time Series Denoising,0,N/A,N/A,N/A,N/A,Time Series,N/A
Smart Grid Prediction,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Crowd Flows Prediction,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Medical Super-Resolution,0,N/A,N/A,N/A,N/A,Medical,N/A
Cancer Metastasis Detection,0,N/A,N/A,N/A,N/A,Medical,N/A
Mitosis Detection,0,N/A,N/A,N/A,N/A,Medical,N/A
Pulsar Prediction,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Weakly-supervised panoptic segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Unbalanced Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Vision-based navigation with language-based assistance,0,N/A,N/A,N/A,N/A,Robots,A grounded vision-language task where an agent with visual perception is guided via language to find objects in photorealistic indoor environments. The task emulates a real-world scenario in that (a) ...
Phrase Vector Embedding,0,N/A,N/A,N/A,N/A,Natural Language Processing,"Just like the generation of word (1-gram) vector embedding, this task is for phrase (n-gram) vector embedding."
Image Imputation,0,N/A,N/A,N/A,N/A,Computer Vision,"Image imputation is the task of creating plausible images from low-resolution images or images with missing data.    <span style=""color:grey; opacity: 0.6"">( Image credit: [NASA](https://www.jpl.nasa...."
Normalising Flows,0,N/A,N/A,N/A,N/A,Methodology,N/A
Knee Osteoarthritis Prediction,0,N/A,N/A,N/A,N/A,Medical,N/A
Readmission Prediction,0,N/A,N/A,N/A,N/A,Medical,N/A
Gravitational Wave Detection,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
4D Spatio Temporal Semantic Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,Image: [Choy et al](https://paperswithcode.com/paper/4d-spatio-temporal-convnets-minkowski)
incongruity detection,0,N/A,N/A,N/A,N/A,Natural Language Processing,Incongruity detection is the task of identifying statements in a text that are inconsistent with each other.
Text Attribute Transfer,0,N/A,N/A,N/A,N/A,Natural Language Processing,"The goal of the **Text Attribute Transfer** task is to change an input text such that the value of a particular linguistic attribute of interest (e.g. language = English, sentiment = Positive) is tran..."
Relation Mention Extraction,0,N/A,N/A,N/A,N/A,Natural Language Processing,Extracting phrases representative for a specific relation.
Image-To-Gps Verification,0,N/A,N/A,N/A,N/A,Computer Vision,"The image-to-GPS verification task asks whether a given image is taken at a claimed GPS location.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Image-to-GPS Verification Through A Bottom-..."
X-Ray Diffraction (XRD),0,N/A,N/A,N/A,N/A,Miscellaneous,"Diffraction of X-ray patterns and images, with common applications for materials and images."
Pulmonary Embolism Detection,0,N/A,PE-CAD FPRED,AUC,N/A,Medical,N/A
JPEG Artifact Removal,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Speculation Detection,0,N/A,N/A,N/A,Speculation Scope Resolution,Natural Language Processing,Identifying information in text that is speculative as opposed to factual information.
Acoustic Novelty Detection,0,N/A,A3Lab PASCAL CHiME,F1,N/A,Audio,"Detect novel events given acoustic signals, either in domestic or outdoor environments."
Image Morphing,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Value prediction,0,N/A,Py150,MRR,Body Mass Index (BMI) Prediction,Computer Vision; Computer Code,N/A
Triad Prediction,0,N/A,N/A,N/A,N/A,Graphs,N/A
Thai Word Segmentation,0,N/A,BEST-2010; WS160,F1-score; F1-Score,N/A,Natural Language Processing,Thai word segmentation
Unsupervised Speech Recognition,0,N/A,N/A,N/A,N/A,Speech,N/A
Diabetic Foot Ulcer Detection,0,N/A,N/A,N/A,N/A,Medical,N/A
Camouflage Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Manufacturing Quality Control,0,N/A,N/A,N/A,N/A,Computer Vision,AI for Quality control in manufacturing processes.
Sequential Distribution Function Estimation,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Multi Diseases Detection,0,N/A,N/A,N/A,N/A,Medical,N/A
Image popularity prediction,0,N/A,N/A,N/A,N/A,N/A,N/A
Low-rank compression,0,N/A,N/A,N/A,N/A,Computer Code,N/A
Pre-election ratings estimation,0,N/A,N/A,N/A,N/A,Reasoning,N/A
Quantum Circuit Mapping,0,N/A,N/A,N/A,N/A,Methodology,Mapping quantum circuits to quantum devices
Prototype Selection,0,N/A,N/A,N/A,N/A,N/A,N/A
Matrix Factorization / Decomposition,0,N/A,N/A,N/A,N/A,N/A,N/A
Template Matching,0,N/A,N/A,N/A,N/A,Computer Vision,Template matching is a technique that is used to find a subimage or a patch (called the template) within a larger image. The basic idea behind template matching is to slide the template image over the...
Detection of Dependencies,0,N/A,N/A,N/A,Detection of Higher Order Dependencies,Methodology,N/A
Text Compression,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Extractive Tags Summarization,0,N/A,N/A,N/A,N/A,Natural Language Processing,"The goal of Extractive Tags Summarization (ETS) task is to shorten the list of tags corresponding to a digital image while keeping the representativity; i.e., is to extract important tags from the con..."
Face Selection,0,N/A,N/A,N/A,N/A,Natural Language Processing,A task where an agent should select at most two sentences from the paper as argumentative facts.
Variable Selection,0,N/A,N/A,N/A,N/A,Methodology,N/A
3D Rotation Estimation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Seismic source localization,0,N/A,N/A,N/A,N/A,Time Series,Locating a seismic source using seismometer recordings
Structual Feature Correlation,0,N/A,N/A,N/A,N/A,Graphs,Expressive Power of GNN to predict structural feature's correlation mutually.
Automated Pulmonary Nodule Detection And Classification,0,N/A,N/A,N/A,Pulmonary Nodules Classification,Medical,N/A
Photoplethysmogram simulation,0,N/A,N/A,N/A,N/A,Medical,Simulating photoplethysmogram (PPG) signals
Chinese Spelling Error Correction,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Vietnamese Word Segmentation,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Partially View-aligned Multi-view Learning,0,N/A,Caltech101; Reuters En-Fr; Scene-15; n-MNIST,NMI,N/A,Computer Vision,"In multi-view learning, Partially View-aligned Problem (PVP) refers to the case when only a portion of data is aligned, thus leading to data inconsistency."
Prostate Zones Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Cognate Prediction,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Bladder Segmentation,0,N/A,N/A,N/A,N/A,Medical,N/A
Bokeh Effect Rendering,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Design Synthesis,0,N/A,N/A,N/A,N/A,Adversarial,N/A
Touch detection,0,N/A,N/A,N/A,N/A,Robots,N/A
Job Prediction,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
road scene understanding,0,N/A,N/A,N/A,Monocular Cross-View Road Scene Parsing(Road); Monocular Cross-View Road Scene Parsing(Vehicle),Computer Vision,N/A
Control with Prametrised Actions,0,N/A,Half Field Offence; Platform; Robot Soccer Goal,Goal Probability; Return,N/A,Playing Games,"Most reinforcement learning research papers focus on environments where the agent’s actions are either discrete or continuous. However, when training an agent to play a video game, it is common to enc..."
3D Character Animation From A Single Photo,0,N/A,N/A,N/A,Scene Recognition,Computer Vision,Image: [Weng et al](https://arxiv.org/pdf/1812.02246v1.pdf)
Image Deblocking,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Action Analysis,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Table Extraction,0,N/A,N/A,N/A,Table Functional Analysis,Miscellaneous,Table extraction involves detecting and recognizing a table's logical structure and content from its unstructured presentation within a document
Protein-Ligand Affinity Prediction,0,N/A,CSAR-HiQ; PDBbind,RMSE,MHC presentation prediction,Medical,N/A
Dynamic Community Detection,0,N/A,N/A,N/A,N/A,Graphs,community detection in dynamic networks
Code Reuse Detection,0,N/A,N/A,N/A,N/A,N/A,N/A
2D Classification,0,N/A,N/A,N/A,Object Detection; Vocabulary-free Image Classification; Multi-label Image Recognition with Partial Labels; Anime; Cell Detection,Methodology; Computer Code; Adversarial; Computer Vision; Audio; Playing Games; Music; Medical; Graphs,N/A
3D Bin Packing,0,N/A,N/A,N/A,N/A,Miscellaneous,"As a classic NP-hard problem, the bin packing problem (1D-BPP) seeks for an assignment of a collection of items with various weights to bins. The optimal assignment houses all the items with the fewes..."
3D Surface Generation,0,N/A,N/A,N/A,Visibility Estimation from Point Cloud,Computer Vision,Image: [AtlasNet](https://arxiv.org/pdf/1802.05384v3.pdf)
Programming Error Detection,0,N/A,N/A,N/A,N/A,Computer Code,N/A
SET TO GRAPH PREDICTION,0,N/A,N/A,N/A,N/A,Graphs,N/A
Set-to-Graph Prediction,0,N/A,N/A,N/A,N/A,Graphs,N/A
Hyperedge Prediction,0,N/A,N/A,N/A,N/A,Graphs,"Hyperlink/hyperedge prediction, targets to find missing hyperedges in a hypergraph."
Obfuscation Detection,0,N/A,N/A,N/A,N/A,N/A,N/A
Audio inpainting,0,N/A,N/A,N/A,N/A,Audio,Filling in holes in audio data
Linear Mode Connectivity,0,N/A,N/A,N/A,N/A,Knowledge Base,"**Linear Mode Connectivity** refers to the relationship between input and output variables in a linear regression model. In a linear regression model, input variables are combined with weights to pred..."
Open Set Video Captioning,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Vietnamese Aspect-Based Sentiment Analysis,0,N/A,N/A,N/A,Sentiment Dependency Learning,Natural Language Processing,"UIT-ViSFD: A Vietnamese Smartphone Feedback Dataset for Aspect-Based Sentiment Analysis       In this paper, we present a process of building a social listening system based on aspect-based sentiment ..."
Manufacturing simulation,0,N/A,N/A,N/A,N/A,Knowledge Base,Simulation of manufacturing system for applying AI methods and big data analysis
3D Prostate Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Video Propagation,0,N/A,N/A,N/A,N/A,Computer Vision,Propagating information in processed frames to unprocessed frames
Computer Architecture and Systems,0,N/A,N/A,N/A,N/A,N/A,N/A
Video Individual Counting,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Earthquake prediction,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
"Deep-Sea Treasure, Image version",0,N/A,N/A,N/A,N/A,Playing Games,"Image state version of the multi-objective reinforcement learning toy environment originally introduced in ""Empirical evaluation methods for multiobjective reinforcement learning algorithms"" by P. Vam..."
Spoken Command Recognition,0,N/A,Speech Command v2,Accuracy,N/A,Speech,N/A
Graph Outlier Detection,0,N/A,N/A,N/A,N/A,Graphs,N/A
3D Canonicalization,0,N/A,N/A,N/A,N/A,Computer Vision,3D Canonicalization is the process of estimating a transformation-invariant  feature for classification and part segmentation tasks.
Unsupervised 3D Point Cloud Linear Evaluation,0,N/A,N/A,N/A,N/A,Computer Vision,Training a linear classifier(e.g. SVM) on the representations learned in an unsupervised manner on the pretrained(e.g. ShapeNet) dataset.
Hate Intensity Prediction,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Referring Image Matting,0,N/A,N/A,N/A,Referring Image Matting (Expression-based); Referring Image Matting (RefMatte-RW100); Referring Image Matting (Prompt-based); Referring Image Matting (Keyword-based),Computer Vision,"Extracting the meticulous alpha matte of the specific object from the image that can best match the given natural language description, e.g., a keyword or a expression."
Inductive Bias,0,N/A,N/A,N/A,N/A,N/A,N/A
Medical waveform analysis,0,N/A,N/A,N/A,Electromyography (EMG); Electrocardiography (ECG),Medical; Methodology,"Information extraction from medical waveforms such as the electrocardiogram (ECG), arterial blood pressure (ABP) central venous pressure (CVP), photoplethysmogram (PPG, Pleth)."
Reverse Dictionary,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Image Retouching,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Image Fusion,0,N/A,N/A,N/A,Multi Focus Image Fusion; Pansharpening,Computer Vision,"Image fusion is a process in computational imaging where information from multiple images, often from different imaging modalities or spectral bands, is combined into a single image. The aim is to enh..."
Situation Recognition,0,N/A,imSitu,Top-1 Verb; Top-1 Verb & Value; Top-5 Verbs; Top-5 Verbs & Value,Grounded Situation Recognition,Computer Vision,"Situation Recognition aims to produce the structured image summary which describes the primary activity (verb), and its relevant entities (nouns)."
speed predict,0,N/A,N/A,N/A,N/A,N/A,N/A
Image Retargeting,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Continual Named Entity Recognition,0,N/A,N/A,N/A,FG-1-PG-1,Natural Language Processing,Continual learning for named entity recogntion
RGB-D Reconstruction,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Monocular Reconstruction,0,N/A,N/A,N/A,N/A,N/A,N/A
Transductive Learning,0,N/A,N/A,N/A,N/A,N/A,"In this setting, both a labeled training sample and an (unlabeled) test sample are provided at training time. The goal is to predict only the labels of the given test instances as accurately as possib..."
Concave shapes,0,N/A,N/A,N/A,N/A,N/A,N/A
Friction,0,N/A,N/A,N/A,N/A,N/A,N/A
Contact mechanics,0,N/A,N/A,N/A,N/A,N/A,N/A
HYPERVIEW Challenge,0,N/A,N/A,N/A,N/A,Computer Vision,The objective of this challenge is to advance the state of the art for soil parameter retrieval from hyperspectral data in view of the upcoming Intuition-1 mission. A campaign took place in March 2021...
Aggregate xView3 Metric,0,N/A,N/A,N/A,N/A,Computer Vision,"The aggregate xView3 metric is the combination of five metrics: object detection F1 score, close-to-shore object detection F1 score, vessel/not vessel classification F1 score, fishing/not fishing clas..."
Image Deep Networks,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Solar Flare Prediction,0,N/A,N/A,N/A,N/A,Time Series,Solar flare prediction in heliophysics
Detect Ground Reflections,0,N/A,N/A,N/A,N/A,Miscellaneous,This task helps in detecting the significant ground reflections at mm-wave bands. The harvested ground reflections can help in overcoming transient blockages at mm-wave bands
3D Inpainting,0,N/A,N/A,N/A,N/A,Computer Vision,"**3D Inpainting** is the removal of unwanted objects  from a 3D scene, such that the replaced region is visually  plausible and consistent with its context."
Conformal Prediction,0,N/A,N/A,N/A,N/A,Computer Vision,Conformal Prediction is a machine learning framework that provides valid measures of confidence for individual predictions. It offers a principled approach to quantify uncertainty in predictions witho...
Pcl Detection,0,N/A,N/A,N/A,SemEval-2022 Task 4-1 (Binary PCL Detection); SemEval-2022 Task 4-2 (Multi-label PCL Detection),Music; Miscellaneous; Natural Language Processing,N/A
Spectral Efficiency Analysis of Uplink-Downlink Decoupled Access in C-V2X Networks,0,N/A,N/A,N/A,N/A,Computer Code,Code for Spectral Efficiency Analysis of Uplink-Downlink Decoupled Access in C-V2X Networks
Finger Vein Recognition,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Metadata quality,0,N/A,N/A,N/A,N/A,Miscellaneous,This task consists of the automatic assessment of the quality of the metadata of an information system.
single-image-generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
human-scene contact detection,0,N/A,N/A,N/A,N/A,Computer Vision,detecting contact between human bodies and scenes
Symmetric face inpainting,0,N/A,N/A,N/A,N/A,N/A,N/A
Generative Semantic Nursing,0,N/A,N/A,N/A,N/A,Methodology,**Generative Semantic Nursing** is a task of intervening the generative process on the fly during inference time to improve the faithfulness of the generated images. It works by carefully manipulating...
Calving Front Delineation From Synthetic Aperture Radar Imagery,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Multi-Objective Reinforcement Learning,0,N/A,N/A,N/A,N/A,N/A,N/A
Twinwidth Contraction Sequence,0,N/A,N/A,N/A,N/A,Graphs,"**Twinwidth Contraction Sequence** is a concept in graph theory and computer science that refers to a sequence of graph contractions that transform a graph into a smaller, more manageable graph. The g..."
Instrument Playing Technique Detection,0,N/A,N/A,N/A,N/A,Music,N/A
Zero-shot Text-to-Video Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Class-Incremental Semantic Segmentation,0,N/A,N/A,N/A,Overlapped 15-5; Overlapped 100-50; Overlapped 100-10; Disjoint 10-1; Overlapped 15-1,Computer Vision,Semantic segmentation with continous increments of classes.
Robust Traffic Prediction,0,N/A,N/A,N/A,N/A,Time Series,N/A
Motion Magnification,0,N/A,N/A,N/A,N/A,Computer Vision,"Motion magnification is a technique that acts like a microscope for visual motion. It can amplify subtle motions in a video sequence, allowing for visualization of deformations that would otherwise be..."
Video Relationship,0,N/A,N/A,N/A,N/A,N/A,N/A
Spatio-temporal Scene Graphs,0,N/A,N/A,N/A,N/A,N/A,N/A
Adversarial Attack Detection,0,N/A,N/A,N/A,N/A,Knowledge Base,The detection of adversarial attacks.
continual anomaly detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Muscle-Computer Interfaces (MCIs),0,N/A,N/A,N/A,Low-latency processing,Robots,An interaction methodology that directly senses and decodes human muscular activity rather than relying on physical device actuation or user actions that are externally visible or audible.
Emotion Detection and Trigger Summarization,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
3D Molecule Generation,0,N/A,N/A,N/A,N/A,Medical,N/A
Multilingual Text-to-Image Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Crosslingual Text-to-Image Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Multi-lingual Text-to-Image Generation,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Cross-lingual Text-to-Image Generation,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Dielectric Constant,0,N/A,N/A,N/A,N/A,Miscellaneous,"Materials with high-dielectric constant easily polarize under external electric fields, allowing them to perform essential functions in many modern electronic devices."
Wearable Activity Recognition,0,N/A,N/A,N/A,N/A,Time Series,N/A
Forward reaction prediction,0,N/A,Mol-Instruction,Exact; Validity; METEOR; Morgan FTS,N/A,Medical,"Forward reaction prediction pertains to the anticipatory determination of the probable product(s) of a chemical reaction, given specific reactants and reagents.   This facilitates the optimization of ..."
Reagent Prediction,0,N/A,Mol-Instruction,Exact; Validity; METEOR; Morgan FTS,N/A,Medical,"Reagent prediction endeavors to ascertain the suitable catalysts, solvents, or ancillary substances required for a specific chemical reaction.  This endeavor facilitates chemists in uncovering novel r..."
Property Prediction,0,N/A,N/A,N/A,N/A,Medical,Property prediction involves forecasting or estimating a molecule's inherent physical and chemical properties based on information derived from its structural characteristics.   It facilitates high-th...
Catalytic activity prediction,0,N/A,N/A,N/A,N/A,Medical,"The EC number, a numerical classification system for enzymes hinging on the chemical reactions they catalyze, is substituted with the corresponding reaction. This substitution aims to leverage the tac..."
Domain/Motif Prediction,0,N/A,N/A,N/A,N/A,Medical,"The domain prediction task which tasks language models with the identification of the domain type within a given protein sequence, which is defined as a compact folded three-dimensional structure."
Functional Description Generation,0,N/A,N/A,N/A,N/A,Medical,"The functional description generation task, which not only evaluates the reasoning capability of the language model in determining the function of a protein sequence but also assesses the efficacy of ..."
Chemical-Disease Interaction Extraction,0,N/A,N/A,N/A,N/A,Medical,"The goal of this task is to discern the relationships between chemicals and diseases from given medical literature, a concept known as chemical-induced disease (CID) relations. These CID relations are..."
Chemical-Protein Interaction Extraction,0,N/A,N/A,N/A,N/A,Medical,The models are presented with excerpts from scientific literature and are required to not only identify distinct chemicals within the text but also to discern the specific nature of the interactions b...
Transcription Factor Binding Site Prediction,0,N/A,690 ChIP-seq,AUC-ROC,Transcription Factor Binding Site Prediction (Human); Transcription Factor Binding Site Prediction (Mouse),Medical,N/A
Visually Guided Sound Source Separation,0,N/A,N/A,N/A,N/A,Audio,The task of visually guided sound source separation (also referred as audio-visual sound separation or visual sound separation) aims to recover sound components from a mixture audio with the aid of vi...
Computing Characateristic Functions,0,N/A,N/A,N/A,N/A,Time Series,N/A
Label Error Detection,0,N/A,TREC-6,Accuracy,N/A,Miscellaneous,Identify labeling errors in data
Point- of-no-return (PNR) temporal localization,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Motion Expressions Guided Video Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,"Given a video and an expression describing the motion clues of the target object(s), MeViS requires to segment and track the target object(s) accurately."
Reconstruction Attack,0,N/A,N/A,N/A,N/A,Adversarial,"Facial reconstruction attack of facial manipulation models such as: Face swapping models, anonymization models, etc."
Zero-Shot Visual Question Answring,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Adversarial Attack on Video Classification,0,N/A,N/A,N/A,N/A,Adversarial,Use optimizer to add disturbe on video frame to fool the video classification systems. The key issue exist in tremendos calculation and the selection of key frame and key area.
Train Ego-Path Detection,0,N/A,N/A,N/A,N/A,Computer Vision,Refer to https://arxiv.org/abs/2403.13094 for the task description.
RNA 3D STRUCTURE PREDICTION,0,N/A,N/A,N/A,N/A,Medical,N/A
Japanese Word Segmentation,0,N/A,BCCWJ,F1-score (Word),N/A,Natural Language Processing,N/A
Task and Motion Planning,0,N/A,N/A,N/A,N/A,Robots,N/A
gaze redirection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Image Operation Chain Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Specular Reflection Mitigation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Model Predictive Control,0,N/A,N/A,N/A,N/A,Time Series,N/A
Personality Trait Recognition by Face,0,N/A,First Impressions v2,CCC; mAcc,N/A,Computer Vision,N/A
Only Connect Walls Dataset Task 2 (Connections),0,N/A,N/A,N/A,N/A,Natural Language Processing,Finding the connection between 4 clues
Unsupervised Zero-Shot Panoptic Segmentation,0,N/A,COCO val2017,RQ; SQ; PQ,N/A,Computer Vision,N/A
Micro-video recommendations,0,N/A,N/A,N/A,N/A,N/A,N/A
MLLM Aesthetic Evaluation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Derendering,0,N/A,N/A,N/A,N/A,Computer Vision,Convert offline handwriting (images) to online handwriting (digital ink).
Climate Projection,0,N/A,N/A,N/A,N/A,Time Series,Simulation of Earth's climate for future decades.
Mutational/Variant Effect Prediction,0,N/A,N/A,N/A,N/A,Medical,Using sequence or any other variant-specific information for organisms to determine the phenotypic change compared to wild-type variant.
Computer Vision Techniques Adopted in 3D Cryogenic Electron Microscopy,0,N/A,N/A,N/A,Cryogenic Electron Tomography; Single Particle Analysis,Computer Vision,Computer Vision Techniques Adopted in 3D Cryogenic Electron Microscopy
Scene Text Editing,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Brain Computer Interface,0,N/A,N/A,N/A,Motor Imagery; SSVEP; ERP; channel selection,Medical; Time Series,"A Brain-Computer Interface (BCI), also known as a Brain-Machine Interface (BMI), is a technology that enables direct communication between the brain and an external device, such as a computer or a mac..."
Motor Imagery,0,N/A,N/A,N/A,Within-Session Motor Imagery,Medical,"Classification of examples recorded under the Motor Imagery paradigm, as part of Brain-Computer Interfaces (BCI).    A number of motor imagery datasets can be downloaded using the MOABB library: [moto..."
Within-Session Motor Imagery,0,N/A,N/A,N/A,Within-Session Motor Imagery (all classes); Within-Session Motor Imagery (left hand vs. right hand); Within-Session Motor Imagery (right hand vs. feet),Medical,MOABB's Within-Session evaluation for the Motor Imagery paradigm.    Evaluation details: [http://moabb.neurotechx.com/docs/generated/moabb.evaluations.WithinSessionEvaluation.html](http://moabb.neurot...
Social Media Mental Health Detection,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Vietnamese Fact Checking,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
ENF (Electric Network Frequency) Classification,0,N/A,N/A,N/A,N/A,N/A,Electric Network Grid classification of ENF signal
Vietnamese Scene Text,0,N/A,N/A,N/A,N/A,Computer Vision; Natural Language Processing,N/A
Grounded Multimodal Named Entity Recognition,0,N/A,Twitter-GMNER,F1,N/A,Computer Vision,N/A
spatial-aware image editing,0,N/A,N/A,N/A,N/A,Computer Vision,Spatial-aware image editing is an image editing technique that recognizes and utilizes the spatial relationships within various regions of an image to achieve more precise and natural editing effects.
Multi Class Classification (Four-level Video Classification),0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Semantic Compression,0,N/A,N/A,N/A,N/A,N/A,N/A
Anatomical Landmark Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Speech Interruption Detection,0,N/A,N/A,N/A,N/A,Speech,"""Overlapping speech is a natural and frequently occurring phenomenon in human-human conversations with an underlying  purpose. Speech overlap events may be categorized as competitive and non-competiti..."
Red Wine Quality Prediction,0,N/A,N/A,N/A,N/A,N/A,N/A
Skeleton Rig Prediction,0,N/A,N/A,N/A,N/A,Graphs,Automated methods for producing animation rigs from input 3d models.
Weakly Supervised Referring Expression Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,RES with less percentage of ground truth annotations
Sound Source Localization,0,N/A,^(#$!@#$)(()))******,0..5sec,N/A,Audio,N/A
Vietnamese Hate Speech Detection,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Onset Detection,0,N/A,N/A,N/A,N/A,Music,N/A
Video/Text-to-Audio Generation,0,N/A,N/A,N/A,N/A,Audio,N/A
Structural Health Monitoring,0,N/A,N/A,N/A,N/A,N/A,N/A
3D Shape Reconstruction from Videos,0,N/A,N/A,N/A,DeepFake Detection,Music; Medical; Computer Vision; Robots,N/A
Exposure Correction,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Equilibrium Reaction Energy (ev/atom),0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
3D Video Frame Interpolation,0,N/A,N/A,N/A,Video Style Transfer,Time Series; Playing Games,N/A
Vietnamese Speech Recognition,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Multi-Dialect Vietnamese,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
entity_extraction,0,N/A,N/A,N/A,Pharmacovigilance,Medical,N/A
Image Editing Dection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Numerical Weather Prediction,0,N/A,N/A,N/A,N/A,Time Series,N/A
Multi-Loss Function Penalizing,0,N/A,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Image Regression,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Action Quality Assessment Report Generation,0,N/A,N/A,N/A,N/A,Computer Vision,"Generate full length action quality assessment reports containing evidence for trustworthy, comprehensive, explainable and unbiased action quality assessment."
Active 3D Reconstruction,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Structured Output Generation,0,N/A,N/A,N/A,N/A,Miscellaneous,Generating Outputs adhering to a given structure.
Battery cycle life prediction,0,N/A,N/A,N/A,N/A,Time Series,"Battery degradation remains a critical challenge in the pursuit of green technologies and sustainable energy solutions. Despite significant research efforts, predicting battery capacity loss accuratel..."
Emotion Detection and Classification,0,N/A,N/A,N/A,N/A,N/A,N/A
Knowledge Base Construction,0,N/A,N/A,N/A,N/A,Knowledge Base,N/A
Relevance Detection,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Temperature Prediction Using Specklegrams,0,N/A,FSS Dataset,Average MAE,N/A,Computer Vision,N/A
Zero-shot skeleton-based action recognition,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Subject-driven Video Generation,0,N/A,N/A,N/A,Human Animation; Audio-Driven Body Animation,Computer Vision,N/A
Reasoning Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Reasoning Video Object Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
High-Level Synthesis,0,N/A,N/A,N/A,N/A,N/A,N/A
Drawing Pictures,0,N/A,N/A,N/A,Style Transfer,Computer Vision,N/A
Blind Image Deblurring,0,N/A,N/A,N/A,Deblurring,Computer Vision; Computer Code,"**Blind Image Deblurring** is a classical problem in image processing and computer vision, which aims to recover a latent image from a blurred input.      <span class=""description-source"">Source: [Lea..."
Inductive Learning,0,N/A,N/A,N/A,N/A,Methodology,N/A
How To Refund A Wrong Transaction In Phonepe ,0,N/A,N/A,N/A,Community Question Answering; How to refund a wrong transaction in PhonePe,Methodology; Knowledge Base,N/A
Referring Multi-Object Tracking,0,N/A,N/A,N/A,Cross-view Referring Multi-Object Tracking,Computer Vision,N/A
Citation Visualization,0,N/A,N/A,N/A,N/A,Knowledge Base,"The visualization, and possibly further analysis, of scholarly citations."
multimodal interaction,0,N/A,N/A,N/A,N/A,Robots,N/A
Single Image Deblurring,0,N/A,N/A,N/A,N/A,N/A,N/A
Binding Site Prediction,0,N/A,N/A,N/A,Antibody-antigen binding prediction,Medical,N/A
Continued fraction,0,N/A,N/A,N/A,N/A,Miscellaneous; Methodology,N/A
Writer Retrieval,0,N/A,N/A,N/A,N/A,N/A,N/A
Video Forecasting,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Philosophical Reflection,0,N/A,N/A,N/A,N/A,Natural Language Processing,"Philosophical Reflection refers to the ability of AI systems to generate, evolve, or respond to deep conceptual and existential questions, often beyond factual reasoning. This includes reflections on ..."
multidimensional harmonic retrieval,0,N/A,N/A,N/A,N/A,N/A,N/A
Zero-day intrusion detection,0,N/A,N/A,N/A,N/A,Miscellaneous,Zero-day or new attacks detection
Full reference image quality assessment,0,N/A,KADID10K; DRIQ; TID2008; ESPL,PLCC; SRCC,N/A,Computer Vision,The goal is to calculate an objective quality score for a given (potentially) distorted image given its reference (potentially) pristine quality image is available. Training-free metrics are listed.
Drift Detection,0,N/A,N/A,N/A,N/A,N/A,N/A
Nested Term Recognition,0,N/A,N/A,N/A,Nested Term Recognition from Flat Supervision,Natural Language Processing,N/A
Quantitative MRI,0,N/A,N/A,N/A,Diffusion  MRI,Medical,N/A
Protein Stability Prediction,0,N/A,N/A,N/A,N/A,Medical,Predicting relative stabilities typically between mutants of the same protein.
Retrieval-augmented Generation,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Forgery Image Detection,0,N/A,N/A,N/A,N/A,Computer Vision,Detecting Forgery Images Generated By LLMs
ObjectGoal Navigation,0,N/A,N/A,N/A,Heuristic Search,Computer Code,ObjectGoal Navigation
3D Mesh Denoising,0,N/A,N/A,N/A,3D Noise Generation,Computer Vision,"3D Mesh Denoising involves removing distortions—referred to as noise—introduced during 3D scanning and reconstruction processes, which cause spatial displacement of mesh vertices. The goal is to elimi..."
ECG signal quality,0,N/A,N/A,N/A,N/A,N/A,N/A
Query focused video summarization,0,N/A,N/A,N/A,N/A,Computer Vision,"Model takes a long video and a query in the following forms(image, textual, video) and based on query model generates video summary relevant to given query."
Referring Audio-Visual Segmentation,0,N/A,N/A,N/A,N/A,Computer Code,N/A
Streaming video understanding,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Pointwise large-scale scene completion,0,N/A,N/A,N/A,N/A,Computer Vision,Obtaining dense point cloud representation of a large-scale scene from a sparse point cloud input.
Spatio-temporal Action Recognition,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Video Focal Modulation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A

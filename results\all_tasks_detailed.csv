Category,Task Name,Dataset Count,Common Datasets (Top 10),Benchmarks,SOTA Metrics,Subtasks Count,Has Description
CV,Semantic Segmentation,348,Matterport3D; Lost and Found; Photi-LakeIce; FoodSeg103; Fashionpedia; ATLANTIS; OmniCity; RobotPush; PETRAW; KvasirCapsule-SEG,Matterport3D; UrbanLF; SYN-UDTIRI; dacl10k v1 testfinal; UAVid; FoodSeg103; CEMS-W; ATLANTIS; DeLiVER test; BDD100K val,FPS; Mean IoU (test); Dice; Params (M); Average F1; A-acc; μ; Category iIoU; IoU; Mean Accuracy,29,No
CV,Object Detection,333,Boreal Forest Fire; AppleBBCH81; Visual 3D shape matching dataset; FOD-A; SPair-71k; SSL; A Dataset of Multispectral Potato Plants Images; CornHub; Hyper-Kvasir Dataset; cranfield-synthetic-drone-detection,; GRAZPEDWRI-DX; VisDrone-DET2019; COCO test-dev; SIXray; BDD100K val; PASCAL Part 2010 - Animals; EventPed; VisDrone- 1% labeled data; A Dataset of Multispectral Potato Plants Images,; mAP w/o OOD; mMR; Operations per network pass; APM; AP@0.5; Average IOU; mAP@0.5:0.95; APt; mAP@0.75,39,No
CV,Image Classification,283,Galaxy Zoo DECaLS; Weapon Detection Dataset; Kuzushiji-MNIST; BAM!; OFDIW; AppleScabFDs; ImageNet-Hard; ColonINST-v1 (Unseen); ColonINST-v1; MuMiN-medium,"Kuzushiji-MNIST; CIFAR-100, 40% Symmetric Noise; ImageNet-Hard; KITTI-Dist; Fracture/Normal Shoulder Bone X-ray Images on MURA; ColonINST-v1 (Unseen); ObjectNet (ImageNet classes); finetuned-websites; Flowers (Tensorflow); CUB-200-2011",Operations per network pass; Overall; Test f1; Test F1 score; PARAMS (M); ImageNet Top-5 Accuracy; Total Accuracy; Percentage Average accuracy - 5 tasks; Epochs; Eval F1,33,Yes
CV,Visual Question Answering (VQA),145,VQA-CE; VisCon-1M; TDIUC; TextKVQA; MM-Vet; FigureQA; CLEVR-Humans; Visual Question Answering v2.0; ZS-F-VQA; Conceptual Captions,TextVQA test-standard; VQA-CE; GQA test-std; A-OKVQA; GRIT; VQA-CP; QLEVR; WebSRC; TDIUC; VQA v2 val,Reasoning (Mea.); PC-cpr; Reasoning (Spa.); Binary; Reasoning (Geo.); PC-grp; Exact Match (EM); Abductive; GC-mat; Open,9,Yes
CV,Named Entity Recognition (NER),130,i2b2 De-identification Dataset; BB; COVID-Q; BC7 NLM-Chem; BC5CDR; FindVehicle; PhoNER COVID19; Europeana Newspapers; Dataset of Legal Documents; Polyglot-NER,LINNAEUS; IECSIL FIRE-2018 Shared Task; WetLab; i2b2 De-identification Dataset; SLUE; NCBI-disease; CoNLL 2000; OntoNotes 5.0; BC7 NLM-Chem; UNER v1 - PUD (Swedish),Average F1; label-F1 (%); Micro F1; Multi-Task Supervision; F1-score (strict); F1-score (Weighted); Micro F1 (Exact Span); Avg F1; Entity F1; F1 (surface form),13,Yes
CV,Pose Estimation,124,GazeFollow; H3WB; MPII; HUMAN4D; MoVi; NCLT; IKEA ASM; RealArt-6; VBR; KeypointNet, ITOP front-view; MERL-RAV; COCO test-dev; MPII; 300W (Full); 3DPW; ITOP top-view; MPII Single Person; MS-COCO; DensePose-COCO,"0..5sec; Percentage correct; DUC2-Acc@0.5m,10°; AP Easy; Average End-Point Error; APM; AR50; Acceleration Error; MAE pitch (º); Mean PCK@0.1",17,Yes
CV,Anomaly Detection,120,ISP-AD; UBI-Fights; Lost and Found; DARPA; SensumSODF; SWAT A7; SPOT-10; UCSD Ped2; Fashion-MNIST; Forest CoverType,KSDD2; Numenta Anomaly Benchmark; Anomaly Detection on Unlabeled ImageNet-30 vs CUB-200; Lost and Found; Unlabeled CIFAR-10 vs CIFAR-100; PAD Dataset; One-class CIFAR-10; Fishyscapes L&F; NB15-Backdoor; UBnormal,Segmentation AU-sPRO (until FPR 5%); FPS; Object AUROC; Average F1; Avg. ROC-AUC; ROC AUC; FPR; Network; AUC; AUC-PRO,28,Yes
CV,Action Recognition,115,HowTo100M; MECCANO; FineGym; TUM Kitchen; EPIC-KITCHENS-100; PETRAW; HACS; Kinetics-Sound; THUMOS14; UAV-GESTURE,HAA500; AVA v2.1; MECCANO; RareAct; NTU RGB+D 120; BAR; THUMOS’14; VIRAT Ground 2.0; H2O  (2 Hands and Objects); EPIC-KITCHENS-100,Top-5 Accuracy; No. of Somersaults Accuracy; Accuracy (Cross-Setup); Params (M); mAP@0.5; Accuracy (Cross-View); Top-5; F-measure (%); Object Label; mAP@0.4,15,Yes
CV,Instance Segmentation,111,GraspClutter6D; Fashionpedia; SIXray; TrashCan; UVO; OmniCity; RobotPush; Hypersim; NDD20; OCID,coco minval; NYUDv2-IS; nuScenes; KINS; COCO test-dev; COCO; UAVBillboards; COCO-N Medium; UFBA-425; BDD100K val,mAP@0.5:0.95:0.05; Params (M); mAP@0.5; APM; mAP50; box AP; Dice Coef; MOTA; Dice Scoe; APS,17,Yes
CV,Speech Recognition,96,SPEECH-COCO; Libri-Light; AliMeeting; SMS-WSJ; ClovaCall; OLR 2021; TIMIT; ADIMA; 2000 HUB5 English; RESD,Podlodka.io; Common Voice Chuvash; swb_hub_500 WER fullSWBCH; Common Voice 7.0 Odia; Libri-Light test-other; Common Voice 8.0 Central Kurdish; Common Voice Latvian; projecte-aina/parlament_parla ca; Common Voice 7.0 Portuguese; Common Voice 8.0 German,Test CER; WER (%); SwitchBoard; VoxPopuli (Dev); Hub5'00; Word Error Rate (WER); Test WER; VoxCeleb (Dev); WER; Character Error Rate (CER),11,Yes
CV,Information Retrieval,93,Robust04; i2b2 De-identification Dataset; ArCOV-19; ASNQ; TutorialBank; MuMu; ReQA; PerKey; MQ2008; Persian Reverse Dictionary Dataset,TREC-PM; dim 64; GermanQuAD; Unknown; Evaluate; News Headlines; dim 512; CQADupStack; dim 768; NanoMSMARCO,infNDCG; Recall@200; Recall@100; mAP@100; Time (ms); 10-20% Mask PSNR; 1:1 Accuracy; NDCG; nDCG@10; MRR@10,6,Yes
CV,2D Object Detection,93,SJTU Multispectral Object Detection (SMOD) Dataset; AppleBBCH81; A collection of 131 CT datasets of pieces of modeling clay containing stones; FES; DOLPHINS; Hypersim; FathomNet2023; ChessReD2K; DermSynth3D; TRR360D,; RADIATE; BDD100K val; FishEye8K; UAV-PDD2023; TXL-PBC: a freely accessible labeled peripheral blood cell dataset; DUO; SCoralDet Dataset; RF100; ExDark,; mAP@0.5; test/mAP; mAP@75; AP@0.5; mAP50; AP90(T<90); AP50(T<90); mAP@0.3; AP50,15,No
CV,Image Retrieval,87,CREPE (Compositional REPresentation Evaluation); CIRCO; DeepPatent; CUB-200-2011; DyML-Product; European Flood 2013 Dataset; Flickr30k-CNA; YFCC100M; Localized Narratives; CIRR,CREPE (Compositional REPresentation Evaluation); ICFG-PEDES; DeepPatent; PhotoChat; WIT; MUGE Retrieval; In-Shop; CUB-200-2011; Par106k; LaSCo,"Recall@1 (HN-Atom, UC); A-R@5; Rank-50; (Recall@5+Recall_subset@1)/2; R@1; PNR; QPS; Average-mAP; Recall@1; R@8",10,Yes
CV,2D Semantic Segmentation,83,FES; OC-Cityscape; ACCT Data Repository; SICKLE; DermSynth3D; AUT-VI; [[get~Easy//Service//Charge]]How do I dispute a charge on Expedia?; WorldFloods; CaDIS; RUGD,xBD; Extended heartSeg; RELLIS-3D; WorldFloods; WildScenes; Deep Indices; CamVid; Time Series Prediction Benchmarks; GF-PA66 3D XCT; Cityscapes val,Localization F1-score; Jaccard (Mean); mIoU (Env DA); mIoU (Temporal DA) ; 1:3 Accuracy; Category mIoU; Mean IoU (class); GMac; Classification F1-score; Average IOU,17,No
CV,Relation Extraction,82,HyperRED; FOBIE; GAD; LabPics; KGRED; TimeBankPT; MAVEN-ERE; XFUND; CDR; X-WikiRE,CoNLL04; 2010 i2b2/VA; GAD; ADE Corpus; Google RE; NYT-single; LPSC-hasproperty; JNLPBA; SciERC; LPSC-contains,F1 (Zero-Shot); Average F1; RE+ Micro F1; Macro F1; Micro F1; P@10%; Triplet F1 (strict EL); RE+ Macro F1; RE+ Macro F1 ; F1 (5% Few-Shot),15,Yes
CV,Image Generation,79,ColorSVG-100K; FaceForensics++; CUB-200-2011; SPOT-10; KMNIST; WISE; ARKitScenes; ActivityNet Entities; Fashion-MNIST; Fashion-Gen,LSUN Horse 256 x 256; Landscapes 256 x 256; KMNIST; WISE; ARKitScenes; CIFAR-10 (10% data); Pokemon 256x256; Fashion-MNIST; LSUN Car 512 x 384; FFHQ 64x64,Model Size (MB); FID-50k; sFID; TextScenesHQ FID; Intra-FID; Inception score; clean-FID; KID; TextVsionBlend OCR (F1 Score); FID (SwAV),23,Yes
CV,Image Captioning,79,ChEBI-20; VisCon-1M; XM 3600; CITE; NoCaps; Localized Narratives; Conceptual Captions; IU X-Ray; Winoground; SCapRepo,nocaps in-domain; ChEBI-20; Peir Gross; nocaps-val-near-domain; nocaps-XD out-of-domain; VizWiz 2020 test; nocaps-XD in-domain; nocaps-val-out-domain; Flickr30k Captions test; WHOOPS!,Exact; B3; chair_i; chair_s; BLEU-1; BLEU-2; CIDER; BLEU-1 (Romantic); CLIPScore; RDK FTS,8,Yes
CV,Depth Estimation,77,Matterport3D; HRWSI; HUMAN4D; Hypersim; 3D Ken Burns Dataset; DermSynth3D; ConSLAM; FutureHouse; TransProteus; DIODE,Matterport3D; DIODE; KITTI Eigen split; KITTI 2015; Taskonomy; Cityscapes test; DCM; ScanNet; eBDtheque; ScanNetV2,Average PSNR; MSE ; Absolute relative error (AbsRel); RMSE; Delta < 1.25^3; BadPix(0.01); mean absolute error; RMS; BadPix(0.03); RMSE log,10,Yes
CV,Autonomous Driving,70,A*3D; PRECOG; openDD; IDD; SynthCity; TITAN; CARLA; Lost and Found; PandaSet; HDD,Town05 Short; Town05 Long; CARLA Leaderboard; ApolloCar3D,Infraction penalty; DS; RC; Driving Score; A3DP; Route Completion,6,Yes
CV,Object Tracking,69,UAVDT; Lindenthal Camera Traps; UFPR-ALPR; VOT2017; TLP; Open Radar Datasets; HT1080WT cells - 3D collagen type I matrices; HOMER; PersonPath22; MDOT,QuadTrack; SeaDronesSee; 1; MMPTRACK; BIRDSAI - ICVGIP 2020; FE108; Perception Test; KITTI; VisEvent; COESOT,Precision Rate; Success Rate; Humans; Averaged Precision; mean success; Precision Score; Precision Plot; HOTA; 3DMOTA; mean precision,10,Yes
CV,Face Recognition,67,23 Pairs of Identical Twins Face Image Data; CASIA-WebFace+masks; FAD; CASIA-FASD; UMDFaces; MLFW; RFW; BTS3.1; XQLFW; mEBAL,CASIA-WebFace+masks; MLFW; BTS3.1; Color FERET (Online Open Set); XQLFW; MFR; MFW+ (M-M); AgeDB-30; mebeblurf; CelebA+masks,FNMR [%] @ 10-3 FMR; MFR-ALL; MFR-MASK; TAR @ FAR=0.0001; TAR@FAR=0.0001; Caucasian; South Asian; TAR @ FAR=0.01; TAR @ FAR=1e-5; 5-class test accuracy,6,Yes
CV,3D Object Detection,67,A*3D; PreSIL; PandaSet; NYUv2; Light Snowfall; Mono3DRefer; nuScenes LiDAR only; nuScenes-C; nuScenes; V2XSet,KITTI Cars Hard val; Light Snowfall; View-of-Delft (val); Dense Fog; nuScenes LiDAR only; KITTI Pedestrian; nuScenes; V2XSet; SUN-RGBD val; nuScenes Camera Only,BEV AP@0.3 Urban;  mAP; AP|R40(moderate); mAP@0.5; AP0.7 (Noisy); mod. Pedestrian AP@.25IoU; mATE; NDS (val); BEV AP@0.3 Rain; FrameAccuracy,5,Yes
CV,Link Prediction,67,Citeseer; GO21; IMDb Movie Reviews; ICEWS; TSP/HCP Benchmark set; SINS; Orkut; Arxiv GR-QC; FB1.5M; Yelp2018,MIT; OpenBG500; Cora (biased evaluation); OpenBioLink; ICEWS05-15; KG20C; FB-AUTO; Wiki-Vote; IMDb; Alibaba-S,Mean AP; Hit@1; Macro F1; ROC AUC; Micro F1; ACC; MR; Hit@10; nDCG@10; HR@10,6,Yes
CV,3D Reconstruction,58,HOD; SceneNet; Common Objects in 3D; Indoor and outdoor DFD dataset; ThermoScenes; BlendedMVS; ShapenetRender; Houses3K; Tragic Talkers; MobileBrick,Aria Synthetic Environments; DTU; ShapeNet; Data3D−R2N2; ScanNet; Scan2CAD; Aria Digital Twin Dataset; ApolloCar3D; 300W; 3DPeople,Comp; A3DP; 3DIoU; Precision; Average Accuracy; P2S; F-Score@1%; 1-of-100 Accuracy; IoU; Normal Consistency,7,Yes
CV,Video Understanding,56,HVU; DeepSportRadar-v1; M$^3$-VOS; VidSitu; WildQA; SoccerDB; CinePile: A Long Video Question Answering Dataset and Benchmark; MLB-YouTube Dataset; STAR Benchmark; Query-Focused Video Summarization Dataset,N/A,N/A,7,Yes
CV,Optical Character Recognition (OCR),55,SSIG-SegPlate; UFPR-ALPR; Twitter100k; Copel-AMR; I2L-140K; SciTSR; FICS PCB Image Collection (FPIC); Visiting Card | ID Card Images | Hindi-English; VideoDB's OCR Benchmark Public Collection; ChineseLP,"I2L-140K; VideoDB's OCR Benchmark Public Collection; SUT; Benchmarking Chinese Text Recognition: Datasets, Baselines, and an Empirical Study; FSNS - Test; im2latex-100k",Accuracy (%); BLEU; Average Accuracy; Character Error Rate (CER); Sequence error; Word Error Rate (WER),10,Yes
CV,Abstractive Text Summarization,53,PeerSum; Spotify Podcast; AMR Bank; WikiSum; ConvoSumm; WITS; VNDS; CNN/Daily Mail; FINDSum; WikiHow,WITS; CNN / Daily Mail; CNN/Daily Mail; mlsum-es; WikiHow; Abstractive Text Summarization from Fanpage; SAMSum Corpus: A Human-annotated Dialogue Dataset for Abstractive Summarization; MLSum-it; AESLC; PLOS,rouge1; ROUGE-1; Content F1; ROUGE-2; Test ROGUE-1; BERTScore; Rouge-L; METEOR; # Parameters; Rouge-2,3,Yes
CV,Medical Image Segmentation,52,HC18; BreastDICOM4; Polyp ASH; MICCAI 2015 Head and Neck Challenge; LiTS17; WORD; FetReg; CoCaHis; CVC-ClinicDB; Endotect Polyp Segmentation Challenge Dataset,EM; MICCAI 2015 Head and Neck Challenge; MoNuSeg 2018; Autoimmune Dataset; 2015 MICCAI Polyp Detection; CVC-ClinicDB; Endotect Polyp Segmentation Challenge Dataset; Medical Segmentation Decathlon; KvasirCapsule-SEG; AMOS,FPS; Dice; NSD; Avg DSC; IoU; mIoU (5-folds); VInfo; Average Dice (5-folds); Jaccard Index; S-Measure,31,Yes
CV,Emotion Recognition,52,EmoPars; BanglaEmotion; Werewolf-XL; SEED; SEND; EmoWOZ; Human faces with mixed-race & various emotions; Video2GIF; REN-20k Dataset; BERSt,MaSaC_ERC; MPED; SEED; MAFW; RAVDESS; คลิปคุณพ่อให้ลูกสาวยืมโทรศัพท์และความสนุกสนาน; EMOTIC; MSP-Podcast; Emomusic; FER2013,"F1-score (Weighted); WAR; Concordance correlation coefficient (CCC); EmoA; Accuracy; 1'""; 5-class test accuracy; EmoV; Top-3 Accuracy (%)",12,Yes
CV,3D Human Pose Estimation,50,Parkour-dataset; TotalCapture; HSPACE; Human-Art; RePoGen; Deep Fashion3D; H3WB; HUMAN4D; SportsPose; 3DPW,SkiPose; HSPACE; Geometric Pose Affordance ; 3D Poses in the Wild Challenge; H3WB; 3DPW; AIST++; Panoptic; 3DOH50K; ITOP front-view,Params (M); PA-MPJPE; MPVPE; PVE-Hands; W-PVE; Single-view; 3DPCK; PVE; PA-PVE-All; B-NMJE,9,Yes
CV,Hate Speech Detection,47,KnowledJe; HateXplain; HatefulDiscussions; Implicit Hate; Peer to Peer Hate; SHAJ; FairPrism; ToxiGen; ViTHSD; MLMA Hate Speech,"bajer_danish_misogyny; Automatic Misogynistic Identification; HateXplain; DKhate; Waseem et al., 2018; Ethos MultiLabel; OLID; HateMM; SHAJ; HatEval",TEST F1 (macro); AAA; Precision; F1; F1-score; Hamming Loss; Macro F1; Accuracy; Macro-F1; AUROC,3,Yes
CV,Novel View Synthesis,46,ThermoScenes; BlendedMVS; PhotoShape; Tanks and Temples; Spaces; OmniObject3D; LLFF; iFF; UASOL; SYNTHIA,PhotoShape; Tanks and Temples; LLFF; iFF; Dosovitskiy Chairs; ShapeNet Chair; BLEFF; RealEstate10K; ScanNet++; ACID,LPIPS; Average PSNR; PSNR/SSIM; PSNR; Average PSNR (dB); Focal Error; FID; NLL; PSIM; Size (MB),2,Yes
CV,Object Recognition,45,MECCANO; Freiburg Groceries; Turath-150K; N-ImageNet; TUM-GAID; UW Indoor Scenes (UW-IS) Occluded dataset; Visual 3D shape matching dataset; EV-IMO; HASY; OCID,"DVS128 Gesture; ObjectNet (ImageNet classes); MECCANO; ObjectNet (ImageNet classes, trained on ImageNet); N-Caltech 101; ObjectNet (All classes); CIFAR10-DVS; N-CARS; shape bias",mAP; Top 5 Accuracy; Top 1 Accuracy; Accuracy (% ); shape bias,3,Yes
CV,Semantic Parsing,45,SQA; AMR Bank; WebQuestionsSP; Szeged Corpus; NomBank; Multilingual TOP; Occluded REID; SEDE; SpCQL; SPLASH,"SQA; Geo; DRG (german, MRP 2020); spider; WebQuestionsSP; PTG (czech, MRP 2020); DRG (english, MRP 2020); SParC; ATIS; UCCA (german, MRP 2020)",EM; Denotation accuracy (test); Exact; F1; F1 Score; Exact Match; Accuracy; Denotation Accuracy; Accuracy (Dev); Test Accuracy,7,Yes
CV,Segmentation,45,BRISC; Boreal Forest Fire; MMFlood; NYUDv2-IS; UIIS10K; PALMS; DeepCrack; Aachen-Heerlen Annotated Steel Microstructure Dataset; CrackVision12K; 25kTrees,SimGas; !(()&&!|*|*|; MFSD; MMFlood; SA-1B,AR-small; Precision; 10%; Average Precision; F1 Score; IoU; AR-large; AR-medium; F1 score; Recall,1,No
CV,Image Clustering,44,Cards; PCam; CUB-200-2011; HAR; Letter; EuroSAT; SPOT-10; UCF101; Oxford-IIIT Pets; Extended Yale B,MNIST-full; PCam; CUB-200-2011; HAR; EuroSAT; UCF101; imagenet-1k; ARL Polarimetric Thermal Face Dataset; Tiny-ImageNet; Oxford-IIIT Pets,ACCURACY; NMI; 	 ACCURACY; ARI; Backbone; Train set; Accuracy; Train Split; Image Size; Train Set,4,Yes
CV,Visual Reasoning,44,Cops-Ref; IconQA; InfiMM-Eval; Bongard-OpenWorld; PHYRE; COG; MaRVL; FigureQA; VSR; SMART-101,VSR; NLVR2 Test; NLVR; WinoGAViL; Winoground; PHYRE-1B-Within; NLVR2 Dev; CLEVRER; IRFL: Image Recognition of Figurative Language; Bongard-OpenWorld,AUCCESS; Predictive-per ques.; Explanatory-per opt.; 1-of-100 Accuracy; accuracy; Jaccard Index; Image Score; Accuracy (Test-U); Counterfactual-per ques.; Text Score,1,Yes
CV,Image Super-Resolution,43,PIRM; Urban100; TESTIMAGES; MSU SR-QA Dataset; IXI; xView; BSD100; CASIA-WebFace; ShipSpotting; StereoMSI,Set5 - 2x upscaling; FFHQ 256 x 256 - 4x upscaling; Set14 - 3x upscaling; BSD100 - 16x upscaling; BSD100 - 4x upscaling; BSD100 - 2x upscaling; Sun80 - 4x upscaling; Manga109 - 8x upscaling; VggFace2 - 8x upscaling; Set5 - 8x upscaling,SSIM 4x T2w; MS-SSIM; Perceptual Index; PSNR 4x T2w; LPIPS; Frechet Inception Distance; MORAN Overall Accuracy; MOS; FED; FLOPs(G),5,Yes
CV,Image Segmentation,43,MARIDA; MSD (Mirror Segmentation Dataset); WildScenes; UIIS10K; PASCAL VOC; MineralImage5k; CEMS-W; 2DeteCT; Linear Equation Image Dataset; CaBuAr,MSD (Mirror Segmentation Dataset); EVD4UAV; Pascal Panoptic Parts; PMD; MAS3K; PASCAL VOC; RMAS; ImageNet; OxfordPets; COCO val2017,Detection: Full (mAP@0.5); F1@M; Dice; E-measure; F1; mIoUPartS; IoU; GFLOPs; mask AP; MAE,1,Yes
CV,Video Question Answering,43,MovieQA; How2QA; MSRVTT-MC; TVQA+; TVQA; HowTo100M; SUTD-TrafficQA; EgoTaskQA; MVBench; VALUE,How2QA; MSRVTT-MC; TVQA; SUTD-TrafficQA; NExT-QA (Efficient); MVBench; WildQA; MSR-VTT; STAR Benchmark; AGQA 2.0 balanced,ROUGE-1; CW; ROUGE-2; 1:1 Accuracy; 	 ACCURACY; Average Accuracy; 1/4; CH; 1/2; AVG,2,No
CV,Multi-Object Tracking,43,UAVDT; BEE23; PersonPath22; 2024 AI City Challenge; HiEve; JRDB; MMPTRACK; BDD100K; GMOT-40; TAO-Amodal,UAVDT; PersonPath22; 2024 AI City Challenge; VisDrone2019; HiEve; JRDB; 2DMOT15; BDD100K; MOT16; Synthehicle,e2e-MOT; AssA; LocA; MOTA; ClsA; TETA; Speed (FPS); IDF1; Track mAP; MOTP,6,Yes
CV,Scene Understanding,43,ADE20K; RADIATE; SceneNet; PSI-AVA; Indoor and outdoor DFD dataset; UAVid; SynPick; Apron Dataset; COQE; CDS2K,ADE20K val; Semantic Scene Understanding Challenge (passive actuation & ground-truth localisation); Semantic Scene Understanding Challenge (active actuation & ground-truth localisation),OMQ; avg_fp_quality; avg_pairwise; Mean IoU; avg_spatial; avg_label,6,Yes
CV,Temporal Action Localization,42,RISE; FineGym; MCAD; Metaphorics; TUM Kitchen; HVU; HiEve; WEAR; Composable activities dataset; EPIC-KITCHENS-100,Ego4D MQ val; THUMOS'14; FineAction; MEXaction2; THUMOS14; MUSES; ActivityNet-1.2; MultiTHUMOS; THUMOS’14; CrossTask,Avg mAP (0.3:0.7); mAP@0.5; mAP IOU@0.1; Avg mAP (0.1-0.5); Average-mAP; mAP@0.4; mAP IOU@0.2; mAP IOU@0.5; mAP@0.3; mAP IOU@0.95,8,Yes
CV,Fine-Grained Image Classification,41,Herbarium 2021 Half–Earth; Kuzushiji-MNIST; CropAndWeed; Aircraft Context Dataset; CUB-200-2011; Tsinghua Dogs; CiNAT-Birds-2021; SPOT-10; FGSCM-52; IP102,Herbarium 2021 Half–Earth; Kuzushiji-MNIST; CUB-200-2011; Herbarium 2022; Con-Text; SOP; Oxford-IIIT Pets; Bird-225; STL-10; EMNIST-Digits,Accuracy (%); Test F1 score (private); mAP; PARAMS; Top 1 Accuracy; Accuracy; Average Per-Class Accuracy; Top-1; Test F1 score; FLOPS,1,Yes
CV,3D Semantic Segmentation,41,ScanNet200; WildScenes; nuScenes-C; nuScenes; SemanticKITTI-C; DALES; 3D Platelet EM; STPLS3D; Hypersim; AMOS,ScanNet200; WildScenes; RELLIS-3D Dataset; nuScenes; DALES; 3D Platelet EM; STPLS3D; Hypersim; ScanNet++; SensatUrban,Mean IoU (test); mIoU (Env DA); Mean IoU (class); test mIoU; mAcc; mIoU (6-Fold); mIoU (test); Mean IoU; mIoU; OA,5,Yes
CV,Face Detection,39,ADE20K; 23 Pairs of Identical Twins Face Image Data; MS-EVS Dataset; FDDB-360; PASCAL Face; DARK FACE; FAD; UMDFaces; AnimalWeb; Human-Parts,ADE20K; WIDER Face (Hard); WIDER Face (Medium); PASCAL Face; Manga109; DCM; COCO-WholeBody; Annotated Faces in the Wild; FDDB; WIDER FACE,AP75; Average Top-1 Accuracy; APL; Average Precision; GFLOPs; AP50; Accuracy; APM; mIoU; AP,1,Yes
CV,Retrieval,38,ALCE; DAPFAM; LLeQA; Spiced; Natural Questions; InfoSeek; Luna-1; RoMQA; DTGB; ToolLens,OK-VQA; Quora Question Pairs; PubMedQA corpus with metadata; HotpotQA; คลิปไวรัล!! ไอซ์ ปรีชญา ลืมปิดไลฟ์สดตอนอาบน้ำ ถูกแชร์กระหึ่มเน็ต; PubMedQA; Natural Questions; InfoSeek; ToolLens; Polyvore,text-to-video Mean Rank; Recall@5; COMP@; Queries per second; 0L; Accuracy (Top-1),3,Yes
CV,Video Captioning,38,How2QA; HowTo100M; 2024 AI City Challenge; Shot2Story20K; VALUE; MSR-VTT; V2C; VATEX; MSVD-Indonesian; How2R,Hindi MSR-VTT; ChinaOpen-1k; VATEX; MSVD-CTN; TVC; VidChapters-7M; Shot2Story20K; MSVD-Indonesian; ActivityNet Captions; MSRVTT-CTN,BLEU4; SPICE; BLEU-3; METEOR; GS; CIDEr; ROUGE; ROUGE-L; BLEU-4,6,Yes
CV,Panoptic Segmentation,37,ADE20K; NYUv2; CropAndWeed; SB20; MUSES: MUlti-SEnsor Semantic perception dataset; LaRS; BDD100K; DALES; Cityscapes; Mapillary Vistas Dataset,ADE20K; Indian Driving Dataset; COCO panoptic; COCO test-dev; LaRS; MUSES: MUlti-SEnsor Semantic perception dataset; ScanNetV2; DALES; KITTI Panoptic Segmentation; NYU Depth v2,PQ (test); Params (M); PQ_th; SQst; RQ; PQ (with stuff); SQth; PQ; PQst; mIoU (test),2,Yes
CV,Text Retrieval,37,CompMix-IR; Image-Chat; TripClick; LLeQA; CURE; TREC-COVID; DAPFAM; Natural Questions; COVID-19 Twitter Chatter Dataset; SciFact,N/A,N/A,0,No
NLP,Question Answering,416,NExT-QA (Open-ended VideoQA); CNN/Daily Mail; Molweni; PersianQA; OpenBookQA; Researchy Questions; ReviewQA; SchizzoSQUAD; PhysiCo; WikiHop,NExT-QA (Open-ended VideoQA); DROP Test; MuLD (NarrativeQA); MedQA; SIQA; MRQA; CODAH; Molweni; OpenBookQA; SchizzoSQUAD,Operations per network pass; 1-of-100 Accuracy; BLEU-1; Conditional (w/ conditions); Overall; Exact Match (EM); Mean Error Rate; Exact F1; HEQQ; Accuracy (trained on 1k),19,Yes
NLP,Classification,222,Urban Hyperspectral Image; Satellite; Open Radar Datasets; GRAZPEDWRI-DX; LLeQA; UK Biobank Brain MRI; GLUE; Two Coiling Spirals; Tasksource; Mudestreda,RTE; Radar Dataset (DIAT-μRadHAR: Radar micro-Doppler Signature dataset for Human Suspicious Activity Recognition); MixedWM38; N-ImageNet; Chest X-Ray Images (Pneumonia); MHIST; SGD; HOWS long; Coordinated Reply Attacks in Influence Operations: Characterization and Detection; CWRU Bearing Dataset,"Params (M); Robustness Score; macro f1 score (A(100), B(100), C(100) Avg.); MCC; 1-of-100 Accuracy; Macro F1; Detection AUROC (severity 5); Detection AUROC (severity 0); Overall accuracy after last sequence; 10 fold Cross validation",24,Yes
NLP,Text Classification,167,HateXplain; MetaHate; Avicenna: Deductive Commonsense Reasoning; MNAD; Overruling; LLeQA; MuMiN-medium; Twitter Sentiment Analysis; GLUE; Persian NLP Hub,HateXplain; Overruling; Twitter Sentiment Analysis; GLUE; reuters21578; NICE-45; RCV1; OneStopEnglish (Readability Assessment); 20 Newsgroups; IMDb,Average F1; Macro F1; Micro F1; weighted-F1 score; F1 Macro; STOPS-2; AUC; nDCG@3; macro F1; Weighted F1,16,Yes
NLP,Language Modelling,166,WMT 2018 News; SLNET; CLOTH; LRA; SIQA; RWC; Glot500-c; PolyNews; Libri-Light; SMC Text Corpus,Text8 dev; PhilPapers; 100 sleep nights of 8 caregivers; FewCLUE (BUSTM); BIG-bench-lite; CLUE (WSC1.1); FewCLUE (OCNLI-FC); OpenWebText; CLUE (DRCD); OpenWebtext2,PPL; 0..5sec; Bits per byte; parameters; Number of params; BPB; Gender Consistency; 10%; Sentiment Alignment; Room Consistency,6,Yes
NLP,Text Generation,162,Avicenna: Deductive Commonsense Reasoning; CNN/Daily Mail; needadvice; Reglamento_Aeronautico_Colombiano_2024; AAVE/SAE Paired Dataset; PolyNews; GLUE; ReDial; TaoDescribe; UHGEvalDataset,CNN/Daily Mail; LDC2016E25; CrimeStats; ReDial; Winogrande TR; Unruly; OpenBookQA; ARC Challenge (25-Shot); Open-Mindedness (0-shot); OpenWebText,BLEU-5; NLLgen; BLEU-1; JS-4; BLEU-2; Distinct-4; NLL; eval_loss; CIDEr; BLEU-4,25,Yes
NLP,Sentiment Analysis,105,DSC (10 tasks); MultiSenti; ReDial; L3CubeMahaSent; ReviewQA; ASTD; SentNoB; Twitter US Airline Sentiment; NoReC; SentimentArcs: Sentiment Reference Corpus for Novels,ChnSentiCorp; IMDb Movie Reviews; FiQA; SLUE; IITP Movie Reviews Sentiment; 122 People - Passenger Behavior Recognition Data; TweetEval; RuSentiment; BanglaBook; Amazon Polarity,Yelp 2014 (Acc); Kitchen; Average F1; Training Time; Recall (%)	; Macro F1; IMDB (Acc); F1 Macro; 10 fold Cross validation; Average,12,Yes
NLP,Text Summarization,98,CNN/Daily Mail; AUTH at BioLaySumm 2024: Bringing Scientific Content to Kids; UKIL-DB-EN; Multi-News; PerKey; Proto Summ; BrWac2Wiki; pn-summary; TQBA++; BookSum,BBC XSum; WikiHow; GigaWord; MeQSum; DUC 2004 Task 1; OrangeSum; GigaWord-10k; CL-SciSumm; LCSTS; ACI-Bench,Meteor; Rouge-2; Spearman Correlation; ROUGE-1; BLEU; ROUGE-3; BertScoreF1; BertScore; Avg. Test Rouge1; Rouge-1,12,Yes
NLP,Domain Adaptation,96,DAPlankton; Stanceosaurus; Five-Billion-Pixels; VisDA-2017; bladderbatch; VIDIT; KdConv; Adaptiope; Nikon RAW Low Light; Modern Office-31,HMDBfull-to-UCF; LeukemiaAttri; GTA-to-FoggyCityscapes; Sim10k; Noisy-SYND-to-MNIST; GTAV+Synscapes to Cityscapes; MoLane; GTAV to Cityscapes+Mapillary; UCF-to-Olympic; SYNTHIA-to-FoggyCityscapes,Accuracy (%);  mAP; mAP; mPQ; Average Accuracy; PSNR; Classification Accuracy; Lane Accuracy (LA); Extra Manual Annotation; Accuracy,13,Yes
NLP,Reading Comprehension,95,WikiReading Recycled; CLOTH; NEREL-BIO; MRQA; Molweni; NomBank; PersianQA; PolicyQA; QuAC; CMRC,ReCAM; RACE; MuSeRC; RadQA; AdversarialQA; ReClor; CrowdSource QA,Accuracy (High); Accuracy (Middle); D(RoBERTa): F1; Average F1; EM ; D(BERT): F1; MSE; Accuracy; Answer F1; Test,20,Yes
NLP,Machine Translation,83,WMT 2018 News; Shifts; WMT 2018; MLQE-PE; BWB; MLQA; ContraCAT; Bilingual Corpus of Arabic-English Parallel Tweets; IWSLT2015; Hindi Visual Genome,Business Scene Dialogue EN-JA; IWSLT2015 English-Vietnamese; IWSLT2015 German-English; WMT 2018 English-Finnish; IWSLT2017 French-English; WMT 2022 English-Russian; WMT 2022 Czech-English; WMT2017 Finnish-English; WMT 2017 Latvian-English; WMT 2018 English-Estonian,ChrF++; Operations per network pass; 1-of-100 Accuracy; Hardware Burden; Number of Params; BLEU (Scn-En); Median Relative Edit Distance; SacreBLEU; BLEU (It-Scn); BLEURT,10,Yes
NLP,Natural Language Inference,81,GLUE; Tasksource; ContractNLI; NLI4Wills Corpus; WinoGrande; PAWS; SelQA; HeadQA; Story Commonsense; Mindgames,AX; RTE; MultiNLI; ANLI test; fever-nli; MedNLI; MNLI + SNLI + ANLI + FEVER; QNLI; GLUE; MultiNLI Dev,Params (M); Average F1; Macro F1; % Test Accuracy; % Dev Accuracy; A3; Dev Matched; Matched; A2; BLEU,3,Yes
NLP,Node Classification,76,Citeseer; Wisconsin (48%/32%/20% fixed splits); tolokers; MUTAG; MuMiN-medium; PASCAL VOC; Penn94; MuMiN-large; HeriGraph; USA Air-Traffic,MuMiN-medium; pokec; Coauthor CS; DBLP: 20 nodes per class; London; Telegram (Directed Graph label rate 60%); Wiki-Vote; Amazon Photo; Wisconsin; ogbn-products: 20 nodes per class,Macro F1; Micro F1; runtime (s); Training Split; Macro-F1@2%; Top-1 accuracy; Inference Time (ms); AUC; Average Top-1 Accuracy; macro F1,5,Yes
NLP,Natural Language Understanding,72,Belebele; NLU Evaluation Corpora; MeDAL; WikiCREM; EPIE; IndirectRequests; ChatLog; Perspectrum; GLUE; SGD,DialoGLUE full; LexGLUE; GLUE; STREUSLE; PDP60; DialoGLUE fewshot,CLINC150 (Acc); UNFAIR-ToS; Tags (Full) Acc; Full F1 (Preps); LEDGAR; ECtHR Task B; Average; ECtHR Task A; CaseHOLD; Role F1 (Preps),2,Yes
NLP,Code Generation,70,DSEval-Exercise; DSEval-Kaggle; Flat Real World Simulink Models; HumanEval-ET; TACO-BAAI; WebApp1K-React; BioCoder; BigCodeBench; TFix's Code Patches Data; CoNaLa-Ext,BigCodeBench-Instruct; HumanEval-ET; Multi-Source Python Code Corpus; TACO-BAAI; WebApp1K-React; CoNaLa-Ext; PECC; Android Repos; Django; MBPP-ET,Competition Pass@5; Test Set pass@5; Interview Pass@any; CorrSc; Java/BLEU; Competition Pass@any; Java/CodeBLEU; Introductory Pass@any; BLEU Score; Introductory Pass@1,5,Yes
NLP,Common Sense Reasoning,56,CREAK; RWSD; Housekeep; ACCORD CSQA 0-5; StepGame; ReCoRD; X-CSQA; CODAH; PARus; QuRe,RWSD; ReCoRD; CODAH; PARus; Russian Event2Mind; Visual Dialog v0.9; Event2Mind test; WinoGrande; ARC (Challenge); BIG-bench (Sports Understanding),EM; Recall@10; EM ; BLEU; Jaccard Index; Dev; Average F1; F1; MSE; 1 in 10 R@5,17,Yes
NLP,Recommendation Systems,56,Spotify Podcast; Tripadvisor Restaurant Reviews; Gowalla; WyzeRule; Yelp2018; Wikidata-14M; CAL10K; Amazon Review; SweetRS; ReDial,Douban Monti; Gowalla; Amazon Games; MovieLens 10M; Yelp2018; Amazon-Movies; Amazon Books; ReDial; Flixster; Amazon Men,nDCG@10 (100 Neg. Samples); Recall@50; RMSE; HR@10 (full corpus); MAP@20; MAP@30; Hits@20; P@10; MAE; Hit@10,11,Yes
NLP,Graph Classification,54,Citeseer; BA-2motifs; MUTAG; IMDB-BINARY; CSL; UK Biobank Brain MRI; SIDER; OASIS; Mutagenicity; SPOT-10,Citeseer; IPC-grounded; NC1; HIV-fMRI-77; IMDB-BINARY; MUTAG; MalNet-Tiny; MSRC-21 (per-class); UK Biobank Brain MRI; CSL,Accuracy (%); Accuracy(10-fold); Rand index; Accuracy (10-fold); AP; ROC-AUC; F1; Accuracy; Mean Accuracy; Test Accuracy,3,Yes
NLP,Word Embeddings,53,WinoBias; United Nations Parallel Corpus; Interpretable STS; DICE: a Dataset of Italian Crime Event news; McQueen; RiskData; Twitter Death Hoaxes; SEND; pioNER; MUSE,N/A,N/A,4,Yes
NLP,Knowledge Graphs,46,DBP15K; CompMix-IR; ForecastQA; FB1.5M; AISECKG; Event-QA; ReDial; ORKG-QA; Kinship; ENT-DESC,MARS (Multimodal Analogical Reasoning dataSet);  FB15k; JerichoWorld; WikiKG90M-LSC,Validation MRR; MRR; Set accuracy; Test MRR,5,Yes
NLP,Few-Shot Learning,45,CareCall; MedNLI; GLUE; CUB-200-2011; EuroSAT; MR; F-SIOL-310; FLEURS; SST-2; Bongard-OpenWorld,Mini-ImageNet - 20-Shot Learning; Mini-Imagenet 5-way (1-shot); MedNLI; OxfordPets; EuroSAT; MR; food101; UCF101; Mini-ImageNet - 1-Shot Learning; CaseHOLD,Macro Recall; 16-shot Accuracy; Micro Precision; Specificity; 12-shot Accuracy; F1-score; 5 way 1~2 shot; Macro F1; 4-shot Accuracy; AUC-ROC,12,Yes
NLP,Coreference Resolution,45,WinoBias; Quizbowl; MASC; WikiCREM; OntoNotes 5.0; MultiReQA; LitBank; A Game Of Sorts; CoNLL; WSC,STM-coref; Quizbowl; WikiCoref; GAP; CoNLL12; XWinograd EN; DocRED-IE; The ARRAU Corpus; CoNLL 2012; Winograd Schema Challenge,Masculine F1 (M); MUC; Bias (F/M); Average F1; CoNLL F1; Avg. F1; F1; Accuracy; Overall F1; Avg F1,2,Yes
NLP,Audio Classification,43,Multimodal PISA; MediBeng; DiCOVA; Mudestreda; EPIC-KITCHENS-100; ReefSet; YouTube-100M; UrbanSound8K; DEEP-VOICE: DeepFake Voice Recognition; MeerKAT: Meerkat Kalahari Audio Transcripts,Multimodal PISA; DiCOVA; EPIC-KITCHENS-100; MeerKAT: Meerkat Kalahari Audio Transcripts; DEEP-VOICE: DeepFake Voice Recognition; EPIC-SOUNDS; ICBHI Respiratory Sound Database; GTZAN; Common Voice 16.1; MNIST,Test mAP; Accuracy (10-fold); Percentage correct; Mean AP; d-prime; Top-1 Noun; AUC; Top 1 Accuracy; MosquitoSound; ICBHI Score,4,Yes
NLP,Zero-Shot Learning,43,How2QA; TVQA+; TVQA; COCO-MLT; AwA2; CUB-200-2011; VOC-MLT; EuroSAT; UCF101; ImageNet_CN,How2QA; TVQA; COCO-MLT; AwA2; CUB-200-2011; VOC-MLT; EuroSAT; ImageNet_CN; UCF101; Oxford-IIIT Pets,k=10 mIOU; A-acc; Top 1 Accuracy; Average mAP; average top-1 classification accuracy; Pearson correlation coefficient (PCC); Accuracy Unseen; Accuracy; Accuracy Seen; H,7,Yes
NLP,Machine Reading Comprehension,43,Belebele; CMRC 2018; DREAM; MC-AFP; WikiReading Recycled; NEREL-BIO; MRQA; ViMMRC; Molweni; Who-did-What,N/A,N/A,0,No
NLP,Unsupervised Domain Adaptation,34,nuScenes (Cross-City UDA); ImageNet-R; Market-1501; Sim10k; Oxford RobotCar Dataset; EPIC-KITCHENS-100; PACS; Five-Billion-Pixels; Sims4Action; MegaAge,Duke to Market; PreSIL to KITTI; ImageNet-R; GTAV-to-Cityscapes Labels; UCF-HMDB; VehicleID to VERI-Wild Medium; EPIC-KITCHENS-100; Veri-776 to VehicleID Medium; PACS; Duke to MSMT, mAP; mAP@0.5; R5; R-1; MIoU (16 classes); rank-5; AP@0.5; Market-1501->mAP; R10; mIoU (19 classes),1,Yes
NLP,Dialogue Generation,34,CareCall; Arabic-ToD; JDDC 2.0; LLMafia; CPsyCounE; diaforge-utc-r-0725; OpenDialKG; UDC; OpenViDial 2.0; SODA,Ubuntu Dialogue (Entity); Twitter Dialogue (Noun); FusedChat; Persona-Chat; CMU-DoG; Ubuntu Dialogue (Cmd); PG-19; Ubuntu Dialogue (Tense); Reddit (multi-ref); Amazon-5,PPL; Meteor; BLEU-1; Slot Accuracy; relevance (human); Joint SA; CIDr; Avg F1; Sensibleness; mauve,1,Yes
NLP,Music Generation,33,abc_cc; JS Fake Chorales; POP909; MusicCaps; MidiCaps; YM2413-MDB; EMOPIA; MuseData; Guitar-TECHS; NES-VMDB,Song Describer Dataset,FAD VGG,3,Yes
NLP,Domain Generalization,31,ImageNet-R; NICO++; All-day CityScapes; PACS; Sims4Action; Cityscapes; ImageNet-Sketch; NICO; Stylized ImageNet; Wild-Time,"ImageNet-R; Stylized-ImageNet; NICO Vehicle; PACS; ImageNet-Sketch; ImageNet-A; NICO Animal; GTA-to-Avg(Cityscapes,BDD,Mapillary); VLCS; VizWiz-Classification",Accuracy - Corrupted Images; Average Accuracy; Top 1 Accuracy; Top-1 Error Rate; mean Corruption Error (mCE); Accuracy; Accuracy - All Images; Number of params; Mean IoU; Top-1 accuracy %,3,Yes
NLP,Multi-Label Classification,30,MIMIC-CXR; EXTREME CLASSIFICATION; CheXpert; OpenImages-v6; NLP Taxonomy Classification Data; MRNet; ECHR; PASCAL VOC 2007; CAVES; BanglaLekha-Isolated,ChestX-ray14; MRNet; MIMIC-CXR; PASCAL VOC 2007; MLRSNet; MS-COCO; PASCAL VOC 2012; CheXpert; OpenImages-v6; NUS-WIDE,AUC on Meniscus Tear (MEN); mAP; AUC on Abnormality (ABN); Accuracy on ACL Tear (ACL); F1-score; Average Accuracy; Average AUC; AUC on ACL Tear (ACL); AVERAGE AUC ON 14 LABEL; Macro F1,4,Yes
NLP,Slot Filling,28,Dialogue State Tracking Challenge; IndirectRequests; NoMusic; SGD; pioNER; xSID; MultiWOZ; diaforge-utc-r-0725; RiSAWOZ; CareerCoach 2022,Dialogue State Tracking Challenge; CAIS; KILT: Zero Shot RE; MASSIVE; MULTIWOZ 2.2; KILT: T-REx; MixATIS; MixSNIPS; ATIS; ATIS (vi),FITB; Recall@5; Slot F1 Score; KILT-AC; F1; KILT-F1; Slot F1; R-Prec; Micro F1; F1 (5-shot) avg,2,Yes
NLP,Document Summarization,28,WikiSum; FINDSum; CNN/Daily Mail; HowSumm; Multi-XScience; NEWSROOM; TalkSumm; AESLC; OpoSum; Multi-News,BBC XSum; CNN / Daily Mail; WikiLingua (tr->en); Arxiv HEP-TH citation graph; arXiv Summarization Dataset; HowSumm-Method; HowSumm-Step,PPL; ROUGE-1; ROUGE-2; Rouge-L; Rouge-2; ROUGE-L,1,Yes
NLP,NER,27,IECSIL FIRE-2018 Shared Task; InLegalNER; AISECKG; SIGARRA News Corpus; TLUnified-NER; FiNER-139; SourceData-NLP; BC4CHEMD; MSRA CN NER; Noise-SF,N/A,N/A,0,No
NLP,Part-Of-Speech Tagging,26,CoNLL 2017 Shared Task - Automatically Annotated Raw Texts and Word Embeddings; MASC; Twitter PoS VCB; Szeged Corpus; Egyptian Arabic Segmentation Dataset; Ritter PoS; Morphosyntactic-analysis-dataset; CoNLL; Finer; LinCE,ANTILLES; UD2.5 test; Sequoia Treebank; Spoken Corpus; Penn Treebank; ARK; Morphosyntactic-analysis-dataset; DaNE; Tweebank; ParTUT,Accuracy (%); Avg. F1; BLEX; Avg accuracy; UPOS; Accuracy; Acc; Weighted Average F1-score; Macro-averaged F1,1,Yes
NLP,Open-Domain Question Answering,26,SQuAD; ARCD; WikiQAar; Natural Questions; InfoSeek; WikiMovies; LAMA; DuReader; ELI5; ScienceQA,KILT: ELI5; ELI5; KILT: TriviaQA; SQuAD1.1 dev; Quasar; Natural Questions (short); TQA; Natural Questions; SearchQA; WebQuestions,EM; Recall@5; F1; KILT-EM; KILT-F1; Exact Match; R-Prec; Rouge-L; N-gram F1; EM (Quasar-T),0,Yes
NLP,Mathematical Reasoning,26,Lila; PGPS9K; DART-Math-Uniform; TheoremQA; IconQA; ConstructiveBench; MathMC; MATH-V; PrOntoQA; Conic10K,Lila (IID); PGPS9K; MATH500; FrontierMath; AMC23; Lila (OOD); GSM8K; UniGeo; GeoQA; UniGeo (PRV),Accuracy (%); Accuracy; Completion accuracy; Acc,7,No
NLP,Question Generation,25,SQuAD; 30MQA; Natural Questions; MMD; HybridQA; InfoLossQA; OK-VQA; diaforge-utc-r-0725; FreebaseQA; TextBox 2.0,GrailQA-IID; COCO Visual Question Answering (VQA) real images 1.0 open ended; GrailQA-Compositional; SQuAD; GrailQA-Zero-Shot; Natural Questions; FairytaleQA; Visual Question Generation; WeiboPolls; SQuAD1.1,BLEU-3; ROUGE-1; BLEU; R-QAE; QAE; BLEU-1; METEOR; bleu; FactSpotter; ROUGE-L,1,Yes
NLP,Data-to-Text Generation,24,Wikipedia Person and Animal Dataset; XAlign; MLB Dataset; MultiWOZ; E2E; WikiOFGraph; TempWikiBio; KELM; Chart2Text; WikiTableT,WebNLG ru; SR11Deep; Wikipedia Person and Animal Dataset; XAlign; Czech Restaurant NLG; MLB Dataset (Content Ordering); MLB Dataset; E2E; WikiOFGraph; MLB Dataset (Content Selection),PARENT; Bleu; METEOR (Validation set); CIDER; count; DLD; CIDEr; BLEU-4; BLEU; Number of parameters (M),3,Yes
NLP,Relation Classification,23,MultiTACRED; Translated TACRED; LabPics; MATRES; RELX; FewRel; FREDo; DRI Corpus; SupplyGraph; CrossRE,SemEval 2010 Task 8; Discovery; DRI Corpus; TACRED; CDCP; MATRES; AbstRCT - Neoplasm; FewRel,F1; F1 (10-way 1-shot); Macro F1; F1 (10-way 5-shot); F1 (5-way 5-shot; F1 (5-way 1-shot); 1:1 Accuracy,3,Yes
NLP,Math Word Problem Solving,23,PGPS9K; DART-Math-Uniform; IconQA; GSM-Plus; MathQA; MathMC; MATH; Math23K; MGSM; MMOS,ALG514; MATH minival; MATH; ParaMAWPS; Math23K; SVAMP (1:N); SVAMP; DRAW-1K; MAWPS; PEN,Accuracy (%); Answer Accuracy; weakly-supervised; Execution Accuracy; Parameters (Billions); Accuracy; Accuracy (training-test); 1:1 Accuracy; Accuracy (5-fold),0,Yes
NLP,Task-Oriented Dialogue Systems,22,SGD; SSD_PLATE; BeNYfits; A Game Of Sorts; MultiWOZ; LLMafia; SSD; diaforge-utc-r-0725; SSD_ID; SSD_PHONE,Kvret; KVRET; MULTIWOZ 2.0; SGD,BLEU; METEOR; Score; Entity F1; BLEU-4,1,Yes
NLP,Text-To-SQL,21,TriageSQL; SEDE; HybridQA; SPLASH; Car_bi; KITTI; Spider-Realistic; SParC; ViText2SQL; BIRD (BIg Bench for LaRge-scale Database Grounded Text-to-SQL Evaluation),Text-To-SQL; spider; SQL-Eval; KaggleDBQA; SParC; 2D KITTI Cars Easy; Spider 2.0; SEDE; SPIDER; BIRD (BIg Bench for LaRge-scale Database Grounded Text-to-SQL Evaluation),0..5sec; Execution Accuracy % (Dev); Exact Match (EM); PCM-F1 (dev); Success Rate; Exact Match Accuracy (in Dev); Execution Accurarcy (Human); Execution Accuracy; question match accuracy; PCM-F1 (test),1,Yes
NLP,Text Simplification,20,Medical Wiki Paralell Corpus for Medical Text Simplification; DEplain-web-sent; OneStopEnglish; InfoLossQA; TextBox 2.0; SimpEvalASSET; DEplain-web-doc; WikiLarge; EurekaAlert; TurkCorpus,DEplain-web-doc; EurekaAlert; Wiki-Auto + Turk; WikiLargeFR; TurkCorpus; ASSET; PWKP / WikiSmall; DEplain-web-sent; DEplain-APA-doc; DEplain-APA-sent,"SARI; Rouge1; BLEU; SARI (EASSE>=0.2.1); ROUGE-2; FKGL; FRE (Flesch Reading Ease); METEOR; BertScore (Precision); QuestEval (Reference-less, BERTScore)",0,Yes
NLP,Style Transfer,20,DukeMTMC-reID; WikiArt; POP909; TextSeg; GYAFC; StyleGallery; iKala; Chinese Traditional Painting dataset; Touchdown Dataset; GTSinger,WikiArt; StyleBench; 01/01/1967' AND 2*3*8=6*8 AND 'AncJ'='AncJ; GYAFC; ^(#$!@#$)(()))******,0..5sec; Accuracy; ArtFID; SSIM; Harmonic mean; CLIP Score; BLEU-4,6,Yes
NLP,Language Identification,20,udhr-lid; VoxForge; CONAN; English-Pashto Language Dataset (EPLD); NLI-PT; TuGebic; Nordic Language Identification; OGTD; Dakshina; L3Cube-MahaCorpus,Nordic Language Identification; VoxForge; OpenSubtitles; GlotLID-C; VOXLINGUA107; Universal Dependencies,Accuracy; Macro F1; Error rate,2,Yes
NLP,Time Series Classification,19,Consumer Spendings; EigenWorms; ECG5000; Yeast colony morphologies; SHAPES; PhysioNet Challenge 2012; BorealTC; ECG200; MJFF Levodopa Response Study; ATMs fault prediction,DigitShapes; ACSF1; Heartbeat; AATLD Gesture Recognition; LP5; ERing; ArabicDigits; UEA; EigenWorms; KickvsPunch,0..5sec; Absolute Time (ms); ACC; % Test Accuracy; AUC; NLL; AUC Stdev; mIoU; Accuracy(30-fold); F1 (Hidden Test Set),2,Yes
NLP,Transfer Learning,18,M2QA; Digital twin-supported deep learning for fault diagnosis; MFRC; AppleScabFDs; ReefSet; BaitBuster-Bangla: A Comprehensive Dataset for Clickbait Detection in Bangla with Multi-Feature and Multi-Modal Analysis; CARLANE Benchmark; TFix's Code Patches Data; fluocells; BanglaLekha-Isolated,COCO70; 100 sleep nights of 8 caregivers; KITTI Object Tracking Evaluation 2012; Office-Home; BanglaLekha Isolated Dataset; Retinal Fundus MultiDisease Image Dataset (RFMiD),Accuracy; EER; AUROC; 10-20% Mask PSNR,4,Yes
NLP,Paraphrase Identification,18,Autoencoder Paraphrase Dataset (AEPD); Quora Question Pairs; IMDb Movie Reviews; Machine Prarphrase Corpus (MPC); Translated SNLI Dataset in Marathi; SV-Ident; WikiHop; PAWS-X; TURL; Autoregressive Paraphrase Dataset (ARPD),Quora Question Pairs; Translated SNLI Dataset in Marathi; MSRP; WikiHop; TURL; 2017_test set; PIT; Quora Question Pairs Dev; IMDb; AP,AP; Dev F1; Val F1 Score; Direct Intrinsic Dimension; F1; Accuracy; MCC; Val Accuracy; 1:1 Accuracy; 10 fold Cross validation,0,Yes
NLP,Document Classification,18,WOS; LUN; RVL-CDIP_MP; Hyperpartisan News Detection; RVL-CDIP_N_MP; Cora; MeSHup; HOC; Reuters-21578; Wikipedia Title,Recipe; SciDocs (MAG); Yelp-14; SciDocs (MeSH); BBCSport; MPQA; AAPD; Cora; Reuters-21578; Reuters De-En,F1; Accuracy; F1 (micro); Micro F1,1,Yes
NLP,Logical Reasoning,18,Inferential-Strategies; JustLogic; GSM-Plus; Winograd Automatic; BIG-bench; SMART-101; Oxford Ontology Library; TruthQuest; QuRe; MultiQ,BIG-bench (Formal Fallacies Syllogisms Negation); Winograd Automatic; BIG-bench (Reasoning About Colored Objects); BIG-bench (Temporal Sequences); BIG-bench (Penguins In A Table); RuWorldTree; BIG-bench (Logic Grid Puzzle); LingOly; BIG-bench (Logical Fallacy Detection); BIG-bench (StrategyQA),Delta_NoContext; Accuracy ; Accuracy; Exact Match Accuracy,22,No
NLP,Paraphrase Generation,17,ParaBank; Quora Question Pairs; AP; ViSP; ChatGPT Paraphrases; OPUS; MSCOCO; StyleKQC; Duolingo STAPLE Shared Task; Autoregressive Paraphrase Dataset (ARPD),Quora Question Pairs; Paralex; MSCOCO,iBLEU; BLEU,1,Yes
Audio,Speech Separation,16,LRS2; LibriCSS; GRID Dataset; iKala; TIMIT; mDRT; MIR-1K; VoxCeleb2; WHAMR!; WHAM!,TCD-TIMIT corpus (mixed-speech); Libri20Mix; Libri10Mix; WSJ0-2mix; Libri5Mix; WSJ0-5mix; GRID corpus (mixed-speech); iKala; Libri15Mix; VoxCeleb2,30%; 10%; SI-SDRi; Number of parameters (M); MACs (G); SI-SNRi; 40%; STOI; 20%; NSDR,1,Yes
Audio,Audio Source Separation,15,MUSDB18; FUSS; SMS-WSJ; Kinect-WSJ; DNS Challenge; jaCappella; MedleyVox; WHAMR!; OpenMIC-2018; WHAM!,MUSIC (multi-source); AudioSet,SAR; SDR; SIR,3,Yes
Audio,Music Transcription,14,CocoChorales; Guitar-TECHS; JS Fake Chorales; ErhuPT; URMP; Music21; MusicNet; MAPS; YourMT3 Dataset; Cadenza Woodwind,URMP; MAPS; MusicNet; Slakh2100; SMD Piano; MAESTRO,Onset F1; APS; Number of params; note-level F-measure-no-offset (Fno),1,Yes
Audio,Speaker Verification,12,VibraVox (throat microphone); CN-CELEB; CALLHOME American English Speech; VibraVox (temple vibration pickup); EVI; VibraVox (rigid in-ear microphone); VoxCeleb2; VibraVox (headset microphone); VibraVox (soft in-ear microphone); VibraVox (forehead accelerometer),VibraVox (throat microphone); CALLHOME; VibraVox (temple vibration pickup); VoxCeleb1; VoxCeleb2; VibraVox (headset microphone); VoxCeleb; VibraVox (soft in-ear microphone); VibraVox (forehead accelerometer); VibraVox (rigid in-ear microphone),Test EER; minDCF; Cosine EER; EER; Test min-DCF,3,Yes
Audio,Speaker Diarization,11,AVA-Speech; MagicData-RAMC; FSC-P2; CALLHOME American English Speech; RadioTalk; AVA; ASR-RAMC-BIGCCSC: A CHINESE CONVERSATIONAL SPEECH CORPUS; CHiME-5; AliMeeting; AVA-ActiveSpeaker,DIHARD; DIHARD3-eval; AMI; CALLHOME; call_home_american_english_speech; AMI MixHeadset; AMI Lapel; CH109; Hub5'00 CallHome; NIST-SRE 2000,FA; MI; CF; DER(%); V; Miss; DER - no overlap; DER(ig olp),0,Yes
Audio,Music Source Separation,9,MUSDB18; CocoChorales; SynthSOD; MuseScore; MIR-1K; MUSDB18-HQ; Cadenza Woodwind; Slakh2100; MedleyDB,MUSDB18; MUSDB18-HQ; Slakh2100,Si-SDRi (Guitar); SDR (avg); SDR (others); SI-SDRi (Bass); SDR (drums); Si-SDRi (Piano); SDR (vocals); SDR (other); Si-SDRi (Drums); SDR (bass),0,Yes
Audio,Voice Conversion,8,ESD; ArVoice; GneutralSpeech Male; GneutralSpeech Female; VESUS; LibriSpeech; VIVOS; VCTK,LibriSpeech test-clean; ZeroSpeech 2019 English; VCTK,Phone Length Error (PLE); Equal Error Rate; Total Length Error (TLE); Character Error Rate (CER); Speaker Similarity; Word Error Rate (WER); Word Length Error (WLE),0,Yes
Audio,Music Modeling,6,JS Fake Chorales; Lakh Pianoroll Dataset; Lakh MIDI Dataset; Music21; Nottingham; JSB Chorales,JSB Chorales; Nottingham,Parameters; NLL,0,Yes
Audio,Speech Dereverberation,6,EARS-Reverb; Reverb-WSJ0; WHAMR_ext; WHAM!; WHAMR!; DNS Challenge,N/A,N/A,0,No
Audio,Multi-instrument Music Transcription,6,URMP; ENST Drums; YourMT3 Dataset; MIR-ST500; Cadenza Woodwind; Slakh2100,N/A,N/A,0,No
Audio,Voice Anti-spoofing,6,PartialSpoof_v1; ASVspoof 2019; PartialSpoof; Laser Data; ASVspoof 5; ReMASC,ASVspoof2019; ASVspoof 2019 - LA; ASVspoof 2019 - PA,minDCF; min a-DCF; EER; min t-dcf,0,Yes
Audio,Audio captioning,5,Clotho; WavCaps; AudioCaps; LongVALE; MACS,Clotho; AudioCaps,SPICE; FENSE; SPIDEr; #params (M); METEOR; SPIDEr-FL; Sentence-BERT; ROUGE; ROUGE-L; CIDEr,2,Yes
Audio,Music Captioning,5,Song Describer Dataset; JamendoMaxCaps; MusicCaps; MidiCaps; YouTube8M-MusicTextClips,N/A,N/A,0,No
Audio,Speaker Identification,4,FSC-P2; CSI; EVI; VoxCeleb1,EVI en-GB; VoxCeleb1; EVI fr-FR; EVI pl-PL,Accuracy; Number of Params; Top-1 (%); Top-5 (%),0,No
Audio,Speaker Separation,3,FSDnoisy18k; LibriCSS; MC_GRID,N/A,N/A,1,No
Audio,Multi-task Audio Source Seperation,3,MTASS; CocoChorales; Acappella,N/A,N/A,0,No
Audio,Rhythm,2,PTB-XL; Fraxtil,N/A,N/A,0,No
Audio,Piano Music Modeling,2,Niko Chord Progression Dataset; PIAST,N/A,N/A,0,No
Audio,Audio Signal Processing,2,mDRT; RemFX,N/A,N/A,3,Yes
Audio,Voice Cloning,2,GneutralSpeech Female; GneutralSpeech Male,N/A,N/A,0,No
Audio,Drum Transcription in Music (DTM),2,YourMT3 Dataset; ENST Drums,N/A,N/A,0,No
Audio,Salt-And-Pepper Noise Removal,1,BSD,N/A,N/A,0,No
Audio,Cross-environment ASR,1,Libri-Adapt,N/A,N/A,0,No
Audio,Cross-device ASR,1,Libri-Adapt,N/A,N/A,0,No
Audio,Noise Estimation,1,SIDD,SIDD,Average KL Divergence; PSNR Gap,0,No
Audio,Audio Fingerprint,1,Fingerprint Dataset,N/A,N/A,0,No
Audio,Soundscape evaluation,1,Subjective Perception of Active Noise Reduction (SPANR),N/A,N/A,0,Yes
Audio,"Speaker Attribution in German Parliamentary Debates (GermEval 2023, subtask 1)",1,GePaDe,GePaDe,F1,0,Yes
Audio,"Speaker Attribution in German Parliamentary Debates (GermEval 2023, subtask 2)",1,GePaDe,GePaDe,F1,0,Yes
Audio,Acoustic Modelling,1,SALMon,N/A,N/A,0,No
Audio,Speaker Profiling,1,HeightCeleb,N/A,N/A,0,Yes
Audio,Recognizing Seven Different Dastgahs Of Iranian Classical Music,0,N/A,N/A,N/A,0,No
Audio,Audio declipping,0,N/A,N/A,N/A,0,Yes
Audio,Audio Dequantization,0,N/A,N/A,N/A,0,Yes
Audio,Speaker Orientation,0,N/A,N/A,N/A,0,Yes
Audio,Hate Speech Normalization,0,N/A,N/A,N/A,0,No
Audio,blind source separation,0,N/A,N/A,N/A,0,Yes
Audio,Speech Representation Learning,0,N/A,N/A,N/A,0,No
Audio,Semi-Supervised Audio Regression,0,N/A,N/A,N/A,0,No
Audio,Speaker anonymization,0,N/A,N/A,N/A,0,No
Other,Person Re-Identification,65,eSports Sensors Dataset; DukeMTMC-reID; Dataset: Privacy-Preserving Gaze Data Streaming in Immersive Interactive Virtual Reality: Robustness and User Experience.; Market1501-Attributes; ICFG-PEDES; P-DukeMTMC-reID; Market-1501; BTS3.1; TVPReid; CityFlow,eSports Sensors Dataset; DukeMTMC-reID; Market-1501->DukeMTMC-reID; P-DukeMTMC-reID; Market-1501; AG-ReID; SYSU-MM01; Market-1501-C; MSMT17; PRID2011, mAP; Rank-1 (Indoor Search);  mAP (All Search); ROC AUC;  Rank-5; Rank-1 (All Search); mINP;  mAP (Indoor Search);  mINP (Indoor Search); mINP (All Search),13,Yes
Other,Data Augmentation,63,CCPD; Evidence Inference; Clotho; MVTec D2S; LITIS Rouen; Incidents; Konzil; MeQSum; Synthetic COVID-19 CXR Dataset; Slakh2100,CIFAR-10; ImageNet; GA1457,Accuracy (%); Percentage error; Classification Accuracy,2,Yes
Other,Multi-Task Learning,59,Photographic Defect Severity; NYUv2; Clotho; CQR; Meta-World Benchmark; THCHS-30; CropAndWeed; UTKFace; FIW-MM; CS,NYUv2; CelebA; ChestX-ray14; Cityscapes test; QM9; UTKFace; OMNIGLOT; wireframe dataset,RMSE; Average Accuracy; sAP10; Error; sAP15; delta_m; FH; Mean IoU; mIoU; ∆m%,2,Yes
Other,Self-Supervised Learning,47,MAX-60K; 3DSeg-8; Argoverse 2 Lidar; Open Radar Datasets; DABS; YUD+; OrangeSum; AVA-ActiveSpeaker; COVID-CT; Wild-Time,TinyImageNet; STL-10; DABS; CREMA-D; cifar100; CIFAR-100; CIFAR-10; ImageNet-100 (TEMI Split); cifar10; Tiny ImageNet,Med. Imaging; Sensors; Text; Natural Images; average top-1 classification accuracy; Top-1 Accuracy; Speech; Images & Text; Accuracy,2,Yes
Other,Time Series Forecasting,46,METR-LA; UTSD; Consumer Spendings; Multivariate-Mobility-Paris; Amazon MTPP; PJM(AEP); Hotel; Weather; EarthNet2021; Exchange,ETTh2 (168) Multivariate; ETTh1 (720) Multivariate; Weather2K1786 (336); ETTh1 (48) Univariate; Weather (96); ETTh1 (336) Univariate; Solar (96); ETTh1 (336) Multivariate; Electricity (336); ETTh2 (24) Univariate,MSE ; MAPE; RMSE; MSE; 9 steps MAE; Accuracy; MAE,6,Yes
Other,Entity Linking,44,ShARe/CLEF 2014: Task 2 Disorders; DBLP-QuAD; BB; WebQuestionsSP; Twitter-MEL; BC7 NLM-Chem; MEIR; CoNLL; BioRED; WiC-TSV,BC7 NLM-Chem; WebQSP-WD; N3-Reuters-128; OKE-2015; WiC-TSV; TAC-KBP 2010; MSNBC; CoNLL-Aida; Rare Diseases Mentions in MIMIC-III (Text-to-UMLS); FUNSD,Task 3 Accuracy: all; Macro F1; Micro F1; Task 3 Accuracy: general purpose; Task 2 Accuracy: domain specific; Task 2 Accuracy: general purpose; Task 1 Accuracy: general purpose; Micro-F1 strong; F1-score (strict); Task 1 Accuracy: all,0,Yes
Other,Misinformation,44,NELA-GT-2018; CHECKED; ArCOV-19; COVID-Q; NELA-GT-2020; Monant Medical Misinformation; HpVaxFrames; COVID-19 Twitter Chatter Dataset; NELA-GT-2021; Stanceosaurus,NLP4IF-2021--Fighting the COVID-19 Infodemic ,Average F1,0,No
Other,Decision Making,40,Evidence Inference; SentiCap; GazeFollow; Flickr Cropping Dataset; RoboNet; Negotiation Dialogues Dataset; DemCare; ROAD; BeNYfits; Industrial Benchmark Dataset for Customer Escalation Prediction,./01/01/1967; NASA C-MAPSS,0..5sec; Average Remaining Cycles,1,No
Other,Continual Learning,35,"DSC (10 tasks); WikiArt; ASC (TIL, 19 tasks); ROAD; ConCon Dataset; SPOT-10; HASY; CRL-Person; HOWS; Wild-Time",DSC (10 tasks); Split MNIST (5 tasks); CUB-200-2011 (20 tasks) - 1 epoch; Tiny-ImageNet (10tasks); visual domain decathlon (10 tasks); Flowers (Fine-grained 6 Tasks); AIDS; MLT17; split CIFAR-100; 5-Datasets,Avg. Accuracy; Top 1 Accuracy %; Pretrained/Transfer Learning; Average Accuracy; decathlon discipline (Score); BWT; 1:3 Accuracy; Accuracy; F1 - macro; MLP Hidden Layers-width,5,Yes
Other,Metric Learning,33,TopLogo-10; DyML-Vehicle; Luna-1; VIPeR; CUB-200-2011; In-Shop; Tsinghua Dogs; DyML-Product; CASIA-WebFace; WildDeepfake,DyML-Animal; DyML-Vehicle; CUB-200-2011; In-Shop; Stanford Online Products; CARS196; DyML-Product;  CUB-200-2011,Average-mAP; R@1,0,Yes
Other,regression,31,"MAX-60K; FLIP; INI-30; DRIFT; SciRepEval; FLIP -- AAV, Designed vs mutant; California Housing Prices; bcTCGA; MineralImage5k; Concrete Compressive Strength",Synthetic: y = x * sin x; ADORE; Medical Cost Personal Dataset; California Housing Prices; Car_Price_Prediction; Concrete Compressive Strength,chemical macro-average RMSE; R2 Score; lambda; R Squared; micro-averaged RMSE,2,No
Other,Drug Discovery,26,CausalBench; ImDrug; SIDER; approved_drug_target; KIBA; DAVIS-DTA; Tox21; HIV (Human Immunodeficiency Virus); QED; FreeSolv (Free Solvation),SIDER; BindingDB IC50; ToxCast; DRD2; KIBA; DAVIS-DTA; BACE; PDBbind; Tox21; QED,Error ratio; CI; RMSE; MSE; Diversity; Success; AUC; Pearson Correlation,3,Yes
Other,Time Series Analysis,25,eSports Sensors Dataset; A Simulated 4-DOF Ship Motion Dataset for System Identification under Environmental Disturbances; Consumer Spendings; Hotel; Solar-Power; Paderbone University Bearing Fault Benckmark; MOSAD; EigenWorms; Extreme Events > Natural Disasters > Hurricane; PhysioNet Challenge 2012,PhysioNet Challenge 2012; Speech Commands; Ventilator Pressure Prediction,F1; % Test Accuracy (Raw Data); % Test Accuracy; MAE,12,Yes
Other,Fairness,23,WinoBias; BAF; SPEECH-COCO; ImDrug; RFW; UTKFace; ACS PUMS; DiveFace; DialogueFairness; HELP,DiveFace; BAF – Variant I; BAF – Base; BAF – Variant III; MORPH; BAF – Variant V; UTKFace; BAF – Variant II; BAF – Variant IV,Degree of Bias (DoB); Predictive Equality (age),1,No
Other,Multimodal Deep Learning,22,Multimodal PISA; CUB-200-2011; Uncorrelated Corrupted Dataset; WebLI; Mudestreda; Gaze-CIFAR-10; ScienceQA; MIMIC Meme Dataset; OLIVES Dataset; MVK,CUB-200-2011,Accuracy,1,Yes
Other,Robotic Grasping,22,GraspClutter6D; Grasp MultiObject; RoboNet; Multiview Manipulation Data; robosuite Benchmark; Calandra Dataset; Cornell; HouseCat6D; NBMOD; Dex-Net 2.0,NBMOD; GraspNet-1Billion;  Jacquard dataset; Cornell Grasp Dataset,Accuracy (%); AP_novel; mAP; 5 fold cross validation; AP_similar; AP_seen; Acc,1,Yes
Other,Graph Regression,21,ZINC; MoleculeNet; PolyDensity; OCB; DrivAerNet; SupplyGraph; Tox21; hERG; PCQM4Mv2-LSC; GlassTemp,ZINC 10k; PGR ; PCQM4M-LSC; ZINC; F2; PARP1; ZINC-500k; ESOL; ZINC 100k; ZINC-full,Validation MAE; AUC@80%Train; RMSE; RMSE ; RMSE@80%Train; R2; MAE; Test MAE; Inference Time (ms),0,Yes
Other,Imitation Learning,21,CARLA; AI2-THOR; Reactive Diffusion Policy-Dataset; IL-Datasets; DeformPAM-Dataset; CHALET; ManiSkill2; MineRL; StarData; AirSim,N/A,N/A,1,Yes
Other,Age Estimation,20,USF; VGGFace2 HQ; Age and Gender; Bone Age; UTKFace; CACD; MegaAge; AADB; Facial  Skeletal angles; mebeblurf,ChaLearn 2015; AFAD; mebeblurf; AgeDB; LAGENDA; PhysioNet Challenge 2021; UTKFace; KANFace; FGNET; IMDB-Clean,Average mean absolute error; Mean Squared Error; Mean Squared Error (cross-val); e-error; Accuracy; CS; Mean absolute error; MAE; Mean Absolute Error (cross-val),1,Yes
Other,Reinforcement Learning (RL),20,V-D4RL; FinRL-Meta; CivRealm; ManiSkill2; RoomEnv-v2; POPGym; QDax; lilGym; MIDGARD; MIKASA-Robo Dataset,.; ProcGen,0..5sec; Mean Normalized Performance,6,Yes
Other,Graph Clustering,19,Citeseer; SLNET; Orkut; Pollen et al; Goolam et al; 97 synthetic datasets; Deng et al; Cora; Treutlein et al; SNAP,Citeseer; Deng et al; Cora; Treutlein et al; Pollen et al; Bozec et al; Biase et al; Goolam et al; Yan et al; Pubmed,Adjusted Rand Index; NMI; Precision; F1; ARI; F score; ACC,1,Yes
Other,Quantization,18,REDDIT-12K; IJB-C; Bach Doodle; FAS100K; Kannada-MNIST; LFW; Groove; Reddit; Twitter100k; Wiki-40B,IJB-C; AgeDB-30; LFW; Wiki-40B; CFP-FP; CIFAR-10; ImageNet; Knowledge-based:; COCO (Common Objects in Context); IJB-B,Perplexity; TAR @ FAR=1e-4; Weight bits; Activation bits; Accuracy; All; Top-1 Accuracy (%); MAP,2,Yes
Other,Multivariate Time Series Forecasting,18,ETT; METR-LA; PeMSD8; MIMIC-III; Multivariate-Mobility-Paris; PhysioNet Challenge 2012; Weather; PeMSD7; PeMS04; PEMS-BAY,ETTh1 (48) Multivariate; ETTh1 (720) Multivariate; MIMIC-III; USHCN-Daily; ETTh2 (720) Multivariate; PhysioNet Challenge 2012; Weather; ETTh1 (96) Multivariate; Helpdesk; Traffic,"MSE stdev; Jitter; NegLL; MSE ; RMSE; normalized RMSE; MSE; mse (10^-3); Accuracy; MSE (10^-2, 50% missing)",1,No
Other,Time Series,18,ETT; Gaze-CIFAR-10; Sales; QM9; Weather; stocknet; MultiSenseBadminton; BorealTC; MSL; Monopedal Gaits,N/A,N/A,0,No
Other,Binarization,17,H-DIBCO 2010; DIBCO 2019; H-DIBCO 2012; H-DIBCO 2016; DIBCO 2011; DIBCO 2009; CIFAR-100; CIFAR-10; H-DIBCO 2018; ImageNet,N/A,N/A,0,No
Other,Topic Models,17,"AG News; OpoSum; RuWiki-Good; MIE Articles Dataset; Reddit; Mapping Topics in 100,000 Real-Life Moral Dilemmas; New York Times Annotated Corpus; MIE Articles Dataset (1996-2024); 20NewsGroups; COVID-19 Tweets with Motivation and Topics",AG News; AgNews; 20NewsGroups; Arxiv HEP-TH citation graph; 20 Newsgroups; NYT,NPMI; C_v; Topic coherence@5; Topic Coherence@50; Test perplexity; MACC,2,Yes
Other,Meta-Learning,17,Chinese Literature NER RE; Meta Omnium; Meta-World Benchmark; TyDiQA-GoldP; Meta-Dataset; Kuzushiji-49; ExtremeWeather; MEx; CODEBRIM; Prostate MRI Segmentation Dataset,"ML45; MT50; OMNIGLOT - 1-Shot, 20-way; ML10",Average Success Rate; Meta-test success rate; % Test Accuracy; Meta-train success rate; Meta-test success rate (zero-shot),3,Yes
Other,SMAC+,17,Off_Hard_sequential; Def_Infantry_sequential; Def_Outnumbered_parallel; Off_Superhard_parallel; Off_Complicated_sequential; Def_Armored_parallel; Def_Outnumbered_sequential; Off_Hard_parallel; Def_Armored_sequential; Off_Distant_sequential,N/A,N/A,0,No
Other,Long-tail Learning,16,Imbalanced-MiniKinetics200; CelebA; EGTEA; NIH-CXR-LT; Kinetics; ImageNet-LT; mini-ImageNet-LT; CIFAR-100; COCO-MLT; Animal Kingdom,CIFAR-10-LT (ρ=200); ImageNet-LT; COCO-MLT; MIMIC-CXR-LT; Places-LT; VOC-MLT; iNaturalist 2018; CIFAR-100-LT (ρ=50); Lot-insts; CIFAR-10-LT (ρ=50),Top 1 Accuracy; Average Recall; Average mAP; Average Precision; Top-1 Accuracy; Accuracy;  Macro-F1; Error Rate; Balanced Accuracy,1,Yes
Other,Prompt Engineering,16,Food-101; FGVC-Aircraft; ImageNet-R; Oxford-IIIT Pets; ImageNet-A; Oxford 102 Flower; Stanford Cars; Oxford-IIIT Pet Dataset; SUN397; OmniBenchmark,Food-101; FGVC-Aircraft; ImageNet-R; ImageNet V2; ImageNet-A; Oxford 102 Flower; Stanford Cars; Oxford-IIIT Pet Dataset; SUN397; ImageNet,Accuracy; Harmonic mean; Top-1 accuracy %,1,Yes
Other,Contrastive Learning,16,"CommitBART; DAD; BankNote-Net; STL-10; INRIA Aerial Image Labeling; GuitarSet; 10,000 People - Human Pose Recognition Data; US-4; CIFAR-10; Extended Agriculture-Vision","imagenet-1k; STL-10; 10,000 People - Human Pose Recognition Data; CIFAR-10",0..5sec; Accuracy (Top-1); ImageNet Top-1 Accuracy,0,Yes
Other,Learning with noisy labels,16,Galaxy Zoo DECaLS; ANIMAL; Red MiniImageNet 20% label noise; Red MiniImageNet 80% label noise; CIFAR-10N; Chaoyang; CIFAR-100; VoxCeleb1; Clothing1M; CIFAR-10,N/A,N/A,0,No
Other,Self-Driving Cars,16,HPD; SOD; OC; TUT Sound Events 2018; Lost and Found; Argoverse 2 Motion Forecasting; GD; Shifts; Hyper Drive; BDD-X,N/A,N/A,0,No
Other,Weather Forecasting,16,Extreme Events > Natural Disasters > Hurricane; SEVIR; Weather2K; A2D2; NOAA Atmospheric Temperature Dataset; nuScenes; WeatherBench; Shifts; GFF; CloudCast,SEVIR; NOAA Atmospheric Temperature Dataset; Shifts; LA; SD,MSE (t+6); MSE; MAE (t+10); MSE (t+1); mCSI; R-AUC MSE; MAE (t+1),1,Yes
Other,Node Clustering,15,Citeseer; Wiki-CS; IMDb Movie Reviews; Cornell; WebKB; arXivCS; Cora; Wiki; Facebook Pages; HeriGraph,N/A,N/A,0,No
Other,Physical Simulations,15,CGNE-Snowflakes; PlasticineLab; Mechanical Metamaterial: Square Array of Circular Holes Under Deformation; A Simulated 4-DOF Ship Motion Dataset for System Identification under Environmental Disturbances; ClimART; Expressive Gaussian mixture models for high-dimensional statistical modelling: simulated data and neural network model files; 4D-DRESS; DrivAerNet; Dataset and Model Weights for Plasma Sheet Model Graph Network Simulator; 2D_NACA_RANS,Impact Plate; 4D-DRESS; Deformable Plate; Deforming Plate; Sphere Simple,Rollout RMSE-all [1e3] Stress; Chamfer (cm); Stretching Energy; Rollout RMSE-all [1e3] Position,3,No
Other,Within-Session ERP,15,BNCI2014-009 MOABB; BrainInvaders2015b MOABB; BrainInvaders2013a MOABB; BrainInvaders2014a MOABB; BrainInvaders2014b MOABB; Sosulski2019 MOABB; BrainInvaders2012 MOABB; BNCI2014-008 MOABB; Cattan2019-VR MOABB; BrainInvaders2015a MOABB,N/A,N/A,0,No
Other,Density Estimation,13,COWC; BSD; UCI Machine Learning Repository; APRICOT; JHU-CROWD; JHU-CROWD++; CIFAR-10; Silhouettes; Caltech-101; MNIST,BSDS300; Freyfaces; UCI POWER; UCI MINIBOONE; CIFAR-10; ImageNet 64x64; CIFAR-10 (Conditional); UCI HEPMASS; OMNIGLOT; Caltech-101,Negative ELBO; MMD-L2; MMD-CD; NLL; Log-likelihood; COV-L2; EMD; Log-likelihood (nats); NLL (bits/dim); MMD-EMD,1,Yes
Other,Clustering Algorithms Evaluation,13,pixraw10P; iris; Fashion-MNIST; JAFFE; ionosphere; Failure-Dataset-OpenStack; Olivetti face; seeds; Satimage; Online retail dataset,pixraw10P; iris; Fashion-MNIST; JAFFE; ionosphere; Olivetti face; seeds; pathbased; MNIST; 97 synthetic datasets,Purity; NMI; F1-score; ARI; HIT-THE-BEST; Rank difference,1,No
Other,Adversarial Robustness,13,Stylized ImageNet; SHADR; ImageNet-A; ImageNet-Patch; ImageNet-C; AdvGLUE; CIFAR-100; Speech Robust Bench; CIFAR-10; ImageNet,Stylized ImageNet; ImageNet-A; AdvGLUE; ImageNet-C; CIFAR-100; CIFAR-10; ImageNet,Clean Accuracy; Robust Accuracy; mean Corruption Error (mCE); AutoAttacked Accuracy; Accuracy; Attack: AutoAttack,0,Yes
Other,UIE,13,WikiANN; New York Times Annotated Corpus; OntoNotes 5.0; BC2GM; ACE 2004; BC5CDR; FindVehicle; SciERC; CoNLL 2003; SUIM-E,N/A,N/A,0,No
Other,Entity Disambiguation,13,Mewsli-9; diaforge-utc-r-0725; AIDA CoNLL-YAGO; WikiSRS; Hansel; Wikidata-Disamb; ACE 2004; DocRED-IE; AQUAINT; ShadowLink,Mewsli-9; MSNBC; AIDA-CoNLL; ShadowLink-Top; WNED-WIKI; ACE2004; DocRED-IE; ShadowLink-Shadow; WNED-CWEB; AQUAINT,Micro-F1; Micro Precision; In-KB Accuracy; Avg F1,0,Yes
Other,Open-Domain Dialog,13,CPsyCounE; CPsyCounD; Reddit; DuLeMon; MMChat; Reddit Conversation Corpus; CPED; MMDialog; MultiDoc2Dial; KILT,KILT: Wizard of Wikipedia,Recall@5; F1; KILT-F1; R-Prec; KILT-RL; ROUGE-L,1,No
Other,Disentanglement,13,MPI3D Disentanglement; Coil100-Augmented; 3D Cars; Natural Sprites; dSprites; Sprites; 3D Shapes Dataset; smallNORB; Causal3DIdent; XYSquares,N/A,N/A,0,No
Other,Graph Embedding,12,Financial Dynamic Knowledge Graph; OLPBENCH; IS-A; Synthetic Dynamic Networks; ChEMBL; KG20C; HeriGraph; AIDS Antiviral Screen; BioGRID; CARD-660,Barabasi-Albert,Entropy Difference,5,Yes
Other,Imputation,12,Adult; UTSD; MotionSense; FDF; WebKB; PhysioNet Challenge 2012; Sprites; PEMS-BAY Point Missing; AIDS Antiviral Screen; METR-LA Point Missing,Adult; HMNIST; PhysioNet Challenge 2012; Sprites,NLL; Test error; AUROC; MSE,1,Yes
Other,Entity Typing,12,FIGER; GUM; AIDA CoNLL-YAGO; WikiSRS; OntoNotes 5.0; DocRED-IE; Open Entity; Figment; HTDM; Few-NERD,FIGER; Ontonotes v5 (English); Freebase FIGER; AIDA-CoNLL; DocRED-IE;  Open Entity; Open Entity; OntoNotes,Recall; Precision; F1; P@1; Macro F1; Micro F1; Accuracy; Avg F1; BEP; Micro-F1,1,Yes
Other,COVID-19 Diagnosis,12,COVIDGR; BrixIA; COVIDx; COVIDx CXR-3; CoIR; Large COVID-19 CT scan slice dataset; Synthetic COVID-19 CXR Dataset; COVID-19-CT-CXR; BIMCV COVID-19; Novel COVID-19 Chestxray Repository,COVID-19 CXR Dataset; ; COVIDx; COVIDx CXR-3; Large COVID-19 CT scan slice dataset; Novel COVID-19 Chestxray Repository; Covid-19 Cough Cambridge; COVIDGR,Macro Recall; Micro Precision; Specificity; ACCURACY; Average F1; Average Recall; Average Precision; Macro F1; 3-class test accuracy; AUC-ROC,0,Yes
Other,Referring Expression Comprehension,12,VQDv1; RefCOCO; GRIT; CLEVR-Ref+; ColonINST-v1 (Unseen); ColonINST-v1; ColonINST-v1 (Seen); Description Detection Dataset; Google Refexp; FineCops-Ref,N/A,N/A,0,No
Other,Entity Resolution,12,Amazon-Google; diaforge-utc-r-0725; DBLP Temporal; MovieGraphBenchmark; WDC LSPM; MusicBrainz20K; Abt-Buy; PIZZA; Binette's 2022 Inventors Benchmark; WDC Products,Amazon-Google; MusicBrainz20K; Abt-Buy; WDC Products; WDC Watches-xlarge; WDC Products-80%cc-seen-medium; WDC Computers-small; WDC Products-50%cc-unseen-medium; WDC Computers-xlarge; WDC Products-80%cc-seen-medium-multi,F1; F1 Micro; F1 (%),1,Yes

{
  "task": "Dialogue",
  "description": "Dialogue is notoriously hard to evaluate. Past approaches have used human evaluation.",
  "subtasks": [
    {
      "task": "Dialogue act classification",
      "description": "Dialogue act classification is the task of classifying an utterance with respect to the fuction it serves in a dialogue, i.e. the act the speaker is performing. Dialogue acts are a type of speech acts (for Speech Act Theory, see [Austin (1975)](http://www.hup.harvard.edu/catalog.php?isbn=9780674411524) and [<PERSON><PERSON> (1969)](https://www.cambridge.org/core/books/speech-acts/D2D7B03E472C8A390ED60B86E08640E7)).",
      "datasets": [
        {
          "dataset": "Switchboard corpus",
          "description": "The [Switchboard-1 corpus](https://catalog.ldc.upenn.edu/ldc97s62) is a telephone speech corpus, consisting of about 2,400 two-sided telephone conversation among 543 speakers with about 70 provided conversation topics. The dataset includes the audio files and the transcription files, as well as information about the speakers and the calls. The Switchboard Dialogue Act Corpus (SwDA) [[download](https://web.stanford.edu/~jurafsky/swb1_dialogact_annot.tar.gz)] extends the Switchboard-1 corpus with tags from the [SWBD-DAMSL tagset](https://web.stanford.edu/~jurafsky/ws97/manual.august1.html), which is an augmentation to the Discourse Annotation and Markup System of Labeling (DAMSL) tagset. The 220 tags were reduced to 42 tags by clustering in order to improve the language model on the Switchboard corpus. A subset of the Switchboard-1 corpus consisting of 1155 conversations was used. The resulting tags include dialogue acts like statement-non-opinion, acknowledge, statement-opinion, agree/accept, etc. Annotated example: *Speaker:* A, *Dialogue Act:* Yes-No-Question, *Utterance:* So do you go to college right now?",
          "subdatasets": [
            {
              "subdataset": "Medical partition",
              "sota": {
              }
            }
          ],
          "dataset_links": [{
            "title": "Switchboard-1 corpus",
            "url": "https://catalog.ldc.upenn.edu/ldc97s62"
          }],
          "dataset_citations": [
            {
              "title": "",
              "url": "",
            }
          ],
          "sota": {
            "metrics": ["accuracy"],
            "rows": [
              {
                "model_name": "CRF-ASN (Chen et al., 2018)",
                "paper_title": "Dialogue Act Recognition via CRF-Attentive Structured Network",
                "paper_url": "https://arxiv.org/abs/1711.05568",
                "paper_date": "",
                "code_links": [],
                "model_links": [],
                "metrics": {
                  "accuracy": 81.3
                }
              },
              {
                "model_name": "Bi-LSTM-CRF (Kumar et al., 2017)",
                "paper_title": "Dialogue Act Sequence Labeling using Hierarchical encoder with CRF",
                "paper_url": "https://arxiv.org/abs/1709.04250",
                "paper_date": "",
                "code_links": [{
                  "title": "Link",
                  "url": "https://github.com/YanWenqiang/HBLSTM-CRF"
                }],
                "model_links": [],
                "metrics": {
                  "accuracy": 79.2
                }
              },
              {
                "model_name": "RNN with 3 utterances in context (Bothe et al., 2018)",
                "paper_title": "A Context-based Approach for Dialogue Act Recognition using Simple Recurrent Neural Networks",
                "paper_url": "https://arxiv.org/abs/1805.06280",
                "paper_date": "",
                "code_links": [],
                "model_links": [],
                "metrics": {
                  "accuracy": 77.34
                }
              }
            ]
          }
        }
      ]
    }
  ]
}
[{"categories": [], "datasets": [{"dataset": "SNLI", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["% Test Accuracy", "% Train Accuracy", "Parameters"], "rows": [{"code_links": [], "metrics": {"% Test Accuracy": "50.4", "% Train Accuracy": "49.4", "Parameters": ""}, "model_links": [], "model_name": "Unlexicalized features", "paper_date": null, "paper_title": "A large annotated corpus for learning natural language inference", "paper_url": "http://nlp.stanford.edu/pubs/snli_paper.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "78.2", "% Train Accuracy": "99.7", "Parameters": ""}, "model_links": [], "model_name": "+ Unigram and bigram features", "paper_date": null, "paper_title": "A large annotated corpus for learning natural language inference", "paper_url": "http://nlp.stanford.edu/pubs/snli_paper.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "77.6", "% Train Accuracy": "84.8", "Parameters": "220k"}, "model_links": [], "model_name": "100D LSTM encoders", "paper_date": null, "paper_title": "A large annotated corpus for learning natural language inference", "paper_url": "http://nlp.stanford.edu/pubs/snli_paper.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "80.6", "% Train Accuracy": "83.9", "Parameters": "3.0m"}, "model_links": [], "model_name": "300D LSTM encoders", "paper_date": null, "paper_title": "A Fast Unified Model for Parsing and Sentence Understanding", "paper_url": "https://www.nyu.edu/projects/bowman/spinn.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "81.4", "% Train Accuracy": "98.8", "Parameters": "15m"}, "model_links": [], "model_name": "1024D GRU encoders w/ unsupervised 'skip-thoughts' pre-training", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON> et al. '15", "paper_url": "http://arxiv.org/pdf/1511.06361v3.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "82.1", "% Train Accuracy": "83.3", "Parameters": "3.5m"}, "model_links": [], "model_name": "300D Tree-based CNN encoders", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '15", "paper_url": "http://arxiv.org/pdf/1512.08422.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "83.2", "% Train Accuracy": "89.2", "Parameters": "3.7m"}, "model_links": [], "model_name": "300D SPINN-PI encoders", "paper_date": null, "paper_title": "A Fast Unified Model for Parsing and Sentence Understanding", "paper_url": "https://www.nyu.edu/projects/bowman/spinn.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "83.3", "% Train Accuracy": "86.4", "Parameters": "2.0m"}, "model_links": [], "model_name": "600D (300+300) BiLSTM encoders", "paper_date": null, "paper_title": "<PERSON> et al. '16", "paper_url": "http://arxiv.org/pdf/1605.09090v1.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "83.4", "% Train Accuracy": "82.5", "Parameters": "4.0m"}, "model_links": [], "model_name": "300D NTI-SLSTM-LSTM encoders", "paper_date": null, "paper_title": "Munkhdalai & Yu '16b", "paper_url": "http://arxiv.org/pdf/1607.04492v1.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "84.2", "% Train Accuracy": "84.5", "Parameters": "2.8m"}, "model_links": [], "model_name": "600D (300+300) BiLSTM encoders with intra-attention", "paper_date": null, "paper_title": "<PERSON> et al. '16", "paper_url": "http://arxiv.org/pdf/1605.09090v1.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "84.5", "% Train Accuracy": "85.6", "Parameters": "40m"}, "model_links": [], "model_name": "4096D BiLSTM with max-pooling", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON> et al. '17", "paper_url": "https://arxiv.org/pdf/1705.02364.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "84.6", "% Train Accuracy": "86.2", "Parameters": "3.0m"}, "model_links": [], "model_name": "300D NSE encoders", "paper_date": null, "paper_title": "Munkhdalai & Yu '16a", "paper_url": "http://arxiv.org/pdf/1607.04315.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "85.5", "% Train Accuracy": "90.5", "Parameters": "12m"}, "model_links": [], "model_name": "600D (300+300) Deep Gated Attn. BiLSTM encoders", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '17", "paper_url": "http://arxiv.org/pdf/1708.01353.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "85.6", "% Train Accuracy": "91.1", "Parameters": "2.4m"}, "model_links": [], "model_name": "300D Directional self-attention network encoders", "paper_date": null, "paper_title": "<PERSON> et al. '17", "paper_url": "http://arxiv.org/pdf/1709.04696.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "85.6", "% Train Accuracy": "91.2", "Parameters": "2.9m"}, "model_links": [], "model_name": "300D Gumbel TreeLSTM encoders", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '17", "paper_url": "https://arxiv.org/pdf/1707.02786.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "85.7", "% Train Accuracy": "89.8", "Parameters": "9.7m"}, "model_links": [], "model_name": "300D Residual stacked encoders", "paper_date": null, "paper_title": "<PERSON><PERSON> and <PERSON><PERSON> '17", "paper_url": "https://arxiv.org/pdf/1708.02312.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "85.9", "% Train Accuracy": "–", "Parameters": "–"}, "model_links": [], "model_name": "1200D REGMAPR (Base+Reg)", "paper_date": null, "paper_title": "Anonymous '18", "paper_url": "https://openreview.net/forum?id=rylNzLFEkm"}, {"code_links": [], "metrics": {"% Test Accuracy": "85.9", "% Train Accuracy": "87.3", "Parameters": "3.7m"}, "model_links": [], "model_name": "300D CAFE (no cross-sentence attention)", "paper_date": null, "paper_title": "<PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1801.00102.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.0", "% Train Accuracy": "93.1", "Parameters": "10m"}, "model_links": [], "model_name": "600D Gumbel TreeLSTM encoders", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '17", "paper_url": "https://arxiv.org/pdf/1707.02786.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.0", "% Train Accuracy": "91.0", "Parameters": "29m"}, "model_links": [], "model_name": "600D Residual stacked encoders", "paper_date": null, "paper_title": "<PERSON><PERSON> and <PERSON><PERSON> '17", "paper_url": "https://arxiv.org/pdf/1708.02312.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.3", "% Train Accuracy": "92.6", "Parameters": "3.1m"}, "model_links": [], "model_name": "300D Reinforced Self-Attention Network", "paper_date": null, "paper_title": "<PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1801.10296.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.3", "% Train Accuracy": "89.6", "Parameters": "4.7m"}, "model_links": [], "model_name": "Distance-based Self-Attention Network", "paper_date": null, "paper_title": "<PERSON><PERSON> and <PERSON> '17", "paper_url": "https://arxiv.org/pdf/1712.02047.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.5", "% Train Accuracy": "91.4", "Parameters": "5.6m"}, "model_links": [], "model_name": "Densely-Connected Recurrent and Co-Attentive Network (encoder)", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1805.11360.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.6", "% Train Accuracy": "89.9", "Parameters": "22m"}, "model_links": [], "model_name": "600D Hierarchical BiLSTM with Max Pooling (HBMP, code)", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1808.08762.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.6", "% Train Accuracy": "94.9", "Parameters": "65m"}, "model_links": [], "model_name": "600D BiLSTM with generalized pooling", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1806.09828.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.7", "% Train Accuracy": "91.6", "Parameters": "9m"}, "model_links": [], "model_name": "512D Dynamic Meta-Embeddings", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1804.07983.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.8", "% Train Accuracy": "87.3", "Parameters": "2.1m"}, "model_links": [], "model_name": "600D Dynamic Self-Attention Model", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1808.07383.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "87.4", "% Train Accuracy": "89.0", "Parameters": "7.0m"}, "model_links": [], "model_name": "2400D Multiple-Dynamic Self-Attention Model", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1808.07383.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "83.5", "% Train Accuracy": "85.3", "Parameters": "250k"}, "model_links": [], "model_name": "100D LSTMs w/ word-by-word attention", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>  et al. '15", "paper_url": "http://arxiv.org/pdf/1509.06664v1.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "84.6", "% Train Accuracy": "85.2", "Parameters": "320k"}, "model_links": [], "model_name": "100D DF-LSTM", "paper_date": null, "paper_title": "Deep Fusion LSTMs for Text Semantic Matching", "paper_url": "https://pdfs.semanticscholar.org/adc1/84fcb04107f95e35ea1b07ef9aad749da8d7.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "85.0", "% Train Accuracy": "85.9", "Parameters": "2.8m"}, "model_links": [], "model_name": "600D (300+300) BiLSTM encoders with intra-attention and symbolic preproc.", "paper_date": null, "paper_title": "<PERSON> et al. '16", "paper_url": "http://arxiv.org/pdf/1605.09090v1.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "85.1", "% Train Accuracy": "86.7", "Parameters": "190k"}, "model_links": [], "model_name": "50D stacked TC-LSTMs", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON> et al. '16b", "paper_url": "https://arxiv.org/pdf/1605.05573v2.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "85.4", "% Train Accuracy": "86.9", "Parameters": "3.2m"}, "model_links": [], "model_name": "300D MMA-NSE encoders with attention", "paper_date": null, "paper_title": "Munkhdalai & Yu '16a", "paper_url": "http://arxiv.org/pdf/1607.04315.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.1", "% Train Accuracy": "92.0", "Parameters": "1.9m"}, "model_links": [], "model_name": "300D mLSTM word-by-word attention model", "paper_date": null, "paper_title": "Wang & Jiang '15", "paper_url": "http://arxiv.org/pdf/1512.08849v1.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "85.7", "% Train Accuracy": "87.3", "Parameters": "1.7m"}, "model_links": [], "model_name": "300D LSTMN with deep attention fusion", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON> et al. '16", "paper_url": "http://arxiv.org/pdf/1601.06733.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.3", "% Train Accuracy": "88.5", "Parameters": "3.4m"}, "model_links": [], "model_name": "450D LSTMN with deep attention fusion", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON> et al. '16", "paper_url": "http://arxiv.org/pdf/1601.06733.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.3", "% Train Accuracy": "89.5", "Parameters": "380k"}, "model_links": [], "model_name": "200D decomposable attention model", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON> et al. '16", "paper_url": "http://arxiv.org/pdf/1606.01933v1.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "86.8", "% Train Accuracy": "90.5", "Parameters": "580k"}, "model_links": [], "model_name": "200D decomposable attention model with intra-sentence attention", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON> et al. '16", "paper_url": "http://arxiv.org/pdf/1606.01933v1.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "87.3", "% Train Accuracy": "88.5", "Parameters": "3.2m"}, "model_links": [], "model_name": "300D Full tree matching NTI-SLSTM-LSTM w/ global attention", "paper_date": null, "paper_title": "Munkhdalai & Yu '16b", "paper_url": "http://arxiv.org/pdf/1607.04492v1.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "87.5", "% Train Accuracy": "90.9", "Parameters": "1.6m"}, "model_links": [], "model_name": "BiMPM", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON> et al. '17", "paper_url": "https://arxiv.org/pdf/1702.03814.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "87.5", "% Train Accuracy": "90.7", "Parameters": "2.0m"}, "model_links": [], "model_name": "300D re-read LSTM", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '16", "paper_url": "https://www.aclweb.org/anthology/C/C16/C16-1270.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.0", "% Train Accuracy": "91.2", "Parameters": "4.4m"}, "model_links": [], "model_name": "448D Densely Interactive Inference Network (DIIN, code)", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '17", "paper_url": "https://arxiv.org/pdf/1709.04348.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.1", "% Train Accuracy": "88.5", "Parameters": "22m"}, "model_links": [], "model_name": "Biattentive Classification Network + CoVe + Char", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON> et al. '17", "paper_url": "https://arxiv.org/pdf/1708.00107.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.3", "% Train Accuracy": "94.5", "Parameters": "14m"}, "model_links": [], "model_name": "150D Multiway Attention Network", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON> et al. '18", "paper_url": "https://www.ijcai.org/proceedings/2018/0613.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.5", "% Train Accuracy": "93.3", "Parameters": "3.5m"}, "model_links": [], "model_name": "Stochastic Answer Network", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1804.07888.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.5", "% Train Accuracy": "94.1", "Parameters": "7.5m"}, "model_links": [], "model_name": "450D DR-BiLSTM", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1802.05577.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.5", "% Train Accuracy": "89.8", "Parameters": "4.7m"}, "model_links": [], "model_name": "300D CAFE", "paper_date": null, "paper_title": "<PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1801.00102.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.6", "% Train Accuracy": "94.1", "Parameters": "4.3m"}, "model_links": [], "model_name": "KIM", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '17", "paper_url": "https://arxiv.org/pdf/1711.04289.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.6", "% Train Accuracy": "93.5", "Parameters": "7.7m"}, "model_links": [], "model_name": "600D ESIM + 300D Syntactic TreeLSTM", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '16", "paper_url": "http://arxiv.org/pdf/1609.06038v3.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.7", "% Train Accuracy": "91.6", "Parameters": "8.0m"}, "model_links": [], "model_name": "ESIM + ELMo", "paper_date": null, "paper_title": "<PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1802.05365.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.8", "% Train Accuracy": "95.4", "Parameters": "9.2m"}, "model_links": [], "model_name": "300D DMAN", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '18", "paper_url": "http://aclweb.org/anthology/P18-1091"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.8", "% Train Accuracy": "93.2", "Parameters": "6.4m"}, "model_links": [], "model_name": "BiMPM Ensemble", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON> et al. '17", "paper_url": "https://arxiv.org/pdf/1702.03814.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.9", "% Train Accuracy": "92.3", "Parameters": "17m"}, "model_links": [], "model_name": "448D Densely Interactive Inference Network (DIIN, code) Ensemble", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '17", "paper_url": "https://arxiv.org/pdf/1709.04348.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "88.9", "% Train Accuracy": "93.1", "Parameters": "6.7m"}, "model_links": [], "model_name": "Densely-Connected Recurrent and Co-Attentive Network", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1805.11360.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "89.1", "% Train Accuracy": "89.1", "Parameters": "6.1m"}, "model_links": [], "model_name": "SLRC", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1809.02794.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "89.1", "% Train Accuracy": "93.6", "Parameters": "43m"}, "model_links": [], "model_name": "KIM Ensemble", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '17", "paper_url": "https://arxiv.org/pdf/1711.04289.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "89.3", "% Train Accuracy": "94.8", "Parameters": "45m"}, "model_links": [], "model_name": "450D DR-BiLSTM Ensemble", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1802.05577.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "89.3", "% Train Accuracy": "92.1", "Parameters": "40m"}, "model_links": [], "model_name": "ESIM + ELMo Ensemble", "paper_date": null, "paper_title": "<PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1802.05365.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "89.3", "% Train Accuracy": "92.5", "Parameters": "17.5m"}, "model_links": [], "model_name": "300D CAFE Ensemble", "paper_date": null, "paper_title": "<PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1801.00102.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "89.4", "% Train Accuracy": "95.5", "Parameters": "58m"}, "model_links": [], "model_name": "150D Multiway Attention Network Ensemble", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON> et al. '18", "paper_url": "https://www.ijcai.org/proceedings/2018/0613.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "89.6", "% Train Accuracy": "96.1", "Parameters": "79m"}, "model_links": [], "model_name": "300D DMAN Ensemble", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '18", "paper_url": "http://aclweb.org/anthology/P18-1091"}, {"code_links": [], "metrics": {"% Test Accuracy": "89.9", "% Train Accuracy": "96.6", "Parameters": "85m"}, "model_links": [], "model_name": "Fine-Tuned LM-Pretrained Transformer", "paper_date": null, "paper_title": "Improving Language Understanding by Generative Pre-Training", "paper_url": "https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/language-unsupervised/language_understanding_paper.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "90.1", "% Train Accuracy": "95.0", "Parameters": "53.3m"}, "model_links": [], "model_name": "Densely-Connected Recurrent and Co-Attentive Network Ensemble", "paper_date": null, "paper_title": "<PERSON><PERSON><PERSON><PERSON> et al. '18", "paper_url": "https://arxiv.org/pdf/1805.11360.pdf"}, {"code_links": [], "metrics": {"% Test Accuracy": "91.1", "% Train Accuracy": "96.8", "Parameters": "110m"}, "model_links": [], "model_name": "MT-DNN", "paper_date": null, "paper_title": "<PERSON><PERSON> et al. '19", "paper_url": "https://arxiv.org/pdf/1901.11504.pdf"}]}, "subdatasets": []}], "description": "", "source_link": {"title": "The Stanford Natural Language Inference (SNLI) Corpus", "url": "https://nlp.stanford.edu/projects/snli/"}, "subtasks": [], "synonyms": [], "task": "Natural Language Inference"}]